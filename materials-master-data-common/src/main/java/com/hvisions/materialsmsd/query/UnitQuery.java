package com.hvisions.materialsmsd.query;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <p>Title: UnitQuery</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/8/31</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Getter
@Setter
@ToString
public class UnitQuery extends PageInfo {
    /**
     * 国际符号
     */
    @NotBlank(message = "单位编码不能为空")
    @ApiModelProperty(value = "国际符号", required = true)
    private String symbol;
    /**
     * 中文符号
     */
    @NotBlank(message = "单位中文描述不能为空")
    @ApiModelProperty(value = "中文符号", required = true)
    private String description;
}









