package com.hvisions.materialsmsd.materials.enums;

/**
 * <p>Title: MaterialPropertyDataType</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum MaterialPropertyDataType {

    //数据类型
    STRING(1),
    LONG(2),
    FLOAT(3);
    int code;

    MaterialPropertyDataType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static MaterialPropertyDataType getByCode(int code) {
        for (MaterialPropertyDataType value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return MaterialPropertyDataType.STRING;
    }
}
