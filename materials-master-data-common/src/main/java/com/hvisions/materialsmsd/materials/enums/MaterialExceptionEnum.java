package com.hvisions.materialsmsd.materials.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: MaterialExceptionEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/9/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum MaterialExceptionEnum implements BaseErrorCode {
    /**
     * 物料信息枚举
     */
    DEMO_EXCEPTION_ENUM(51001),
    MATERIAL_NOT_EXISTS(51002),
    MATERIAL_CODE_EIGENVALUE_ERROR(51010),
    UOM_ERROR(510003),
    UOM_NAME_ERROR(51004),
    MATERIAL_TYPE_ERROR(51005),
    MATERIAL_TYPE_NAME_ERROR(51006),
    MATERIAL_BOM_RELEVANCY(51007),
    MATERIAL_BOM_ITEM_RELEVANCY(51008),
    MATERIAL_SUBSTITUTE_ITEM_RELEVANCY(51009),
    BOM_STATUS_ARCHIVED(51010),
    MATERIAL_GROUP_IS_USE(51011),
    UOM_IS_USE(51012),
    BOM_MATERIAL_IS_EXISTS(51013),
    BOM_CODE_OR_VERSION_NOT_NULL(51014),
    REGEX_ERROR_MATERIAL_NOT_FIND(51015),
    MATERIAL_HAVE_BOM(51016),
    MATERIAL_PARSE_NOT_SET(51017),
    PARSE_SETTING_ERROR(51018),
    NOT_BOM(51019), BOM_NOT_FIND(51020),
    FIXED_TYPE_CANNOT_BE_DELETED(51021),

    MATERIAL_TYPE_NOT_NULL(51022), MATERIAL_TYPE_IS_USE(51023),
    MATERIAL_TYPE_HAVE_DATA(51024), CHOOSE_BOM(51025)
    ,BOM_STATE_NOT_TAKEEFFECT(51026);
    private Integer code;

    MaterialExceptionEnum(Integer code) {
        this.code = code;
    }


    @Override
    public String getMessage() {
        return this.toString();
    }
}
