package com.hvisions.materialsmsd.materials.dto;

import com.hvisions.materialsmsd.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
//mdm 原辅料数据推送
@EqualsAndHashCode(callSuper = true)
@Data
public class MDMPushRowMaterialDTO extends SysBaseDTO {
    @ApiModelProperty("传输原辅物料对象")
    private @Valid PkgMatQueryDto pkgJmsRawPackMaterials;
}
