package com.hvisions.materialsmsd.bom.dto;

import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.materialsmsd.MaterialObjectEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <p>Title: UnitDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class UnitDTO implements IObjectType {
    /**
     * 主键
     */

    @ApiModelProperty(value = "主键", example = "1")
    protected Integer id;

    /**
     * 国际符号
     */
    @NotBlank(message = "单位编码不能为空")
    @ApiModelProperty(value = "国际符号", required = true)
    private String symbol;
    /**
     * 中文符号
     */
    @NotBlank(message = "单位中文描述不能为空")
    @ApiModelProperty(value = "中文符号", required = true)
    private String description;


    @Override
    public Integer getObjectType() {
        return MaterialObjectEnum.UNIT_DTO.getCode();
    }
}
