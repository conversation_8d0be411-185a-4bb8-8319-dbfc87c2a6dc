package com.hvisions.materialsmsd.bom.dto;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.materialsmsd.MaterialObjectEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: BomQueryDTO</p >
 * <p>Description: 分页查询使用的DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "bom查询对象")
public class BomQueryDTO extends PageInfo implements IObjectType {

    /**
     * bom名称
     */
    @ApiModelProperty(value = "bom名称")
    private String bomName;
    /**
     * bom编码
     */
    @ApiModelProperty(value = "bom编码")
    private String bomCode;
    /**
     * bom版本
     */
    @ApiModelProperty(value = "bom版本")
    private String bomVersions;
    /**
     * bom状态
     */
    @ApiModelProperty(value = "bom状态")
    private Integer bomStatus;

    @Override
    public Integer getObjectType() {
        return MaterialObjectEnum.BOM_QUERY_DTO.getCode();
    }
}
