package com.hvisions.powder.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum BatchStateEnum {
    // 状态枚举
    TAKE_EFFECT("0", "生效"),
    FILE("1", "归档"),
    ;

    private String code;
    private String name;

    BatchStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
