package com.hvisions.powder.dto.qudou.demand.issue.order.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @描述:
 * @作者: 刘文勇
 * @日期: 2024/7/12 16:00
 * @版本 1.0
 */
@Data
public class IssueWeightQuerySummary {

    @ApiModelProperty("发放日期起点")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    @ApiModelProperty("发放日期终点")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

}
