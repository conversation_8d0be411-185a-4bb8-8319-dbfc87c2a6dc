package com.hvisions.powder.dto.qudou.demand;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.DateFormat;
import java.util.Date;

/**
 * @描述:
 * @作者: 刘文勇
 * @日期: 2024/6/4 9:52
 * @版本 1.0
 */
@Data
public class PackTaskQueryDTO extends PageInfo {

    @ApiModelProperty("基地id")
    private Integer baseId;

    @ApiModelProperty("使用时间起点")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("使用时间终点")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}
