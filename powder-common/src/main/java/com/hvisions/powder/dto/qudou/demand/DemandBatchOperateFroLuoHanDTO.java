package com.hvisions.powder.dto.qudou.demand;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description: 曲粉需求操作dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "曲粉需求操作dto")
public class DemandBatchOperateFroLuoHanDTO {

    @ApiModelProperty("需求单id")
    private Integer id;

    @ApiModelProperty("中心id")
    private Integer centerId;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("基地Id")
    private Integer baseId;

    @ApiModelProperty("基地名称")
    private String baseName;

    @ApiModelProperty("使用日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date useTime;

    @ApiModelProperty("是否报送")
    private Boolean isSubmit;

    @ApiModelProperty("跨详情")
    List<DemandBatchOperateDetailForLuoHanDTO> demandDetailReqList;

}
