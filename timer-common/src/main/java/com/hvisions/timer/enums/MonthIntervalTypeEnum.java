package com.hvisions.timer.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: MonthIntervalTypeEnum</p>
 * <p>Description: 每月的重复类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum MonthIntervalTypeEnum implements IKeyValueObject {

    //参数用途
    DAYS(1, "自定义日"),
    SINGLE_DAY(2, "单独一天"),
    ;

    MonthIntervalTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}

    
    
    
    
    
    
    
    
