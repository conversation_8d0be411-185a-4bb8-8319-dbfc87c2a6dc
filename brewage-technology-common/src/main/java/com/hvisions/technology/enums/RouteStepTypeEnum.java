package com.hvisions.technology.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: RouteStepTypeEnum</p>
 * <p>Description: 工艺步骤类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum RouteStepTypeEnum implements IKeyValueObject {
    //工艺步骤类型
    OPERATION(1, "工艺操作"),
    ROUTE(2, "工艺路线"),
    ;

    RouteStepTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}

    
    
    
    
    
    
    
    
