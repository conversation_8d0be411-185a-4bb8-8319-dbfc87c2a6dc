package com.hvisions.technology.enums;

/**
 * 调用日志相关
 */
public enum LogCaptureEnum {
    FEEDING_FORMULA_SAP_TO_MES("brewage-technology-server", "SAP", "/base/feeding/formula/syncSapToMes", "SAP调用MES", "投料配方-SAP同步到MES"),
    ROUTE_DISABLE_IOT_TO_MES("brewage-technology-server", "IOT", "/iot/send/iotToMesRouteDisable", "IOT调用MES","工艺禁用-iot传递数据到mes"),
    ROUTE_PROCESS_ALARM_IOT_TO_MES("brewage-technology-server", "IOT", "/iot/send/iotToMesRouteProcessAlarm", "IOT调用MES","工艺参数告警-iot传递数据到mes"),
    ;

    private String logModular;
    private String logInvocation;
    private String location;
    private String controllerName;
    private String methodName;

    public String getLogModular() {
        return logModular;
    }

    public String getLogInvocation() {
        return logInvocation;
    }

    public String getLocation() {
        return location;
    }

    public String getControllerName() {
        return controllerName;
    }

    public String getMethodName() {
        return methodName;
    }

    LogCaptureEnum(String logModular, String logInvocation, String location, String controllerName, String methodName) {
        this.logModular = logModular;
        this.logInvocation = logInvocation;
        this.location = location;
        this.controllerName = controllerName;
        this.methodName = methodName;
    }
}
