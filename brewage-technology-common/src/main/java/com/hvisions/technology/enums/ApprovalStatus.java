package com.hvisions.technology.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ApprovalEnum
 * @description: 审批相关
 * @date 2025/6/12 9:03
 */
public enum ApprovalStatus {
    NOT_STARTED(0, "未发起", "审批流程尚未发起"),
    IN_REVIEW(1, "审批中", "审批流程进行中"),
    REJECTED(2, "驳回", "审批被驳回"),
    APPROVED(3, "通过", "审批通过"),
    CLOSED(4, "关闭", "审批流程手动关闭"),
    WITHDRAWN(5, "撤回", "提交人撤回审批"),
    NOT_EXECUTE(6, "未审批", "提交人未审批");

    private final int code;
    private final String name;
    private final String description;

    ApprovalStatus(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    // --- Getter 方法 ---
    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}
