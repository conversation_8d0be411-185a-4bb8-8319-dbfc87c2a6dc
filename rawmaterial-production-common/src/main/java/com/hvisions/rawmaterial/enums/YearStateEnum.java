package com.hvisions.rawmaterial.enums;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:19
 */
public enum YearStateEnum {
    // 状态枚举
    NEW("0", "执行中"),
    FINISH("1", "已完成"),
    FILE("2", "归档"),
    ;

    private String code;
    private String name;

    YearStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
