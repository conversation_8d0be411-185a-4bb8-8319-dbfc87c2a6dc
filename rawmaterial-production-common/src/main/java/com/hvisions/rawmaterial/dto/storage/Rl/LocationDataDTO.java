package com.hvisions.rawmaterial.dto.storage.Rl;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 库位存储物料详情dto
 *
 * <AUTHOR>
 * @date 2022/2/28 17:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "库位存储物料详情dto")
public class LocationDataDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "库位id")
    private Integer storageId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
