package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "车辆运输管理配置信息")
public class VehicleTransportDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "创建人id")
    private Integer creatorId;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "车辆状态(正常 1     检修 2)")
    private Integer state;

    @ApiModelProperty(value = "车辆状态")
    private String stateName;
}
