package com.hvisions.rawmaterial.dto.production.husk.Inspection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "蒸糠工艺巡检项目详细修改Dto")
public class HuskInspectionDetailUpdateDTO {

    @ApiModelProperty(value = "质量巡检详细id")
    private Integer id;

    /**
     * 质量巡检id
     */
    @ApiModelProperty(value = "质量巡检id")
    private Integer inspectionId;

    /**
     * 检验项目id
     */
    @ApiModelProperty(value = "检验项目id")
    private Integer itemId;

    /**
     * 巡检项目编码
     */
    @ApiModelProperty(value = "巡检项目编码")
    private String code;

    /**
     * 巡检项目名称
     */
    @ApiModelProperty(value = "巡检项目名称")
    private String name;

    /**
     * 巡检标准要求
     */
    @ApiModelProperty(value = "巡检标准要求")
    private String standard;

    /**
     * 检查结论
     */
    @ApiModelProperty(value = "检查结论")
    private String detailResult;

    /**
     * 异常描述
     */
    @ApiModelProperty(value = "异常描述")
    private String abnormalDescription;

    /**
     * 改进措施
     */
    @ApiModelProperty(value = "改进措施")
    private String improvementMeasures;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
