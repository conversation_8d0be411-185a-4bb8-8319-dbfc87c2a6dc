package com.hvisions.rawmaterial.dto.storage.Rl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @description 库内物料数据
 *
 * <AUTHOR>
 * @date 2022/2/28 17:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "库内物料数据DTO")
public class StorageMaterialDataDTO {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

}
