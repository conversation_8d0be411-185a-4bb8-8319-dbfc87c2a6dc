package com.hvisions.quality.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.quality.dto.quality.inspection.LaunchInspectionDTO;
import com.hvisions.quality.dto.quality.inspection.data.InspectionDataDetailDTO;
import com.hvisions.quality.dto.quality.inspection.production.infrared.InspectionDataDTO;
import com.hvisions.quality.dto.quality.inspection.task.InspectionTaskDetailDTO;
import com.hvisions.quality.dto.quality.inspection.task.InspectionTaskInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: InspectionTaskClient
 * @author: yyy
 * @time: 2023/12/12 10:11
 */
@FeignClient(name = "quality-server", path = "/inspection/task", fallbackFactory = InspectionTaskClientFallBack.class)
public interface InspectionTaskClient {

    // 根据关联单号获取检验任务详情
    @GetMapping({"/getInspectionTaskDetailByAssociate/{associatedDocument}"})
    ResultVO<InspectionTaskInfoDTO> getInspectionTaskDetailByAssociate(@PathVariable String associatedDocument);

    // 发起报检
    @PostMapping({"/launch"})
    ResultVO<String> launchInspection(@Valid @RequestBody LaunchInspectionDTO launchInspectionDTO);

    // 根据关联单号关闭质检任务
    @DeleteMapping({"/inspectionInfo/delete/{associatedDocument}"})
    ResultVO<Integer> deleteInspection(@PathVariable String associatedDocument);

    // 特殊退货同步质检信息
    @GetMapping({"/specialReturnsUpdateTask/{inspectionId}"})
    ResultVO<Integer> specialReturnsUpdateTask(@PathVariable Integer inspectionId);

    // 根据检验任务id获取检验数据
    @GetMapping({"/getInspectionData/{inspectionId}"})
    ResultVO<List<InspectionDataDetailDTO>> getInspectionData(@PathVariable Integer inspectionId);

    // 获取全部检验单和预处理检验数据
    @GetMapping({"/getAllInspectionData"})
    ResultVO<List<InspectionDataDTO>> getAllInspectionData();

    // 获取检验任务详情和检验数据列表
    @GetMapping({"/getDetailByAssociatedDocument/{associatedDocument}"})
    ResultVO<List<InspectionTaskDetailDTO>> getInspectionTaskDetailByAssociatedDocument(@PathVariable String associatedDocument);

    @ApiOperation(value = "粉碎工单绑定当天检验任务")
    @RequestMapping(value = "/bindInspectionTask/{orderNo}/{materialId}", method = RequestMethod.GET)
    ResultVO<Integer> bindInspectionTask(@PathVariable String orderNo, @PathVariable Integer materialId);
}
