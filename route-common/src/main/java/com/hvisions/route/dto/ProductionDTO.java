package com.hvisions.route.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: ProductionDTO</p >
 * <p>Description: 产品列表中展示基本工艺信息的DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/9/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@ApiModel(description = "产品列表中绑定产品工艺的DTO")
public class ProductionDTO {
    /**
     *产品id
     */
    @ApiModelProperty(value = "产品id")
    private Integer productId;

    /**
     * 产品工艺信息
     */
    @ApiModelProperty(value = "工艺信息")
    private List<RouteDTO> productRouteDTOS;



}
