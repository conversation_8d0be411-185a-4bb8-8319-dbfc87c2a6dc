package com.hvisions.route.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ParameterActiveEnum</p >
 * <p>Description: 参数生效状态</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/11</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum ActiveEnum implements IKeyValueObject {
    //参数用途
    NEW(0, "新建"),
    ACTIVE(1, "生效"),
    INVALID(2, "归档");

    private int code;
    private String name;

    ActiveEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public static ActiveEnum valueOf(int code) {
        switch (code) {
            case 0:
                return NEW;
            case 1:
                return ACTIVE;
            case 2:
                return INVALID;
            default:
                return INVALID;
        }
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
