package com.hvisions.route.enums;

import com.hvisions.common.interfaces.IKeyValueObject;
import com.hvisions.common.utils.EnumUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: ParameterTypeEnum</p>
 * <p>Description: 参数类型枚举
 * 数据类型，1.整数，2.浮点数，3.字符串，4.布尔值，5.日期，6.时间，7.精确小数，</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum ParameterDataUsage implements IKeyValueObject {
    //参数用途
    PRODUCTION(0, "生产"),
    QUALITY(1, "质量"),
    TECHNIQUE(3, "工艺"),
    //如果需要增加其他类型。只需要设置为2^n-1 数值即可。例如“其他”类型。设置为2^3-1=7。再其他的就是2^4-1=15
    ;

    ParameterDataUsage(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    /**
     * 根据数值计算他是什么参数，用字符串标识
     * @param code 实体标识类型的值
     * @return  字符串表示的用户
     */
    public static String stringValue(int code){
        List<String> result = new ArrayList<>();
        Map<Integer, String> map = EnumUtil.enumToMap(ParameterDataUsage.class);
        map.forEach((k,v)->{
            //逻辑与运算
            if(((code+1)&(k+1))>0){
                result.add(v);
            }
        });
        return String.join("-",result);
    }

    /**
     * 根据用途的字符串。返回应该保存的数据值,使用的flag算法。如果是生产
     * @param usageCode 用途字符串
     * @return 应该对应的数据值
     */
    public static int valueOfString(String usageCode){
        List<Integer> result = new ArrayList<>();
        String[] split = usageCode.split("-");
        Map<Integer, String> map = EnumUtil.enumToMap(ParameterDataUsage.class);
        map.forEach((k,v)->{
            if(Arrays.asList(split).contains(v)){
                result.add(k);
            }
        });
        if(result.size()==0){
            return 0;
        }
        return result.size()+result.stream().mapToInt(t->t).sum()-1;
    }



    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}

    
    
    
    
    
    
    
    
