package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.vo.MaterialRequirePlanBaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/1 16:41
 */
@Data
public class ProductionMaterialVO extends MaterialRequirePlanBaseVO {
    @ApiModelProperty("中心年度计划产量")
    BigDecimal planProduce;
    @ApiModelProperty("具体物料  需求量")
    List<PlanMaterialQuantityVO> planMaterialQuantityVOList;
}
