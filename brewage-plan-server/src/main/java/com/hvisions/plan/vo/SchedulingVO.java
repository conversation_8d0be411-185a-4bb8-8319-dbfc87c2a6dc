/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname SchedulingVO
 * @description 排程计划视图实体类
 * @date 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("SchedulingVO对象")
public class SchedulingVO extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("酿酒中心")
    private String centerNames;

    @ApiModelProperty("车间名称")
    private String locationNames;

    @ApiModelProperty("计划编码")
    private String code;

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("计划时间")
    private String planTime;

    @ApiModelProperty("排程开始时间")
    private LocalDate beginTime;

    @ApiModelProperty("排程结束时间")
    private LocalDate endTime;

    @ApiModelProperty("计划状态;0-新建、1-执行、2-已完成、3-存档")
    private String planState;

    @ApiModelProperty("审批状态;0-待审批、1-审批中、2-审批完成")
    private String auditState;

    @ApiModelProperty("审核人姓名")
    private String auditorName;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("计划投粮")
    private BigDecimal feeding;

    @ApiModelProperty("计划耗粮")
    private BigDecimal consumption;

    @ApiModelProperty("计划基酒产出")
    private BigDecimal winOutput;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("生产基地，逗号分割")
    private String productionBaseIds;

    @ApiModelProperty("酿酒中心，逗号分割")
    private String centerIds;

    @ApiModelProperty("车间名称，逗号分割")
    private String locationIds;

    @ApiModelProperty("多选  糟源ID列表")
    private String zenIds;
}
