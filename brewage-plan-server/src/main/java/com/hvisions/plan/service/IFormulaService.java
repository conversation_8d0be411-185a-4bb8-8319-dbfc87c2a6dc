package com.hvisions.plan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.plan.dto.FormulaQueryReq;
import com.hvisions.plan.dto.FormulaReq;
import com.hvisions.plan.dto.PreCheckResp;
import com.hvisions.plan.entity.Formula;
import com.hvisions.plan.vo.FormulaAllInfo;
import com.hvisions.plan.vo.FormulaDetailVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 糟源配方 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface IFormulaService extends IService<Formula> {
    /**
     * 生效
     *
     * @param id
     */
    void validData(Integer id);

    /**
     * 归档
     *
     * @param id
     */
    void invalidData(Integer id);

    /**
     * 查询配方明细
     *
     * @param formulaId
     * @return
     */
    List<FormulaDetailVO> getFormulaDetailsByFormulaId(Integer formulaId);

    /**
     * 拷贝配方
     *
     * @param req
     */
    void copy(FormulaReq req);

    /**
     * 根据糟源编码查询配方
     *
     * @param vinasseCode
     * @return
     */
    List<FormulaDetailVO> getFormulaDetailsByPlantAndFormula(Integer plantId, String centerId, String vinasseCode, LocalDate ldt);

    List<FormulaAllInfo> listAllFormulaInfo();

    List<Formula> getFormulaByWorking();

//    List<Formula> getFormulaByWorkingByCenterId(Integer centerId);

    List<Formula> getValidFormulaByWorkingByCenterId(Integer centerId);


    List<Integer> listFormulaByCenter(FormulaQueryReq req);

    PreCheckResp checkSameVinasseAndPeriod(Integer formulaId);
}
