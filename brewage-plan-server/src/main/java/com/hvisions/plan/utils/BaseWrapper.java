package com.hvisions.plan.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@Component
public class BaseWrapper {
    public <E, V> V convert(E entity, Class<V> vClass, String... ignoreProps) {
        return CopyUtil.simpleCopy(entity, vClass, ignoreProps);
    }

    public <E, V> V convert4List(E entity, Class<V> vClass, String... ignoreProps) {
        return CopyUtil.simpleCopy(entity, vClass, ignoreProps);
    }

    public <E, V> List<V> convertToList(List<E> list, Class<V> vClass, String... ignoreProps) {
        return (List)list.stream().map((entity) -> {
            return this.convert4List(entity, vClass, ignoreProps);
        }).collect(Collectors.toList());
    }

    public <E, V> Page<V> convertToPage(IPage<E> pages, Class<V> vClass, String... ignoreProps) {
        List<V> records = this.convertToList(pages.getRecords(), vClass, ignoreProps);
        Page<V> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    public BaseWrapper() {
    }
}
