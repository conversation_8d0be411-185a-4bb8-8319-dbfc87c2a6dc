/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.dto;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @classname SchedulingReq
 * @description 排程计划数据传输对象实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("Scheduling数据传输对象实体类")
public class SchedulingReq extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("计划编码")
    private String code;

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("计划时间")
    private String planTime;

    @ApiModelProperty("排程开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginTime;

    @ApiModelProperty("排程结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty("计划状态;0-新建、1-执行、2-已完成、3-存档")
    private Boolean planState;

    @ApiModelProperty("审批状态;0-待审批、1-审批中、2-审批完成")
    private Boolean auditState;

    @ApiModelProperty("计划投粮")
    private BigDecimal feeding;

    @ApiModelProperty("计划耗粮")
    private BigDecimal consumption;

    @ApiModelProperty("计划基酒产出")
    private BigDecimal winOutput;

    @ApiModelProperty("生产基地，逗号分割，按照数值大小排序")
    private String productionBaseIds;

    @ApiModelProperty("酿酒中心，逗号分割，按照数值大小排序")
    private String centerIds;

    @ApiModelProperty("车间名称，逗号分割，按照数值大小排序")
    private String locationIds;

    @ApiModelProperty("多选  糟源ID列表")
    private List<Integer> zenIds;

    @ApiModelProperty("来源类型")
    private Integer type;

    @ApiModelProperty("排次信息")
    List<SchedulingRowReq> schedulingRowReqList;
}