package com.hvisions.plan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/11 14:50
 */
@Data
public class MonthPlanCreateSchedulingReq {

    @ApiModelProperty("第一步参数")
    private SchedulingReq schedulingReq;

    @ApiModelProperty("第二步参数")
    private List<SchedulingArrangeReq> schedulingArrangeReqs;

    @ApiModelProperty("提交审批参数")
    private AuditAddReq auditAddReq;
}
