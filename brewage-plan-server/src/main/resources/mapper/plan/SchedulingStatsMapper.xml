<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.plan.dao.SchedulingStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.plan.entity.SchedulingStats">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
        <result column="scheduling_id" property="schedulingId"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="type" property="type"/>
        <result column="work_days" property="workDays"/>
        <result column="production_days" property="productionDays"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        creator_id, updater_id, site_num, deleted, scheduling_id, year, month, type, work_days
    </sql>
    <select id="getBySchedulingId" resultMap="BaseResultMap">
        select id,
        creator_id,
        create_time,
        update_time,
        updater_id,
        deleted,
        site_num,
        scheduling_id,
        year,
        month,
        type,
        work_days,
        production_days
        from brewage_plan.t_pp_scheduling_stats
        where deleted = 0
        and scheduling_id = #{scheduleId}
        <if test="billType != null">
            and type = #{billType}
        </if>
        order by year, month, id
    </select>
    <select id="getStatsListBySchedulingId" resultType="com.hvisions.plan.dto.SchedulingStatsDetailDto">
        select tpss.year,
               tpss.month,
               tpss.work_days,
               tpss.type billType,
               tpssd.stats_id,
               tpssd.material_id,
               tpssd.material_code,
               tpssd.material_name,
               tpssd.type,
               tpssd.unit,
               tpssd.total_quality,
               tpss.month_begin_date,
               tpss.month_end_date
        from brewage_plan.t_pp_scheduling_stats tpss
                 left join brewage_plan.t_pp_scheduling_stats_detail tpssd on tpss.id = tpssd.stats_id
        where tpss.deleted = 0
          and tpssd.deleted = 0
          and tpss.scheduling_id = #{schedulingId}
    </select>

</mapper>
