#feign 熔断开启
feign:
  hystrix:
    enabled: false
spring:
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: 1001
  redis:
    host: ************
    port: 6379
    password: 1001
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    name: gateway-license
  profiles:
    active: dev
  messages:
    basename: i18n/messages
    encoding: utf-8
    cache-seconds: -1
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 10MB
  jmx:
    enabled: false
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 300000
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
h-visions:
  swagger:
    # 如果为false或者没有此属性。swagger界面将不会加载
    enable: false
  gateway:
    auth-type: default
    #    规定是否验证请求，如果开启，则需要登录后访问，并且开启后无法看到所有的swagger地址。
    valid: true
    #跳过验证的uri,可用逗号分割多个uri，请仔细检查添加的数据格式
    skip: /file/downloadFile
    # token过期时间（分钟）
    token-expire: 720
    casUrl: http://localhost:9001
    casClientId: hipermatic
    casLogoutQueue: hipermatic
    allowCredentials: true
    allowedOrigin:
      - "*"
    allowedHeader:
      - "*"
    maxAge: 7200
    allowedMethod:
      - "*"
    corsPath: "/**"
    ldap:
      suffix: "@pg.com"
    # 增加配置。可以根据实际情况设置授权文件的存储地址
    license-path:
    china-mobile:
      apId: xxx
      secretKey: xx
      url: http://************:1992/sms/norsubmit
      ecName: xxx
      sign: xxxx
      codeLength: 6
      template: 重置验证码为:%s,请勿转给他人
    validCode:
      # chinaMobile 使用中国电信的短信服务作为验证方式
      # default是默认验证，（无操作）
      type: default
  service-name: 网关服务
zuul:
  sensitive-headers:
  max:
    host:
      connections: 500
  host:
    socket-timeout-millis: 60000
    connect-timeout-millis: 60000
server:
  port: 9000
info:
  build:
    artifact: @project.artifactId@
    version: @project.version@
    server-name: ${h-visions.service-name}
