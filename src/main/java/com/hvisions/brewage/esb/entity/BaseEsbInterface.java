package com.hvisions.brewage.esb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
    * esb接口相关
    */
@ApiModel(value="esb接口相关")
@Data
@TableName(value = "brewage_base.base_esb_interface")
public class BaseEsbInterface {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 接口编号
     */
    @TableField(value = "interface_code")
    @ApiModelProperty(value="接口编号")
    private String interfaceCode;

    /**
     * 业务系统
     */
    @TableField(value = "sevlevel")
    @ApiModelProperty(value="业务系统")
    private String sevlevel;

    /**
     * 业务系统名称
     */
    @TableField(value = "sevlevel_name")
    @ApiModelProperty(value="业务系统名称")
    private String sevlevelName;

    /**
     * 业务服务名
     */
    @TableField(value = "service_name")
    @ApiModelProperty(value="业务服务名")
    private String serviceName;

    /**
     * 控制器名称
     */
    @TableField(value = "controller_name")
    @ApiModelProperty(value="控制器名称")
    private String controllerName;

    /**
     * 接口名称
     */
    @TableField(value = "interface_name")
    @ApiModelProperty(value="接口名称")
    private String interfaceName;

    /**
     * 接口地址
     */
    @TableField(value = "interface_url")
    @ApiModelProperty(value="接口地址")
    private String interfaceUrl;
}