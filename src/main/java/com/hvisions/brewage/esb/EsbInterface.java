package com.hvisions.brewage.esb;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.brewage.esb.dto.EsbRequestDTO;
import com.hvisions.brewage.esb.entity.BaseEsbInterface;
import com.hvisions.brewage.esb.service.BaseEsbInterfaceService;
import com.hvisions.brewage.esb.vo.EsbResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: Esbin
 * @description:
 * @date 2025/7/29 14:33
 */
@Component
@Slf4j
public class EsbInterface<T> {

    @Resource
    private BaseEsbInterfaceService baseEsbInterfaceService;

    /**
     * POST请求发送到esb
     *
     * @param interfaceCode
     * @param data
     * @return
     */
    public EsbResultVO sendPost(String interfaceCode, T data) {
        log.info("收到业务传递参数，参数内容：{}",data);

        //获取对应的配置
        BaseEsbInterface baseEsbInterface = baseEsbInterfaceService.findBaseEsbInterfaceOne(interfaceCode);

        EsbRequestDTO esbRequestDTO = new EsbRequestDTO();
        EsbRequestDTO.Request request = esbRequestDTO.getRequest();
        request.setList(data);
        EsbRequestDTO.Head head = request.getHead();
        head.setSevlevel(baseEsbInterface.getSevlevel());

        String toJSONString = JSONObject.toJSONString(esbRequestDTO);
        JSONObject jsonObject = JSONObject.parseObject(toJSONString);

        log.info("调用ESB接口，传入参数：接口编号:{},参数：{}",interfaceCode,jsonObject);

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity(jsonObject, headers);
        ResponseEntity<String> requestPost = null;
        try {
            requestPost = restTemplate.postForEntity(baseEsbInterface.getInterfaceUrl(), httpEntity, String.class);
        } catch (RestClientException e) {
            log.info("调用ESB接口-调用接口异常，传入参数：接口编号:{},参数：{},收到返回参数：{}",interfaceCode,jsonObject,e.getMessage());

            JSONObject object =new JSONObject();
            object.put("code",500);
            object.put("message","调用ESB接口异常：接口编号="+interfaceCode+",原异常因="+e.getMessage());
            //组装返回的数据
            EsbResultVO esbResultVO = new EsbResultVO();
            esbResultVO.setRequestParam(toJSONString);
            esbResultVO.setResultVO(JSONObject.toJSONString(object));

            return esbResultVO;
        }

        log.info("调用ESB接口，传入参数：接口编号:{},参数：{},收到返回参数：{}",interfaceCode,jsonObject,JSONObject.toJSONString(requestPost.getBody()));
        //组装返回的数据
        EsbResultVO esbResultVO = new EsbResultVO();
        esbResultVO.setRequestParam(toJSONString);
        esbResultVO.setResultVO(requestPost.getBody());

        return esbResultVO;
    }
}
