/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.brewage.plan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @classname MonthPlanVinasse
 * @description 月度计划糟源数据实体类
 * <AUTHOR>
 * @date 2022-03-29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_pp_month_plan_vinasse")
@ApiModel("MonthPlanVinasse对象")
public class MonthPlanVinasse extends SysBase {

    @ApiModelProperty("月度计划id")
    private Integer monthPlanId;

    @ApiModelProperty("糟源代码")
    private String vinasseCode;

    @ApiModelProperty("糟源数据")
    private BigDecimal vinasseData;

    @ApiModelProperty("是否扎帐期;0-否、1-是")
    private String billType;

}