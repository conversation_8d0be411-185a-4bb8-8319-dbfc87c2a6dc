package com.hvisions.brewage.feign.plan;

import com.hvisions.brewage.mkwine.req.MonthPlanMaterialReq;
import com.hvisions.brewage.mkwine.vo.MonthPlanMaterialVO;
import com.hvisions.brewage.plan.entity.*;
import com.hvisions.brewage.plan.vo.FormulaAllInfo;
import com.hvisions.brewage.plan.vo.KojiPharmacologyTemplate;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: BrewagePlanClient
 * @description:
 * @date 2025/2/14 14:38
 */

@FeignClient(name = "brewage-plan-server", path = "/plan/feign", fallbackFactory = BrewagePlanClientFallBack.class)
public interface BrewagePlanClient {


    @ApiOperation("查询VinasseSource列表")
    @RequestMapping(value = "/selectVinasseSourceById",method = RequestMethod.GET)
    ResultVO<VinasseSource> selectVinasseSourceById(@RequestParam("categoryId") Integer categoryId);


    @ApiOperation("获取计划总量")
    @RequestMapping(value = "/selectMonthPlanList",method = RequestMethod.GET)
    ResultVO<List<MonthPlan>> selectMonthPlanList(@RequestParam("locationId") Integer locationId);

    @ApiOperation("获取糟源的计划量-单条")
    @RequestMapping(value = "/selectMonthPlanVinasseOne",method = RequestMethod.POST)
    ResultVO<MonthPlanVinasse> selectMonthPlanVinasseOne(@RequestBody List<MonthPlan> monthPlans);

    @ApiOperation("获取糟源的计划量-批量")
    @RequestMapping(value = "/selectMonthPlanVinasseList",method = RequestMethod.POST)
    ResultVO<List<MonthPlanVinasse>> selectMonthPlanVinasseList(@RequestBody List<MonthPlan> monthPlans);


    @ApiOperation("查询VinasseSource列表-集合")
    @RequestMapping(value = "/selectVinasseSourceList",method = RequestMethod.POST)
    ResultVO<Map<String, Integer>> selectVinasseSourceList(@RequestBody List<String> vinasses);

    @RequestMapping(value = "/getVinasseQuantity",method = RequestMethod.GET)
    ResultVO<BigDecimal> getVinasseQuantity(@RequestParam("centerId") Integer centerId,
                                            @RequestParam("locationId") Integer locationId,
                                            @RequestParam("vinasseCode") String vinasseCode,
                                            @RequestParam("startDate")String startDate,
                                            @RequestParam("endDate") String endDate);

    @RequestMapping(value = "/getVinasseSourceAndCode",method = RequestMethod.GET)
    ResultVO<VinasseSource> getVinasseSourceAndCode(@RequestParam("code") String code);

    @ApiOperation("根据配方去查找该数据的配方明细(高粱、稻壳、回酒、曲粉)")
    @RequestMapping(value = "/getFormulaDetails",method = RequestMethod.GET)
    ResultVO<List<FormulaDetail>> getFormulaDetails(@RequestParam("formulaId") Integer formulaId);

    @ApiOperation("通过中心ID获取配方，默认选则第一个")
    @RequestMapping(value = "/getValidFormulaByWorkingByCenterId",method = RequestMethod.GET)
    ResultVO<List<Formula>> getValidFormulaByWorkingByCenterId(@RequestParam("centerId") Integer centerId);

    @ApiOperation("获取所有配方数据")
    @RequestMapping(value = "/listAllFormulaInfo",method = RequestMethod.GET)
    ResultVO<List<FormulaAllInfo>> listAllFormulaInfo();

    @ApiOperation("糟源信息")
    @RequestMapping(value = "/selectVinasseSourceOne",method = RequestMethod.GET)
    ResultVO<VinasseSource> selectVinasseSourceOne(@RequestParam("code") String code);

    @ApiOperation("查找配方对应的糟源类型")
    @RequestMapping(value = "/getFormulaById",method = RequestMethod.GET)
    ResultVO<Formula> getFormulaById(@RequestParam("id")Integer id);

    @RequestMapping(value = "/getMonthPlanVinasseList",method = RequestMethod.GET)
    ResultVO<List<MonthPlanVinasse>> getMonthPlanVinasseList(@RequestParam("deleted") Integer deleted,
                                                             @RequestParam("monthPlanId") Integer monthPlanId,
                                                             @RequestParam("billType") Integer billType);

    @RequestMapping(value = "/getMonthPlanList",method = RequestMethod.GET)
    ResultVO<List<MonthPlan>> getMonthPlanList(@RequestParam("deleted") Integer deleted,
                                               @RequestParam("planTime") String planTime,
                                               @RequestParam("month") String month,
                                               Integer centerId);

    @RequestMapping(value = "/getRowById",method = RequestMethod.GET)
    ResultVO<Row> getRowById(@RequestParam("id")Integer id);

    @RequestMapping(value = "/kojiPharmacology/importData",method = RequestMethod.POST)
    @ApiOperation("曲粉理化导入")
    ResultVO importData(@RequestBody List<KojiPharmacologyTemplate> anImport);

    @RequestMapping(value = "/monthPlanMaterial",method = RequestMethod.POST)
    @ApiOperation("日计划查看生产情况接口")
    ResultVO<List<MonthPlanMaterialVO>> queryMaterialList(@RequestBody MonthPlanMaterialReq req);
}
