package com.hvisions.brewage.feign.technology;

import com.hvisions.brewage.technology.dto.JudgeRouteParameterRangeDTO;
import com.hvisions.brewage.technology.dto.ProcessAlarmAddDTO;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @Description: ConfigurationClient
 * @author: yyy
 * @time: 2023/12/12 10:11
 */
@FeignClient(name = "brewage-technology-server", path = "/proc/process/alarm", fallbackFactory = ProcessAlarmClientFallBack.class)
public interface ProcessAlarmClient {

    @ApiOperation("判断是否超工艺参数上下限范围,超过则自动生成参数异常")
    @PostMapping("/judgeRouteNodeParameterRange")
    ResultVO<String> judgeRouteParameterRange(@RequestBody JudgeRouteParameterRangeDTO dto);

    @ApiOperation("新增工艺告警")
    @PostMapping("/addProcessAlarm")
    ResultVO<String> addProcessAlarm(@RequestBody ProcessAlarmAddDTO addDTO);
}
