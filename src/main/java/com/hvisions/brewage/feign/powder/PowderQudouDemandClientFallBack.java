package com.hvisions.brewage.feign.powder;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.brewage.powder.DemandBatchOperateDTO;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

@Slf4j
@Component
public class PowderQudouDemandClientFallBack extends BaseFallbackFactory<PowderQudouDemandClient> {


    @Override
    public PowderQudouDemandClient getFallBack(ResultVO vo) {
        return new PowderQudouDemandClient() {

            @Override
            public ResultVO batchOperateQudouDemand(DemandBatchOperateDTO demandBatchOperateDTO) {
                log.error("fegin接口调用失败,接口参数:" + JSONObject.toJSONString(demandBatchOperateDTO));
                return vo;
            }

            @Override
            public ResultVO delTurnoverDemandDetail(@RequestParam String taskNo, @RequestParam String lineName) {
                log.error("fegin接口调用失败,接口参数taskNo:{}, lineName:{}", taskNo, lineName);
                return vo;
            }
        };
    }
}









