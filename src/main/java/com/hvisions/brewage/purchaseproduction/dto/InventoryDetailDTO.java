package com.hvisions.brewage.purchaseproduction.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 盘点任务详情
 * @author: Jcao
 * @time: 2022/4/25 11:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "盘点任务详情")
public class InventoryDetailDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "库位id")
    private Integer storageId;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位名称")
    private String locationName;

    @ApiModelProperty(value = "库区id")
    private Integer areaId;

    @ApiModelProperty(value = "库区名称")
    private String areaName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "帐存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "实盘数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "差异数量")
    private BigDecimal differenceQuantity;

    @ApiModelProperty(value = "物料类型id")
    private Integer materialType;

    @ApiModelProperty(value = "库位id")
    private Integer locationId;

}
