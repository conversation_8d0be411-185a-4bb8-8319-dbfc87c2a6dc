package com.hvisions.powder.dto.product.config.standard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 标准量配置新增修改dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "标准量配置新增修改dto")
public class StandardUpdateDTO {
    @ApiModelProperty(value = "主键id ")
    private Integer id;

    @ApiModelProperty(value = "标准编号")
    private String standardNo;

    @ApiModelProperty(value = "物料id ")
    private Integer materialId;

    @ApiModelProperty(value = "物料编号 ")
    private String materialCode;

    @ApiModelProperty(value = "物料名称 ")
    private String materialName;

    @ApiModelProperty(value = "标准量 ")
    private BigDecimal standardMeasure;

    @ApiModelProperty(value = "单位 ")
    private String measureUnit;

    @ApiModelProperty(value = "备注 ")
    private String remark;

}
