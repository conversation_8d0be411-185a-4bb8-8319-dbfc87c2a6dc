package com.hvisions.powder.dto.product.process.shelvesAllocateOrder;

import com.hvisions.powder.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 外销曲块出库单
 * @TableName t_mkp_shelves_allocate_order
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "曲框曲架调拨单DTO")
public class ShelvesAllocateOrderDTO extends SysBaseDTO implements Serializable {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编号")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "楼层id")
    private Integer floorId;

    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    @ApiModelProperty(value = "楼层编码")
    private String floorCode;

    @ApiModelProperty(value = "库房id")
    private Integer storeroomId;

    @ApiModelProperty(value = "库房编码")
    private String storeroomCode;

    @ApiModelProperty(value = "出曲框数量")
    private Integer frameNum;

    @ApiModelProperty(value = "出曲块数量")
    private Integer blockNum;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "左边选择库位")
    private List<Integer> leftLocations;

    @ApiModelProperty(value = "右边选择库位")
    private List<Integer> rightLocations;

}
