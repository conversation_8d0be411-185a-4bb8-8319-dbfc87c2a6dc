package com.hvisions.powder.dto.product.craft.craftProject;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.powder.dto.product.QueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ApiModel(description = "工艺巡检项目筛选DTO 分页")
@EqualsAndHashCode(callSuper = true)
public class CraftProjectPageQueryDTO extends PageInfo implements Serializable, QueryDTO {

    private Integer id;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

}