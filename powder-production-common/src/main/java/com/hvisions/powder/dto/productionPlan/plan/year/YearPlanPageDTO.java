package com.hvisions.powder.dto.productionPlan.plan.year;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.powder.dto.SysBaseDTO;
import com.hvisions.powder.dto.productionPlan.audit.AuditPageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @描述: 年计划查询DTO
 * @作者: 刘文勇
 * @日期: 2024/1/23 16:25
 * @版本 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "年计划查询DTO")
public class YearPlanPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "计划编号")
    private String code;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "计划年份")
    private Integer year;

    @ApiModelProperty(value = "普曲计划数量")
    private BigDecimal ordinaryPlanCount;

    @ApiModelProperty(value = "普曲完成数量")
    private BigDecimal ordinaryCompletedCount;

    @ApiModelProperty(value = "有机曲计划数量")
    private BigDecimal organicPlanCount;

    @ApiModelProperty(value = "有机曲完成数量")
    private BigDecimal organicCompletedCount;

    @ApiModelProperty(value = "进度")
    private String schedule;

    @ApiModelProperty(value = "计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "计划开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "计划结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "计划状态;1、新建、2-审批中 、3-生效 4 -归档")
    private String status;

    @ApiModelProperty(value = "审批详情")
    private AuditPageDTO auditPageDTO;

    @ApiModelProperty(value = "年计划详情")
    private List<YearPlanDetailDTO> yearPlanList;

}
