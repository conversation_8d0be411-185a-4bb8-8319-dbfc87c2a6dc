package com.hvisions.powder.dto.product.craft.craftPowderCrew;

import com.hvisions.powder.dto.SysBaseDTO;
import com.hvisions.powder.dto.product.QueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ApiModel(description = "制曲班组DTO")
@EqualsAndHashCode(callSuper = true)
public class CraftPowderCrewDTO extends SysBaseDTO implements Serializable, QueryDTO {
    @ApiModelProperty(value = "班组名称")
    private String crewName;

    @ApiModelProperty(value = "班组编码")
    private String crewCode;

    @ApiModelProperty(value = "班组状态")
    private String state;

    @ApiModelProperty(value = "楼层id")
    private Integer floorId;

    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    @ApiModelProperty(value = "楼层编码")
    private String floorCode;

    @ApiModelProperty(value = "组长id")
    private Integer leaderId;

    @ApiModelProperty(value = "组长名称")
    private String leaderName;
}
