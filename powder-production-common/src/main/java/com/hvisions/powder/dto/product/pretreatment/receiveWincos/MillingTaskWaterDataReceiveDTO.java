package com.hvisions.powder.dto.product.pretreatment.receiveWincos;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 磨粉着水机记录接收dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@ApiModel(description = "磨粉着水机记录接收dto")
public class MillingTaskWaterDataReceiveDTO {

    @ApiModelProperty(value = "着水机编号")
    @JsonProperty("MyfeIdent")
    private String myfeIdent;

    @ApiModelProperty(value = "目标水分-润麦（百分比）")
    @JsonProperty("WaterTargetPercent")
    private BigDecimal waterTargetPercent;

    @ApiModelProperty(value = "实际水分-润麦（百分比）")
    @JsonProperty("WaterActualPercent")
    private BigDecimal waterActualPercent;

}
