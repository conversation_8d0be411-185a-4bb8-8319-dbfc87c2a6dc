package com.hvisions.powder.dto.product.config.shelve;

import com.hvisions.powder.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 曲架管理分页返回dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "曲架管理分页返回dto")
public class ShelvePageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "物料编号")
    private String code;

    @ApiModelProperty(value = "物料名称")
    private String name;

    @ApiModelProperty(value = "长")
    private Integer length;
    
    @ApiModelProperty(value = "宽")
    private Integer width;

    @ApiModelProperty(value = "高")
    private Integer height;

    @ApiModelProperty(value = "层数")
    private Integer layer;

    @ApiModelProperty(value = "容量")
    private Integer capacity;

    @ApiModelProperty(value = "备注")
    private String remark;

}
