package com.hvisions.powder.dto.kanban.product.timeProduct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "曲坯月度产量详情")
public class TimeEmbryoProductDTO {

    @ApiModelProperty(value = "时间")
    private String time;

    @ApiModelProperty(value = "二楼曲架数")
    private BigDecimal towFloorShelveNum;

    @ApiModelProperty(value = "三楼曲架数")
    private BigDecimal threeFloorShelveNum;

    @ApiModelProperty(value = "四楼曲架数")
    private BigDecimal fourFloorShelveNum;

    @ApiModelProperty(value = "二楼块数")
    private BigDecimal towFloorOutputNum;

    @ApiModelProperty(value = "三楼块数")
    private BigDecimal threeFloorOutputNum;

    @ApiModelProperty(value = "四楼块数")
    private BigDecimal fourFloorOutputNum;

    @ApiModelProperty(value = "二楼产量")
    private BigDecimal towFloorQuantity;

    @ApiModelProperty(value = "三楼产量")
    private BigDecimal threeFloorQuantity;

    @ApiModelProperty(value = "四楼产量")
    private BigDecimal fourFloorQuantity;

}
