package com.hvisions.powder.dto.storage.warehouse.powder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 仓库曲粉仓出库记录dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "仓库曲粉仓出库记录dto")
public class WarehousePowderOutStorageDTO {

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "粉碎工单号")
    private String orderNo;

    @ApiModelProperty(value = "出库库房ids")
    private String storeroomIds;

    @ApiModelProperty(value = "出库库房codes")
    private String storerooms;

    @ApiModelProperty(value = "曲框投入量")
    private Integer frameNum;

    @ApiModelProperty(value = "曲块数量")
    private Integer inputNum;

    @ApiModelProperty(value = "曲块标准重量")
    private BigDecimal standardQuantity;

    @ApiModelProperty(value = "完成时间")
    private Date actualEndTime;

//    @ApiModelProperty(value = "当前库存")
//    private BigDecimal quantity;
//
//    @ApiModelProperty(value = "出库总量")
//    private BigDecimal total;
//
//    @ApiModelProperty(value = "出库次数")
//    private Integer count;
//
//    @ApiModelProperty(value = "最新出库时间")
//    private Date outTime;

    @ApiModelProperty(value = "发放明细")
    private List<WarehousePowderOutDetailDTO> list;

}
