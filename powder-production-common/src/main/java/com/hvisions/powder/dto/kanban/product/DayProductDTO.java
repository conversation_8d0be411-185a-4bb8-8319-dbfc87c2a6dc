package com.hvisions.powder.dto.kanban.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 日生产数据
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "日生产情况返回DTO")
public class DayProductDTO {

    @ApiModelProperty(value = "昨天生产数量-压曲")
    private BigDecimal yesterdayNum;

    @ApiModelProperty(value = "对比前天")
    private BigDecimal contrastRatio;

    @ApiModelProperty(value = "本周压曲完成率")
    private BigDecimal completionRate;

    @ApiModelProperty(value = "今日排班情况")
    private String scheduling;

    @ApiModelProperty(value = "曲坯日数据")
    private DayProductByFloorDTO qpDay;

    @ApiModelProperty(value = "曲块日数据")
    private DayProductByFloorDTO qkDay;

    @ApiModelProperty(value = "曲粉日数据")
    private DayProductByFloorDTO qfDay;
}
