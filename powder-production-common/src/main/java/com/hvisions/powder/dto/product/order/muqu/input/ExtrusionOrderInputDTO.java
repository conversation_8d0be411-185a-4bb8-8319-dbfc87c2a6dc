package com.hvisions.powder.dto.product.order.muqu.input;

import com.hvisions.powder.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 压曲工单母曲投入
 * @TableName t_mkp_extrusion_order_input
 */
@Data
@ApiModel(description = "压曲工单母曲投入DTO")
@EqualsAndHashCode(callSuper = true)
public class ExtrusionOrderInputDTO extends SysBaseDTO implements Serializable {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单不能为空")
    private Integer orderId;

    /**
     * 母曲筒仓id
     */
    @ApiModelProperty(value = "母曲筒仓id")
    @NotNull(message = "母曲筒仓id不能为空")
    private Integer warehouseId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal number;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private String batch;

}