package com.hvisions.powder.dto.product.order.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 曲粉产出修正
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "曲粉产出修正dto")
public class UpdateSmashPowderOutPutDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "修正值")
    private BigDecimal correctionValue;
}
