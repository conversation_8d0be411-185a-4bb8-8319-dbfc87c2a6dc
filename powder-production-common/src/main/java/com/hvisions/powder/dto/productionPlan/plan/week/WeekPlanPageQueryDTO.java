package com.hvisions.powder.dto.productionPlan.plan.week;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 制曲周计划分页条件dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "制曲周计划分页条件dto")
public class WeekPlanPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "状态:1-新建、2-审批中、3-生效、4-归档")
    private String status;

    @ApiModelProperty(value = "月计划id")
    private Integer monthPlanId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
