package com.hvisions.powder.dto.product.order.muqu.demand;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 母曲需求发放dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "母曲需求发放dto")
public class MuQuDemandIssueDTO {

    @ApiModelProperty(value = "需求id集合")
    private List<Integer> demandIds;

}
