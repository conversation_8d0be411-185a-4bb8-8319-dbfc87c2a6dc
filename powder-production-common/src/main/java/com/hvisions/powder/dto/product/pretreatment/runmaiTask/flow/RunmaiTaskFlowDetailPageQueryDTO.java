package com.hvisions.powder.dto.product.pretreatment.runmaiTask.flow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 润麦任务详情分页查询条件dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "润麦任务详情分页查询条件dto")
public class RunmaiTaskFlowDetailPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "润麦任务编号")
    private String taskCode;

    @ApiModelProperty(value = "润麦仓id")
    private Integer acceptWarehouseId;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}
