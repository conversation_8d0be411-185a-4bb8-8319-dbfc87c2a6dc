package com.hvisions.powder.dto.product.pretreatment.receiveWincos;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 接收入仓任务入仓明细dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "接收入仓任务入仓明细dto")
public class WarehouseTaskDetailReceiveDTO {

    @ApiModelProperty(value = "mes单据号")
    @JsonProperty("BillNo")
    private String BillNo;

    @ApiModelProperty(value = "入仓明细列表")
    @JsonProperty("ReceiverItems")
    List<StorageWeightDTO> receiverItems;

}
