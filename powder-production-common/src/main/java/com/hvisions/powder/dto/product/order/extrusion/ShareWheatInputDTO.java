package com.hvisions.powder.dto.product.order.extrusion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 磨粉小麦投入分摊dto
 */
@Data
@ApiModel(description = "磨粉小麦投入分摊dto")
@EqualsAndHashCode(callSuper = false)
public class ShareWheatInputDTO {

    @ApiModelProperty(value = "楼层id")
    private Integer floorId;

    @ApiModelProperty(value = "小麦名称")
    private String wheatName;

    @ApiModelProperty(value = "小麦编码")
    private String wheatCode;

    @ApiModelProperty(value = "小麦id（当天磨粉）")
    private Integer wheatId;

    @ApiModelProperty(value = "小麦投入数量（当天磨粉任务量）")
    private BigDecimal wheatQuantity;

    @ApiModelProperty(value = "批次")
    private String batch;

}