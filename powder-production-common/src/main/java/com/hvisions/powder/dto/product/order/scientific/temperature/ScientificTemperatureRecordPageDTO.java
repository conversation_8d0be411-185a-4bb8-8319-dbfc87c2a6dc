package com.hvisions.powder.dto.product.order.scientific.temperature;

import com.hvisions.powder.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 科研制曲记录分页返回dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "科研制曲记录分页返回dto")
public class ScientificTemperatureRecordPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "科研记录id")
    private Integer scientificId;

    @ApiModelProperty(value = "日期")
    private Date date;

    @ApiModelProperty(value = "二氧化碳浓度")
    private String co2;

    @ApiModelProperty(value = "环境温度")
    private String environmentTemperature;

    @ApiModelProperty(value = "曲房温度")
    private String storeroomTemperature;

    @ApiModelProperty(value = "环境湿度")
    private String environmentHumidity;

    @ApiModelProperty(value = "曲房湿度")
    private String storeroomHumidity;

}
