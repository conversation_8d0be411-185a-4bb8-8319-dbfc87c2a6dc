package com.hvisions.powder.dto.kanban.product.smashBatchCompart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "粉碎批次参数对比查询dto")
public class SmashBatchCompareChildrenDTO {

    @ApiModelProperty(value = "发酵房号")
    private String fermentNo;

    @ApiModelProperty(value = "工单实际开始日期")
    private Date actualStartTime;

    @ApiModelProperty(value = "发酵天数")
    private Integer fermentPersistentDay;

    @ApiModelProperty(value = "母曲投入比例")
    private BigDecimal primerInputRate;

    @ApiModelProperty(value = "麦料水分含量")
    private Integer wheatWater;

    @ApiModelProperty(value = "粉碎度")
    private Integer smashValue;

    @ApiModelProperty(value = "润麦水分含量")
    private Integer runMaiWater;


}
