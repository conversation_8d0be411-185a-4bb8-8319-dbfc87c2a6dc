package com.hvisions.powder.dto.product.order.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 压曲投入同步dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "压曲投入修正dto")
public class ExtrusionInputAmendDTO {

    @ApiModelProperty(value = "计划id")
    private Integer planId;

    @ApiModelProperty(value = "订单id列表")
    private List<Integer> orderIds;

    @ApiModelProperty(value = "小麦物料号")
    private String wheatCode;

    @ApiModelProperty(value = "小麦投入修正数量")
    private BigDecimal wheatAmendQuantity;

}
