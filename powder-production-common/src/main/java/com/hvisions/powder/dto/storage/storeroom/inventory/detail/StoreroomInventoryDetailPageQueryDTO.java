package com.hvisions.powder.dto.storage.storeroom.inventory.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 库房盘点详情分页查询条件dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "库房盘点详情分页查询条件dto")
public class StoreroomInventoryDetailPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "盘点单号")
    private String inventoryNo;

    @ApiModelProperty(value = "库房编号")
    private String storeroomNo;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "盘点结果:盘盈、盘亏")
    private String inventoryResult;

}
