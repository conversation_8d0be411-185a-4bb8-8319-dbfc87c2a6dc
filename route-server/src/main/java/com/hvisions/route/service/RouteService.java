package com.hvisions.route.service;

import com.hvisions.route.dto.*;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: RouteService</p>
 * <p>Description: 工艺路线服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface RouteService {

    /**
     * 更新工艺路线
     *
     * @param routeDTO 工艺路线对象
     */
    void update(RouteDTO routeDTO);


    /**
     * 根据id删除工艺路线
     *
     * @param routeId 工艺路线id
     */
    void deleteProductRoute(Integer routeId);

    /**
     * 生效工艺路线
     *
     * @param routeId 工艺路线id
     */
    void activeRoute(Integer routeId);

    /**
     * 获取工艺路线详细信息
     *
     * @param routeId 工艺路线id
     * @return 工艺路线详细信息
     */
    RouteDTO getRoute(Integer routeId);


    /**
     * 获取工艺路线，包含子工艺路线信息
     *
     * @param id 工艺路线id
     * @return 工艺路线信息
     */
    RouteDTO getRouteWithSubRoute(Integer id);

    /**
     * 根据工艺路线模板创建产品工艺路线
     *
     * @param productRouteDTO 产品 工艺路线关系DTO
     * @return 新增关系的id
     */
    Integer createProductRouteByRoute(ProductRouteDTO productRouteDTO);

    /**
     * 复制工艺路线
     *
     * @param copyDTO 复制参数
     * @return 复制后的工艺路线id
     */
    Integer copyProductRoute(RouteCopyDTO copyDTO);



    /**
     * 根据产品id 列表查询产品工艺信息
     *
     * @param productId 产品id
     * @return ProductionDTO 产品工艺路线关系
     */
    List<RouteDTO> getProductRouteDTOsByProductId(Integer productId);



    /**
     * 根据产品id 列表查询工艺路线信息
     *
     * @param productId 产品id
     * @return ProductionDTO 工艺路线
     */
    List<RouteDTO> getRouteDTOsByProductId(Integer productId);



    /**
     * 获取工艺路线详细信息
     *
     * @param productRouteId 工艺路线id
     * @param code           拼接后节点code
     * @return 工艺路线详细信息
     */
    List<NodeParameterData> getStepParam(Integer productRouteId, String code);

    /**
     * 查询工艺路线分页信息
     *
     * @param queryDTO 查询对象
     * @return 工艺路线分页信息
     */
    Page<RouteDTO> getRoute(RouteQueryDTO queryDTO);



    /**
     * 获取工艺路线详细信息
     *
     * @param stepParamQueryDTO 查询条件
     * @return 工艺路线详细信息
     */
    Page<NodeParameterData> getStepParamPage(StepParamQueryDTO stepParamQueryDTO);

    /**
     * 根据id查询工艺流程key
     *
     * @param id 工艺操作id
     * @param operationTypeId 操作类型
     * @return key值列表
     */
    List<Node> getNodeListByRouteIdAndOperationType(int id, int operationTypeId);


    /**
     * 归档版本
     *
     * @param routeId 版本id
     */
    void archiveRoute(Integer routeId);

    /**
     * 获取所有配置了产品工艺的物料列表
     *
     * @param pageInfo 分页信息
     * @return 配置了产品工艺的物料列表
     */
    Page<MaterialInfo> getProductRouteMaterial(RouteMaterialQuery pageInfo);

    /**
     * 获取生效的产品工艺
     * @param materialId 物料di
     * @param routeCode 工艺编码
     * @return 生效的产品工艺
     */
    RouteDTO getActiveProductRoute(Integer materialId, String routeCode);

    /**
     * 获取生效的产品工艺
     * @param materialId 物料id
     * @return 生效的产品工艺
     */
    RouteDTO getActiveProductRoute(Integer materialId);


    /**
     * 获取生效的产品工艺
     * @param bomCode 物料code
     * @return 生效的产品工艺
     */
    RouteDTO getActiveProductRouteByBomCode(String bomCode);

    /**
     * 升级版本
     *
     * @param id 旧版本id
     * @return 新版本id
     */
    Integer levelUp(Integer id);
    /**
     * 产品工艺路线分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    Page<RouteDTO> getProductRouteByDto(RouteQueryDTO queryDTO);


    /**
     * 创建工艺路线
     * @param routeDTO 工艺路线
     * @return 工艺路线id
     */
    Integer createTemplate(RouteDTO routeDTO);
    /**
     * 更新工艺路线模板
     *
     * @param routeDTO 工艺路线模板
     */
    void updateTemplate(RouteDTO routeDTO);
    /**
     * 根据id删除工艺路模板线
     *
     * @param routeTemplateId 工艺路线模板id
     */
    void deleteRouteTemplate(Integer routeTemplateId);

    /**
     * 复制工艺路模板线模板
     *
     * @param copyDTO 工艺路线模板id
     * @return 复制成功后的工艺路线模板id
     */
    Integer copyRouteTemplate(RouteCopyDTO copyDTO);
    /**
     * 创建产品工艺路线
     *
     * @param routeDTO 产品工艺路线
     * @return 主键
     */
    Integer createProductRoute(RouteDTO routeDTO);
}

    
    
    
    