/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.route.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname FormulaDetailReq
 * @description 糟源配方明细数据传输对象实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@ApiModel("FormulaDetail数据传输对象实体类")
public class FormulaDetailReq  implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    protected Integer id;

    @ApiModelProperty("配方id")
    private Integer formulaId;

    @ApiModelProperty("物料id")
    private Integer materialId;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("类型;0-投入、1-消耗、2-产出")
    private String type;

    @ApiModelProperty("单位;kg、g、t")
    private String unit;

    @ApiModelProperty("数量")
    private BigDecimal quality;

    @ApiModelProperty("备注")
    private String remark;

}