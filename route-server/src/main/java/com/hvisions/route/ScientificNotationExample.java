package com.hvisions.route;

public class ScientificNotationExample {
    public static void main(String[] args) {
        double number = 318763000;

        // 调用自定义方法将数字转换为科学计数法表示
        String scientificNumber = toScientificNotation(number);

        // 打印结果
        System.out.println(scientificNumber);

        // 将结果传递给前端（假设这里是通过打印模拟传递给前端）
        // 在实际应用中，可以通过API或其他方式传递
        // response.set("number", scientificNumber);
    }

    public static String toScientificNotation(double number) {
        if (number == 0) {
            return "0";
        }

        int exponent = (int) Math.floor(Math.log10(Math.abs(number)));
        double mantissa = number / Math.pow(10, exponent);

        return String.format("%.2f×10%s", mantissa, toSuperscript(exponent));
    }

    public static String toSuperscript(int exponent) {
        String expStr = Integer.toString(exponent);
        StringBuilder superscript = new StringBuilder();

        for (char c : expStr.toCharArray()) {
            switch (c) {
                case '0':
                    superscript.append('\u2070');
                    break;
                case '1':
                    superscript.append('\u00B9');
                    break;
                case '2':
                    superscript.append('\u00B2');
                    break;
                case '3':
                    superscript.append('\u00B3');
                    break;
                case '4':
                    superscript.append('\u2074');
                    break;
                case '5':
                    superscript.append('\u2075');
                    break;
                case '6':
                    superscript.append('\u2076');
                    break;
                case '7':
                    superscript.append('\u2077');
                    break;
                case '8':
                    superscript.append('\u2078');
                    break;
                case '9':
                    superscript.append('\u2079');
                    break;
                case '-': superscript.append('\u207B'); break;
            }
        }

        return superscript.toString();
    }
}