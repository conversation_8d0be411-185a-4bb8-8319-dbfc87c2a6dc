#dev配置
spring:
  redis:
    host: ************
    port: 6379
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *********************************************************************************************
  application:
    name: route-leiming
eureka:
  instance:
    prefer-ip-address: true
    instance-id: route:${server.port}
  client:
    service-url:
      defaultZone: http://************:8763/eureka/