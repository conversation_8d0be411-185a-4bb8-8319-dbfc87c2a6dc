<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.route.dao.FormulaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.route.dto.entity.HvBmFormula">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
        <result column="name" property="name"/>
        <result column="unit" property="unit"/>
        <result column="state" property="state"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        version,
        creator_id, updater_id, site_num, deleted, production_base_id, production_base_name, code, name, source_code, unit, state, begin_time, end_time
    </sql>
    <select id="getCenterName" resultType="java.lang.String">
        select name
        from equipment.hv_bm_location
        where id = #{centerId}
    </select>
</mapper>
