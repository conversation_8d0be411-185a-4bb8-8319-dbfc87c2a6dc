spring:
  mail:
    host: smtp.mxhichina.com #发送邮件服务器地址
    username: <EMAIL> #发送邮件的邮箱地址
    password: xxxxx
    from: <EMAIL> #发送邮件的邮箱地址
    default-encoding: utf-8 #字符编码格式
    port: 25 #服务器端口号
  jmx:
    enabled: false
  cloud:
    refresh:
      #为了解决springboot与spring cloud数据库初始化检查添加的配置项的循环依赖问题所添加
      refreshable: none
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  cache:
    type: redis
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    hibernate:
      ddl-auto: update
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin
  application:
    name: auth
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  profiles:
    active: dev
server:
  port: 9011
eureka:
  instance:
    prefer-ip-address: true
    #实例名
    instance-id: auth:${server.port}
#    ip-address: ************
  client:
    service-url:
      #euraka地址
      defaultZone: http://*************:8763/eureka/
mybatis:
  typeAliasesPackage: com.hvisions.auth.entitys
  mapperLocations: classpath:mapper/*.xml
h-visions:
  # 是否开启接口日志
  log:
    enable: false
  # 扩展属性，需要设置数据库名称
  extend:
    schema: authority
  service-name: 人员权限服务
  swagger:
    # 如果为false或者没有此属性。swagger界面将不会加载
    enable: true
    api-url: http://localhost:9010/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
  # 消息记录
  auth:
    # 判断是否要求用户初始化或者被重置密码后需要强制用户设置自己的密码
    force-password-change: true
    # 登录接口尝试设置
    attempts:
      # 是否开启登录接口尝试模式
      enable: true
      # 是否尝试全部失败后锁定账号
      lock-account: true
      # 最大尝试次数
      max-time: 5
      # 多长时间内的尝试次数会计算其中,单位：分钟
      in-time: 10
    # 演示模式，增加了一个直接获取用户信息的接口。用于演示环境，正式环境请不要开启。
    demo-mode: false
    # 默认的用户密码和重置后的用户密码
    password: Lzlj@1573#
    message:
      #是否开启自动清理过期消息功能
      auto-clean: true
      #消息过期删除，0为不过期,单位为天
      expire-time: 30
      #是否开启发送邮件功能
      send-message: true
    #指定菜单文件路径
    moduleFilePath: ./module.json
    password-regex: ^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$
    password-regex-error-message: 密码规则校验失败
    useractivity:
      auto-clean: true
  userevent:
    handler:
      type: rabbitmq
  data-audit:
    #是否开启数据历史审计
    enable: false
    #是否审计所有实体
    audit-all-entity: false
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}

ribbon:
  ConnectTimeout: 5000 #请求连接的超时时间，默认时间为1秒
  ReadTimeout: 5000 #请求处理的超时时间
