spring:
  rabbitmq:
    host: ************
    username: admin
    password: admin
  redis:
    host: ************
    port: 6379
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *************************************************************************************************
  application:
    name: auth-test
eureka:
  instance:
    prefer-ip-address: true
    #实例名
    instance-id: auth:${server.port}
  client:
    service-url:
      #euraka地址
      defaultZone: http://localhost:8763/eureka/
h-visions:
  # 消息记录
  auth:
    # 判断是否要求用户初始化或者被重置密码后需要强制用户设置自己的密码
    force-password-change: true
    # 登录接口尝试设置
    attempts:
      # 是否开启登录接口尝试模式
      enable: true
      # 是否尝试全部失败后锁定账号
      lock-account: false
      # 最大尝试次数
      max-time: 2
      # 多长时间内的尝试次数会计算其中,单位：分钟
      in-time: 5
    # 演示模式，增加了一个直接获取用户信息的接口。用于演示环境，正式环境请不要开启。
    demo-mode: false
    # 默认的用户密码和重置后的用户密码
    password: 123
    message:
      #是否开启自动清理过期消息功能
      auto-clean: true
      #消息过期删除，0为不过期,单位为天
      expire-time: 30
      #是否开启发送邮件功能
      send-message: true
    #指定菜单文件路径
    moduleFilePath: ./module.json
    password-regex: 123
    password-regex-error-message: 密码不对啊
  userevent:
    handler:
      type: rabbitmq
  data-audit:
    enable: false
    audit-all-operation: true
    audit-all-entity: true