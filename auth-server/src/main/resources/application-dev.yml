spring:
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: 1001
  redis:
    host: ************
    port: 6379
    password: 1001
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 1001
    url: ****************************************************************************************************
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
#IAM对接配置
#IAM 接口调用地址
iam.config.url: https://iamtest.lzlj.com
#IAM 应用标识 - 客户端应用注册ID（统一身份认证平台提供）
iam.config.client_id: MES
#IAM 密钥 - 客户端应用注册密钥（统一身份认证平台提供）
iam.config.client_secret: 509e100b7f404f1090f0123bc92045ae
#ESB调用地址
esb:
  #IAM获取用户信息接口
  iam-url: http://esbtest.lzljidc.com:9001/WP_LZLJ_SOA/APP_OA_SERVICES/Proxy_Services/TA_TYSFRZ/LZLJ_935_OA_ObtainAccountData_PS
