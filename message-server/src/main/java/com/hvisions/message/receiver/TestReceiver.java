package com.hvisions.message.receiver;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.RabbitListenerErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class TestReceiver {



    /**
     * 监听队列,当流程结束的时候接收响应的消息
     *
     * @param message message
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "queue_test", autoDelete = "false"),
            exchange = @Exchange(value = "message.name", type = ExchangeTypes.TOPIC)), errorHandler = "fixErrorHandler")
    public void process(String message) {
        log.info("接收到消息：" + message);
        JSONObject jsonObject = JSONObject.parseObject(message);
        JSONObject messageData = jsonObject.getJSONObject("message");
//        throw new BaseKnownException(10000, "未查询到工单信息");
//        try {
//            log.info("开始休眠");
//            Thread.sleep(3000);
//            log.info("休眠结束");
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
    }

    @Bean
    public RabbitListenerErrorHandler fixErrorHandler() {
        return (message, message1, e) -> {
            log.error(e.getMessage(), e);
            return null;
        };
    }
}
