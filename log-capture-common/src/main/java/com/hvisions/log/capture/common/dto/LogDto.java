package com.hvisions.log.capture.common.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title: LogDto</p >
 * <p>Description: log日志服务dto对象</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/1/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class LogDto implements Serializable {


    private static final long serialVersionUID = -2440675861846654786L;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer logType;

    /**
     * 调用传参
     */
    @ApiModelProperty(value = "调用时传入参数")
    private String logParameter;

    /**
     * 模块（调用的微服务的名称）
     */
    @ApiModelProperty(value = "模块（调用的微服务的名称）")
    private String logModular;

    /**
     * 异常信息
     */
    @ApiModelProperty(value = "异常信息")
    private String logExceptionMessage;

    /**
     * 调用人
     */
    @ApiModelProperty(value = "调用人")
    private String logInvocation;

    /**
     * 调用的API名称和路径
     */
    @ApiModelProperty(value = "调用的API名称和路径")
    private String location;

    /**
     * 调用时间
     */
    @ApiModelProperty(value = "调用时间(默认系统当前时间)")
    private Date logCaptureTime = new Date();

    /**
    *   控制器名称
    */
    @ApiModelProperty(value = "控制器名称")
    private String controllerName;

    /**
    *   调用的方法名称
    */
    @ApiModelProperty(value = "调用的方法名称")
    private String methodName;



}
