<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.technology.dao.base.CellarTurningFormulaDetailsMapper">
    <resultMap id="BaseResultMap" type="com.hvisions.technology.entity.base.CellarTurningFormulaDetails">
        <!--@Table base_cellar_turning_formula_details-->
        <id column="id" property="id"/>
        <result column="cellar_turning_formula_id" property="cellarTurningFormulaId"/>
        <result column="turnover_times" property="turnoverTimes"/>
        <result column="fermentation_days" property="fermentationDays"/>
        <result column="material_id" property="materialId"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="dosage" property="dosage"/>
        <result column="pattern" property="pattern"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        cellar_turning_formula_id,
        turnover_times,
        fermentation_days,
        material_id,
        material_code,
        material_name,
        dosage,
        pattern,
        create_time,
        update_time,
        creator_id,
        updater_id,
        deleted
    </sql>

    <insert id="addPitsFormula">
        INSERT INTO brewage.t_po_turn_over_pits_formula (
        back_alcoholic_code,
        back_alcoholic_quantity,
        create_time,
        is_deleted,
        qu_code,
        qu_quantity,
        remain_num,
        turn_over_pits_formula_code,
        turn_over_pits_formula_name,
        standard_vol,
        wj_code,
        hs_code,
        sdj_code,
        wj_quantity,
        hs_quantity,
        sdj_quantity,
        turnover_times,
        fermentation_days,
        out_pit_source,
        in_pit_source)
        VALUES (#{backAlcoholicCode},
                #{backAlcoholicQuantity},
                now(),
                0,
                #{quCode},
                #{quQuantity},
                #{remainNum},
                #{turnOverPitsFormulaCode},
                #{turnOverPitsFormulaName},
                #{standardVol},
                #{wjCode},
                #{hsCode},
                #{sdjCode},
                #{wjQuantity},
                #{hsQuantity},
                #{sdjQuantity},
                #{turnoverTimes},
                #{fermentationDays},
                #{outPitSource},
                #{inPitSource})
    </insert>

    <select id="selectPitsFormulaFlag" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        from brewage.t_po_turn_over_pits_formula
        WHERE is_deleted = 0
          and turn_over_pits_formula_code = #{turnOverPitsFormulaCode}
          and turnover_times = #{turnoverTimes}
    </select>

    <update id="updatePitsFormula">
        UPDATE brewage.t_po_turn_over_pits_formula
        SET back_alcoholic_code= #{backAlcoholicCode},
            back_alcoholic_quantity= #{backAlcoholicQuantity},
            modify_time= now(),
            qu_code= #{quCode},
            qu_quantity= #{quQuantity},
            remain_num= #{remainNum},
            turn_over_pits_formula_code= #{turnOverPitsFormulaCode},
            turn_over_pits_formula_name= #{turnOverPitsFormulaName},
            standard_vol= #{standardVol},
            wj_code= #{wjCode},
            hs_code= #{hsCode},
            sdj_code= #{sdjCode},
            wj_quantity= #{wjQuantity},
            hs_quantity= #{hsQuantity},
            sdj_quantity= #{sdjQuantity},
            turnover_times= #{turnoverTimes},
            fermentation_days= #{fermentationDays},
            out_pit_source= #{outPitSource},
            in_pit_source= #{inPitSource}
        WHERE turn_over_pits_formula_code = #{turnOverPitsFormulaCode}
          and turnover_times = #{turnoverTimes}
          and is_deleted = 0
    </update>

    <update id="deletePitsFormula">
        UPDATE brewage.t_po_turn_over_pits_formula a
            INNER JOIN brewage_technology.base_cellar_turning_formula b on a.turn_over_pits_formula_code=CONCAT(b.out_pit_source,'-',b.in_pit_source) and b.id =#{id,jdbcType=INTEGER}
        SET a.is_deleted=1,a.modify_time=now()

    </update>

    <update id="updateDeleteFormula">
        UPDATE brewage.t_po_turn_over_pits_formula a
        SET
            a.is_deleted = 1,
            a.modify_time = now()
        WHERE
            a.turn_over_pits_formula_code = #{turnOverPitsFormulaCode,jdbcType=VARCHAR} AND a.turnover_times NOT IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>