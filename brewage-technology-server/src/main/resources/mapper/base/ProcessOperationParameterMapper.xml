<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.technology.dao.base.ProcessOperationParameterMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.technology.entity.base.ProcessOperationParameter">
    <!--@Table base_process_operation_parameter-->
    <id column="id" property="id" />
    <result column="process_operation_id" property="processOperationId" />
    <result column="param_code" property="paramCode" />
    <result column="param_name" property="paramName" />
    <result column="collection_method" property="collectionMethod" />
    <result column="purpose" property="purpose" />
    <result column="unit" property="unit" />
    <result column="param_type" property="paramType" />
    <result column="is_out_of_tolerance" property="isOutOfTolerance" />
    <result column="data_type" property="dataType" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="creator_id" property="creatorId" />
    <result column="updater_id" property="updaterId" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, process_operation_id, param_code, param_name, collection_method, purpose, unit, 
    param_type, is_out_of_tolerance, data_type, create_time, update_time, creator_id, 
    updater_id, deleted
  </sql>
</mapper>