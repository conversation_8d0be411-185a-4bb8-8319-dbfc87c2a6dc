<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.technology.dao.base.BmRouteNodeGuideMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.technology.entity.base.BmRouteNodeGuide">
    <!--@Table hv_bm_route_node_guide-->
    <id column="id" property="id" />
    <result column="route_id" property="routeId" />
    <result column="route_code" property="routeCode" />
    <result column="operation_id" property="operationId" />
    <result column="operation_code" property="operationCode" />
    <result column="operation_name" property="operationName" />
    <result column="operation_guide_id" property="operationGuideId" />
    <result column="operation_instructions" property="operationInstructions" />
    <result column="create_time" property="createTime" />
    <result column="creator_id" property="creatorId" />
    <result column="deleted" property="deleted" />
    <result column="update_time" property="updateTime" />
    <result column="updater_id" property="updaterId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, route_id,route_code, operation_id, operation_code, operation_name, operation_guide_id, operation_instructions,
    create_time, creator_id, deleted, update_time, updater_id
  </sql>
</mapper>