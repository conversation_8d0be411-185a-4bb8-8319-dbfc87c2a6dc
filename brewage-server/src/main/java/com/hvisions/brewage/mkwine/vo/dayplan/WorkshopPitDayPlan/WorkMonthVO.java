package com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan;

import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitDayPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @program: brewage
 * @description: 自然月单个车间剩余计划生产甑口数据返回VO
 * @author: DengWeiTao
 **/
@ApiModel("自然月单个车间剩余计划生产甑口数据返回VO")
@Data
public class WorkMonthVO {

    @ApiModelProperty(value = "年份")
    private Integer cycleYear;

    @ApiModelProperty(value = "月份")
    private Integer cycleMonth;

    @ApiModelProperty(value = "工作天数")
    private Integer workDay;

    @ApiModelProperty("月度开始时间")
    private LocalDate monthBeginDate;

    @ApiModelProperty("月度结束时间")
    private LocalDate monthEndDate;

    @ApiModelProperty("计划时间")
    private String planDate;

    @ApiModelProperty("预计空窖数量")
    private Integer planEmptyNum;

    @ApiModelProperty("前一次排产是空窖排产--用于判断接下来的排产情况")
    private Boolean isEmptyProd;

    @ApiModelProperty("回酒计划使用量")
    private BigDecimal hjPlanQuality;

    @ApiModelProperty(value = "糟源甑口详细列表")
    private List<WorkMonthDetailVO> workMonthDetailVOList;

    @ApiModelProperty(value = "物料数据详细列表")
    private List<WorkMonthMaterialVO> workMonthMaterialVOList;

    @ApiModelProperty(value = "未完成的日计划 -- 还需要执行的日计划")
    private List<TPoWorkshopPitDayPlan> continueDayPlans;

    @ApiModelProperty(value = "空窖排产糟源")
    private List<String> emptyDayPlanVinasseList;

    @ApiModelProperty(value = "生产排产糟源")
    private List<String> prodDayPlanVinasseList;
}
