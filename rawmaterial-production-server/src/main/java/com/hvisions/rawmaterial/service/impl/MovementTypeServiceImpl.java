package com.hvisions.rawmaterial.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.MovementTypeMapper;
import com.hvisions.rawmaterial.dto.storage.movement.type.MovementTypeDTO;
import com.hvisions.rawmaterial.dto.storage.movement.type.MovementTypeListDTO;
import com.hvisions.rawmaterial.dto.storage.movement.type.MovementTypePageDTO;
import com.hvisions.rawmaterial.dto.storage.movement.type.MovementTypePageQueryDTO;
import com.hvisions.rawmaterial.entity.TMpdMovementType;
import com.hvisions.rawmaterial.service.MovementTypeService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: 移动类型
 * @author: Jcao
 * @time: 2022/4/24 8:57
 */
@Service
public class MovementTypeServiceImpl implements MovementTypeService {

    @Resource
    private MovementTypeMapper movementTypeMapper;


    /*
     * @Description: 分页查询移动类型列表
     *
     * <AUTHOR>
     * @param movementTypePageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.storage.movement.type.MovementTypePageDTO>
     */
    @Override
    public Page<MovementTypePageDTO> getMovementTypePageList(MovementTypePageQueryDTO movementTypePageQueryDTO) {
        return PageHelperUtil.getPage(movementTypeMapper::getMovementTypePageList, movementTypePageQueryDTO, MovementTypePageDTO.class);
    }

    /*
     * @Description: 获取移动类型列表
     *
     * <AUTHOR>
     * @return java.util.List<com.hvisions.purchase.dto.storage.movement.type.MovementTypeListDTO>
     */
    @Override
    public List<MovementTypeListDTO> getMovementTypeList() {
        List<TMpdMovementType> tMpdMovementTypes = movementTypeMapper.selectList(new LambdaQueryWrapper<TMpdMovementType>()
                .eq(TMpdMovementType::getDeleted, 0));
        return DtoMapper.convertList(tMpdMovementTypes, MovementTypeListDTO.class);
    }

    @Override
    public Integer addMovementType(MovementTypeDTO movementTypeDTO) {
        TMpdMovementType movementType = DtoMapper.convert(movementTypeDTO, TMpdMovementType.class);
        movementType.setCreateTime(new Date());
        movementType.setUpdateTime(new Date());
        return movementTypeMapper.insert(movementType);
    }

    @Override
    public Integer updateMovementType(MovementTypeDTO movementTypeDTO) {
        TMpdMovementType movementType = DtoMapper.convert(movementTypeDTO, TMpdMovementType.class);
        movementType.setUpdateTime(new Date());
        movementType.setUpdaterId(null);
        return movementTypeMapper.updateById(movementType);
    }

    @Override
    public Integer deleteMovementType(Integer id) {
        return movementTypeMapper.deleteById(id);
    }
}
