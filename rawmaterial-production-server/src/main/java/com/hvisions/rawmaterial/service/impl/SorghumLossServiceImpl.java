package com.hvisions.rawmaterial.service.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.client.ParamClient;
import com.hvisions.brewage.dto.mkwine.vo.ParamVO;
import com.hvisions.brewage.feign.purchase.PurchaseClient;
import com.hvisions.rawmaterial.consts.SapConst;
import com.hvisions.brewage.purchase.dto.CostLossItemDto;
import com.hvisions.brewage.purchase.dto.OrderWriteOffHeaderDto;
import com.hvisions.brewage.purchase.dto.SapBaseResponseDto;
import com.hvisions.brewage.purchase.dto.SyncCostLossDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.qudou.SapPostVO;
import com.hvisions.rawmaterial.dto.production.loss.LossPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.loss.SyncLossDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDetailDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossPageDTO;
import com.hvisions.rawmaterial.dao.SorghumLossDetailMapper;
import com.hvisions.rawmaterial.dao.SorghumLossMapper;
import com.hvisions.rawmaterial.entity.TMpdSorghumLoss;
import com.hvisions.rawmaterial.entity.TMpdSorghumLossDetail;
import com.hvisions.rawmaterial.service.SorghumLossService;
import com.hvisions.rawmaterial.utils.GenerateCodeUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:高粱损耗
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumLossServiceImpl implements SorghumLossService {

    @Resource
    private SorghumLossMapper sorghumLossMapper;

    @Resource
    private SorghumLossDetailMapper sorghumLossDetailMapper;

    @Resource
    private PurchaseClient purchaseClient;

    @Resource
    private ParamClient paramClient;

    @Resource
    private GenerateCodeUtil generateCodeUtil;


    @Override
    public Page<SorghumLossPageDTO> getSorghumLossPageList(LossPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumLossMapper::getSorghumLossPageList, queryDTO, SorghumLossPageDTO.class);
    }

    @Override
    @Transactional
    public void syncSorghumLoss(SyncLossDTO syncLossDTO) {
        TMpdSorghumLoss sorghumLoss = sorghumLossMapper.selectById(syncLossDTO.getId());
        List<CostLossItemDto> list = new ArrayList<>();
        CostLossItemDto itemDto = new CostLossItemDto();
        itemDto.setItemKey("0010");
//        itemDto.setStgeLoc("1434");
        itemDto.setMaterial(sorghumLoss.getMaterialCode());
        itemDto.setPlant("1100");
//        itemDto.setMoveType("Z51");
        itemDto.setMoveType(syncLossDTO.getMoveType());
        itemDto.setEntryQut(syncLossDTO.getLossQuantity());
        itemDto.setEntryUom(sorghumLoss.getUnit());
        // 损耗同步中心仓库和成本中心配置
        switch (sorghumLoss.getCenter()) {
            case "709":
                itemDto.setKostl("11001130");
                itemDto.setStgeLoc("1369");
                break;
            case "713":
                itemDto.setKostl("11001134");
                itemDto.setStgeLoc("1434");
                break;
            case "718":
                itemDto.setKostl("11001138");
                itemDto.setStgeLoc("1378");
                break;
            case "710":
                itemDto.setKostl("11001131");
                itemDto.setStgeLoc("1370");
                break;
            case "711":
                itemDto.setKostl("11001132");
                itemDto.setStgeLoc("1371");
                break;
            case "712":
                itemDto.setKostl("11001133");
                itemDto.setStgeLoc("1372");
                break;
            case "714":
                itemDto.setKostl("11001121");
                itemDto.setStgeLoc("1374");
                break;
            case "715":
                itemDto.setKostl("11001135");
                itemDto.setStgeLoc("1375");
                break;
            case "716":
                itemDto.setKostl("11001136");
                itemDto.setStgeLoc("1376");
                break;
            case "717":
                itemDto.setKostl("11001137");
                itemDto.setStgeLoc("1377");
                break;
            case "719":
                itemDto.setKostl("11001139");
                itemDto.setStgeLoc("1379");
                break;
            default:
                break;
        }
        itemDto.setGrund("0011");
        itemDto.setSakto("");
        itemDto.setPrctr("11001010");
        list.add(itemDto);
        // sap过账对接
        SyncCostLossDto syncCostLossDto = new SyncCostLossDto();
        syncCostLossDto.setList(list);
        syncCostLossDto.setLossDate(syncLossDTO.getLossDate());
        ResultVO<SapBaseResponseDto> resultVO = purchaseClient.syncCostLoss(syncCostLossDto);
        if(resultVO.getCode()!=200){
            throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
        }
        SapBaseResponseDto response = resultVO.getData();
        if ("S".equals(response.getEsMessage().getMsgty()) || response.getEsMessage().getMsgtx().contains("已处理，请勿重复传输")) {
            // 存储物料凭证
            sorghumLoss.setLossQuantity(syncLossDTO.getLossQuantity());
            sorghumLoss.setState("1");
            sorghumLoss.setUpdateTime(new Date());
            sorghumLoss.setMatDoc(response.getEvOutput().getOutput().get(0).getMblnr()); // 物料凭证编号
            sorghumLoss.setDocYear(response.getEvOutput().getOutput().get(0).getMjahr()); // 物料凭证年度
            sorghumLoss.setPostingTime(syncLossDTO.getLossDate());
            sorghumLossMapper.updateById(sorghumLoss);
        } else {
            // 异常
            throw new BaseKnownException(10000, "SAP损耗同步失败，原因：" + response.getEsMessage().getMsgtx());
        }
    }

    /***
     * @Description 高粱损耗冲销
     *
     * <AUTHOR>
     * @Date 2022-9-6 10:33
     * @param matDocs
     * @return java.lang.Integer
     **/
    @Override
    public Integer writeOff(List<String> matDocs) {
        int res = 0;
        for (String matDoc : matDocs) {
            // 获取已经过账的且过账凭证号为选择的
            List<TMpdSorghumLoss> sorghumLosses = sorghumLossMapper.selectList(Wrappers.<TMpdSorghumLoss>lambdaQuery()
                    .eq(TMpdSorghumLoss::getMatDoc, matDoc)
                    .eq(TMpdSorghumLoss::getDeleted, 0)
                    .eq(TMpdSorghumLoss::getState, "1")
            );
            OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
            headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
            headerDto.setPstingDate(DateUtil.format(sorghumLosses.get(0).getPostingTime(), DatePattern.PURE_DATETIME_PATTERN));
            headerDto.setMjahr(sorghumLosses.get(0).getDocYear());
            headerDto.setMblnr(matDoc);
            ResultVO<SapBaseResponseDto> resultVO = purchaseClient.writeOff(headerDto);
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }
            SapBaseResponseDto response = resultVO.getData();
            if ("S".equals(response.getEsMessage().getMsgty())) {
                sorghumLosses.get(0).setState("0");
                sorghumLosses.get(0).setMatDoc("");
                res += sorghumLossMapper.updateById(sorghumLosses.get(0));
            }

        }
        return res;
    }

    @Override
    public Integer deleteById(Integer id) {
        TMpdSorghumLoss sorghumLoss = sorghumLossMapper.selectById(id);
        if (sorghumLoss != null && "1".equals(sorghumLoss.getState())) {
            return 0;
        }
        sorghumLossMapper.deleteById(id);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("order_id", id);
        sorghumLossDetailMapper.delete(wrapper);
        return 1;
    }

    @Override
    public SorghumLossDetailDTO getSorghumLossDetailByCertificateNumber(String certificateNumber) {
        return sorghumLossMapper.selectDetailByCertificateNumber(certificateNumber);
    }

    @Override
    public String getCenterCodeById(Integer centerId) {
        return sorghumLossMapper.getCenterCodeById(centerId);
    }

    @Override
    public Integer deleteSorghumLossDetail(SorghumLossDetailDTO sorghumLossDetailDTO) {

        int res = 0;
        // 获取损耗比例
        ParamVO paramVO = paramClient.getParam("grainsWastagePercent").getData();
        BigDecimal percent = new BigDecimal(paramVO.getParamValue()).divide(new BigDecimal(1000));
        TMpdSorghumLoss sorghumLoss = sorghumLossMapper.selectById(sorghumLossDetailDTO.getOrderId());
        if (StringUtil.isNotEmpty(sorghumLoss)) {
            sorghumLoss.setUpdateTime(new Date());
            BigDecimal issQty = sorghumLoss.getIssueQuantity().subtract(sorghumLossDetailDTO.getIssueQuantity());
            sorghumLoss.setIssueQuantity(issQty);
            sorghumLoss.setLossQuantity(issQty.multiply(percent));
            res += sorghumLossMapper.updateById(sorghumLoss);
        }
        res += sorghumLossDetailMapper.deleteById(sorghumLossDetailDTO.getId());
        return res;
    }

    @Override
    @Transactional
    public Integer insertOrUpdateSorghumLoss(SorghumLossDTO sorghumLossDTO) {
        int res = 0;
        TMpdSorghumLoss sorghumLoss;
        // 损耗比例
        //ParamVO paramVO = paramClient.getParam("grainsWastagePercent").getData();
        //BigDecimal percent = new BigDecimal(paramVO.getParamValue()).divide(new BigDecimal(1000));
        //TMpdSorghumLossDetail sorghumLossDetail = DtoMapper.convert(sorghumLossDTO, TMpdSorghumLossDetail.class);
        //sorghumLossDetail.setUnit("kg");
        // 新增高粱损耗记录
        /*LambdaQueryWrapper<TMpdSorghumLoss> lossLambdaQueryWrapper = Wrappers.<TMpdSorghumLoss>lambdaQuery()
                .eq(TMpdSorghumLoss::getCenter, sorghumLossDTO.getCenter())
                .eq(TMpdSorghumLoss::getMaterialCode, sorghumLossDTO.getMaterialCode())
                .eq(TMpdSorghumLoss::getState, "0")
                .ge(TMpdSorghumLoss::getEndDate, sorghumLossDTO.getActualBeginTime())
                .le(TMpdSorghumLoss::getStartDate, sorghumLossDTO.getActualBeginTime());*/
        //List<TMpdSorghumLoss> sorghumLossList = sorghumLossMapper.selectList(lossLambdaQueryWrapper);
        /*if (sorghumLossList.size() > 0) {
            sorghumLoss = sorghumLossList.get(0);
            sorghumLoss.setUpdateTime(new Date());
            BigDecimal issQty = sorghumLossDTO.getIssueQuantity().add(sorghumLoss.getIssueQuantity());
            sorghumLoss.setIssueQuantity(issQty);
            sorghumLoss.setLossQuantity(issQty.multiply(percent));
            res += sorghumLossMapper.updateById(sorghumLoss);
        } else {*/
            sorghumLoss = DtoMapper.convert(sorghumLossDTO, TMpdSorghumLoss.class);
            List<Date> dateList = com.hvisions.rawmaterial.utils.DateUtil.getOneMonth24To23(sorghumLossDTO.getActualBeginTime());
            sorghumLoss.setCreateTime(new Date());
            sorghumLoss.setUpdateTime(new Date());
            sorghumLoss.setStartDate(dateList.get(0));
            sorghumLoss.setEndDate(dateList.get(1));
            sorghumLoss.setState("0");
            sorghumLoss.setLossQuantity(sorghumLossDTO.getLossQuantity());
            res += sorghumLossMapper.insert(sorghumLoss);
        /*}*/
        /*sorghumLossDetail.setOrderId(sorghumLoss.getId());
        if (StringUtil.isEmpty(sorghumLossDTO.getOnlyCreate())) {
            res += sorghumLossDetailMapper.insert(sorghumLossDetail);
        }*/
        return res;
    }

    @Override
    @Transactional
    public Integer batchInsertOrUpdateSorghumLoss(List<SorghumLossDTO> sorghumLossDTOS) {

        AtomicInteger res = new AtomicInteger();

        // 获取高粱损耗率
        ParamVO paramVO = paramClient.getParam("grainsWastagePercent").getData();
        BigDecimal percent = new BigDecimal(paramVO.getParamValue()).divide(new BigDecimal(1000));

        Map<String, List<SorghumLossDTO>> materialGroup = sorghumLossDTOS.stream().collect(Collectors.groupingBy(SorghumLossDTO::getMaterialCode));

        materialGroup.forEach((k, v) -> {
            LambdaQueryWrapper<TMpdSorghumLoss> lossLambdaQueryWrapper = Wrappers.<TMpdSorghumLoss>lambdaQuery()
                    .eq(TMpdSorghumLoss::getCenter, v.get(0).getCenter())
                    .eq(TMpdSorghumLoss::getMaterialCode, v.get(0).getMaterialCode())
                    .eq(TMpdSorghumLoss::getState, "0")
                    .ge(TMpdSorghumLoss::getEndDate, v.get(0).getActualBeginTime())
                    .le(TMpdSorghumLoss::getStartDate, v.get(0).getActualBeginTime());
            List<TMpdSorghumLoss> sorghumLossList = sorghumLossMapper.selectList(lossLambdaQueryWrapper);


            TMpdSorghumLoss sorghumLoss;
            BigDecimal issQty = BigDecimal.ZERO;

            if (sorghumLossList.size() > 0) {

                sorghumLoss = sorghumLossList.get(0);
                issQty = sorghumLoss.getIssueQuantity();
                for (SorghumLossDTO sorghumLossDTO : v) {
                    TMpdSorghumLossDetail sorghumLossDetail = DtoMapper.convert(sorghumLossDTO, TMpdSorghumLossDetail.class);
                    issQty = issQty.add(sorghumLossDTO.getIssueQuantity());
                    sorghumLossDetail.setOrderId(sorghumLoss.getId());
                    res.addAndGet(sorghumLossDetailMapper.insert(sorghumLossDetail));

                }
                sorghumLoss.setUpdateTime(new Date());
                sorghumLoss.setIssueQuantity(issQty);
                sorghumLoss.setLossQuantity(issQty.multiply(percent));

            } else {
                sorghumLoss = DtoMapper.convert(v.get(0), TMpdSorghumLoss.class);
                List<Date> dateList = com.hvisions.rawmaterial.utils.DateUtil.getOneMonth24To23(v.get(0).getActualBeginTime());
                sorghumLoss.setCreateTime(new Date());
                sorghumLoss.setUpdateTime(new Date());
                sorghumLoss.setStartDate(dateList.get(0));
                sorghumLoss.setEndDate(dateList.get(1));
                sorghumLoss.setState("0");
                res.addAndGet(sorghumLossMapper.insert(sorghumLoss));
                for (SorghumLossDTO sorghumLossDTO : v) {
                    TMpdSorghumLossDetail sorghumLossDetail = DtoMapper.convert(sorghumLossDTO, TMpdSorghumLossDetail.class);
                    sorghumLossDetail.setOrderId(sorghumLoss.getId());
                    res.addAndGet(sorghumLossDetailMapper.insert(sorghumLossDetail));

                    issQty = issQty.add(sorghumLossDTO.getIssueQuantity());

                }
                sorghumLoss.setIssueQuantity(issQty);
                sorghumLoss.setLossQuantity(issQty.multiply(percent));

            }
            res.addAndGet(sorghumLossMapper.updateById(sorghumLoss));

        });

        return res.get();
    }


    @Override
    public Integer checkAndInsertBySapPost(SapPostVO entity) {
        /**
         * 损耗记录新增逻辑：
         *      709，713，718高粱粉发放过账时生产sap过账记录后 插入损耗记录
         *      其他7个中心，手动新增sap库存调整记录时 ，插入损耗记录
         *
         *      711 714 716 718 损耗物料是 LGL1 和LGL2
         *      其他中心全部LGL3
         *
         *      移动类型311的，，入库仓库为中心，过账日期为期限内的
         */

        int res = 0;
        //709,713,718,717,710,711,712,714,715,716,719
        List<Integer> centerList = new ArrayList<>(Arrays.asList(2, 3, 4, 67, 92, 93, 94, 95, 96, 97, 165));
        List<String> materiaList = new ArrayList<>(Arrays.asList("LGL1", "LGL2", "LGL3"));
        if (entity != null && "1".equals(entity.getState()) && entity.getCertificateNumber() != null
                && entity.getMovementTypeId() == 311 && materiaList.contains(entity.getMaterialName())
                && centerList.contains(entity.getCenterId())) {
            SorghumLossDetailDTO lossDetail = sorghumLossMapper.selectDetailByCertificateNumber(entity.getCertificateNumber());
            if (lossDetail != null) {
                return res;
            }
            SorghumLossDTO sorghumLossDTO = DtoMapper.convert(entity, SorghumLossDTO.class);
            sorghumLossDTO.setUnit("kg");
            sorghumLossDTO.setCenterId(sorghumLossMapper.getCenterIdByWarehouseCode(entity.getTargetWarehouseCode()));
            sorghumLossDTO.setCenter(sorghumLossMapper.getCenterCodeById(sorghumLossDTO.getCenterId()));
            sorghumLossDTO.setIssueQuantity(entity.getWeight());
            sorghumLossDTO.setCertificateNumber(entity.getCertificateNumber());
            sorghumLossDTO.setActualBeginTime(Date.from(entity.getCertificateDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
            res += this.insertOrUpdateSorghumLoss(sorghumLossDTO);
        }
        return res;
    }

    @Override
    public Integer revokeDetailBySapPost(SapPostVO entity) {
        //709,713,718,717,710,711,712,714,715,716,719
        int res = 0;
        List<Integer> centerList = new ArrayList<>(Arrays.asList(2, 3, 4, 67, 92, 93, 94, 95, 96, 97, 165));
        List<String> materiaList = new ArrayList<>(Arrays.asList("LGL1", "LGL2", "LGL3"));
        if (entity != null && "1".equals(entity.getState()) && entity.getCertificateNumber() != null
                && entity.getMovementTypeId() == 311 && materiaList.contains(entity.getMaterialName())
                && centerList.contains(entity.getCenterId())) {
            SorghumLossDetailDTO lossDetail = sorghumLossMapper.selectDetailByCertificateNumber(entity.getCertificateNumber());
            if (lossDetail == null) {
                return res;
            }
            // 获取高粱损耗率
            ParamVO paramVO = paramClient.getParam("grainsWastagePercent").getData();
            BigDecimal percent = new BigDecimal(paramVO.getParamValue()).divide(new BigDecimal(1000));
            SorghumLossDetailDTO sorghumLossDetail = sorghumLossMapper.selectDetailByCertificateNumber(entity.getCertificateNumber());
            TMpdSorghumLoss sorghumLoss;
            // 新增高粱损耗记录
            LambdaQueryWrapper<TMpdSorghumLoss> lossLambdaQueryWrapper = Wrappers.<TMpdSorghumLoss>lambdaQuery()
                    .eq(TMpdSorghumLoss::getCenterId, sorghumLossMapper.getCenterIdByWarehouseCode(entity.getTargetWarehouseCode()))
                    .eq(TMpdSorghumLoss::getMaterialId, sorghumLossDetail.getMaterialId())
                    .eq(TMpdSorghumLoss::getMaterialCode, sorghumLossDetail.getMaterialCode())
                    .eq(TMpdSorghumLoss::getMaterialName, sorghumLossDetail.getMaterialName())
                    .eq(TMpdSorghumLoss::getState, "0")
                    .ge(TMpdSorghumLoss::getEndDate, sorghumLossDetail.getActualBeginTime())
                    .le(TMpdSorghumLoss::getStartDate, sorghumLossDetail.getActualBeginTime());
            List<TMpdSorghumLoss> sorghumLossList = sorghumLossMapper.selectList(lossLambdaQueryWrapper);
            if (sorghumLossList.size() > 0) {
                sorghumLoss = sorghumLossList.get(0);
                sorghumLoss.setUpdateTime(new Date());
                BigDecimal issQty = sorghumLoss.getIssueQuantity().subtract(sorghumLossDetail.getIssueQuantity());
                sorghumLoss.setIssueQuantity(issQty);
                sorghumLoss.setLossQuantity(issQty.multiply(percent));
                res += sorghumLossMapper.updateById(sorghumLoss);
            }
            res += sorghumLossDetailMapper.deleteById(sorghumLossDetail.getId());
        }
        return res;
    }
}
