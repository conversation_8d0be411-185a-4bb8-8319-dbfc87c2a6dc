package com.hvisions.rawmaterial.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapHandleDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapPageDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapQueryDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.detail.ChaffScrapDetailDTO;
import com.hvisions.rawmaterial.dto.production.production.ProductionWeightDataDTO;
import com.hvisions.rawmaterial.dto.production.production.weight.ProductionWeightDTO;
import com.hvisions.rawmaterial.consts.DisqualifiedState;
import com.hvisions.rawmaterial.consts.ProductionWeightState;
import com.hvisions.rawmaterial.dao.BranProductionPlanMapper;
import com.hvisions.rawmaterial.dao.ChaffScrapMapper;
import com.hvisions.rawmaterial.entity.TMpdChaffScrap;
import com.hvisions.rawmaterial.entity.TMpdChaffScrapDetail;
import com.hvisions.rawmaterial.service.ChaffScrapDetailService;
import com.hvisions.rawmaterial.service.ChaffScrapService;
import com.hvisions.rawmaterial.service.ProductionWeightService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.SerialNumberUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 蒸糠报废处理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */
@Service
public class ChaffScrapServiceImpl extends ServiceImpl<ChaffScrapMapper, TMpdChaffScrap> implements ChaffScrapService {

    @Resource
    private ChaffScrapDetailService chaffScrapDetailService;

    @Resource
    private BranProductionPlanMapper branProductionPlanMapper;

    @Resource
    private ProductionWeightService productionWeightService;

    @Resource
    private SerialNumberUtil serialNumberUtil;


    /*
     * @Description: 分页查询蒸康报废
     *
     * <AUTHOR>
     * @param queryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.production.chaff.scrap.ChaffScrapPageDTO>
     */
    @Override
    public Page<ChaffScrapPageDTO> getChaffScrapPageList(ChaffScrapQueryDTO queryDTO) {
        return PageHelperUtil.getPage(baseMapper::getChaffScrapPageList, queryDTO, ChaffScrapPageDTO.class);
    }

    /*
     * @Description: 新增蒸康报废
     *
     * <AUTHOR>
     * @param chaffScrapDTO:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean addChaffScrap(ChaffScrapDTO chaffScrapDTO) {
        TMpdChaffScrap chaffScrap = DtoMapper.convert(chaffScrapDTO, TMpdChaffScrap.class);
        String number = serialNumberUtil.getSerialNumber("t_mpd_chaff_scrap", 3, true);
        chaffScrap.setTaskOrder("ZKBF" + DateUtil.dateFormat(new Date(), "yyMMdd") + number);
        // 获取当天蒸糠计划id
        chaffScrap.setOrderId(branProductionPlanMapper.getBranProductionPlanId());
        chaffScrap.setCreateTime(new Date());
        chaffScrap.setUpdateTime(new Date());
        return this.save(chaffScrap);
    }

    /*
     * @Description: 删除蒸康报废
     *
     * <AUTHOR>
     * @param id: 主键id
     * @return java.lang.Boolean
     */
    @Override
    public Boolean deleteChaffScrap(Integer id) {
        return this.removeById(id);
    }

    /*
     * @Description: 过磅称重数据录入
     *
     * <AUTHOR>
     * @param data:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean weightRecord(ProductionWeightDataDTO data) {
        // 获取蒸康报废任务
        TMpdChaffScrap unqualifiedRefund = this.getOne(new QueryWrapper<TMpdChaffScrap>()
                .eq("task_order", data.getSourceOrder()));
        // 获取详情数据
        TMpdChaffScrapDetail detail = chaffScrapDetailService.getOne(new QueryWrapper<TMpdChaffScrapDetail>()
                .eq("scrap_id", unqualifiedRefund.getId())
                .eq("production_weight_id", data.getId()));
        if ("0".equals(data.getType())) {
            detail.setEnterQuantity(data.getGrossWeight());
        } else {
            detail.setOutQuantity(data.getAppearanceWeight());
            detail.setDealQuantity(data.getNetWeight());
        }
        return chaffScrapDetailService.updateById(detail);
    }

    /*
     * @Description: 蒸康报废处理
     *
     * <AUTHOR>
     * @param handles:
     * @return java.lang.Boolean
     */
    @Override
    @Transactional
    public Boolean handleChaffScrap(ChaffScrapHandleDTO handles) {
        Boolean res = false;
        // 获取蒸康报废信息
        TMpdChaffScrap chaffScrap = this.getById(handles.getId());
        // 判断是否为地磅称重，是生成地磅称重任务
        if (DisqualifiedState.WEIGHBRIDGE.equals(handles.getWeightType())) {
            List<ChaffScrapDetailDTO> details = handles.getLicensePlateNumber().stream().map(item -> {
                ChaffScrapDetailDTO detail = new ChaffScrapDetailDTO();
                detail.setWeightType(handles.getWeightType());
                detail.setScrapId(handles.getId());
                detail.setMaterialId(chaffScrap.getMaterialId());
                detail.setMaterialCode(chaffScrap.getMaterialCode());
                detail.setMaterialName(chaffScrap.getMaterialName());
                detail.setUnit(chaffScrap.getUnit());
                detail.setLicensePlateNumber(item);
                detail.setRecordPeopleId(handles.getRecordPeopleId());
                detail.setRecordPeople(handles.getRecordPeople());
                detail.setRecordTime(new Date());
                detail.setCreateTime(new Date());
                detail.setCreatorId(handles.getCreatorId());
                detail.setUpdateTime(new Date());
                detail.setUpdaterId(handles.getUpdaterId());
                // 判断是否为地磅称重，是生成地磅称重任务
                ProductionWeightDTO productionWeight = new ProductionWeightDTO();
                productionWeight.setType(ProductionWeightState.RE_STEAMING);
                productionWeight.setSourceOrder(chaffScrap.getTaskOrder());
                productionWeight.setMaterialId(chaffScrap.getMaterialId());
                productionWeight.setMaterialCode(chaffScrap.getMaterialCode());
                productionWeight.setMaterialName(chaffScrap.getMaterialName());
                productionWeight.setUnit(chaffScrap.getUnit());
                productionWeight.setLicensePlateNumber(item);
                Integer id = productionWeightService.addProductionWeight(productionWeight);
                if (StringUtil.isEmpty(id) && id == 0) {
                    throw new BaseKnownException(10000, "过磅任务新增失败！");
                }
                detail.setProductionWeightId(id);
                return detail;
            }).collect(Collectors.toList());
            res = chaffScrapDetailService.addChaffScrapDetail(details);
        } else {
            ChaffScrapDetailDTO detail = new ChaffScrapDetailDTO();
            detail.setWeightType(handles.getWeightType());
            detail.setScrapId(handles.getId());
            detail.setDealQuantity(handles.getDealQuantity());
            detail.setMaterialId(chaffScrap.getMaterialId());
            detail.setMaterialCode(chaffScrap.getMaterialCode());
            detail.setMaterialName(chaffScrap.getMaterialName());
            detail.setUnit(chaffScrap.getUnit());
            detail.setRecordPeopleId(handles.getRecordPeopleId());
            detail.setRecordPeople(handles.getRecordPeople());
            detail.setRecordTime(new Date());
            detail.setCreateTime(new Date());
            detail.setCreatorId(handles.getCreatorId());
            detail.setUpdateTime(new Date());
            detail.setUpdaterId(handles.getUpdaterId());
            res = chaffScrapDetailService.save(DtoMapper.convert(detail, TMpdChaffScrapDetail.class));
        }
        if (!res) {
            throw new BaseKnownException(10000, "处理操作异常！");
        }
        // 修改任务为已处理
        chaffScrap.setState(DisqualifiedState.HAS_HANDLE);
        return this.updateById(chaffScrap);
    }

}
