package com.hvisions.rawmaterial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.rawmaterial.dto.production.production.ProductionWeightDataDTO;
import com.hvisions.rawmaterial.dto.production.unqualified.refund.UnqualifiedRefundDTO;
import com.hvisions.rawmaterial.dto.production.unqualified.refund.UnqualifiedRefundHandleDTO;
import com.hvisions.rawmaterial.dto.production.unqualified.refund.UnqualifiedRefundPageDTO;
import com.hvisions.rawmaterial.dto.production.unqualified.refund.UnqualifiedRefundQueryDTO;
import com.hvisions.rawmaterial.entity.TMpdUnqualifiedRefund;
import org.springframework.data.domain.Page;

/**
 * <p>
 * 不合格退货处理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
public interface UnqualifiedRefundService extends IService<TMpdUnqualifiedRefund> {

    Page<UnqualifiedRefundPageDTO> getUnqualifiedRefundPageList(UnqualifiedRefundQueryDTO queryDTO);

    Boolean addUnqualifiedRefund(UnqualifiedRefundDTO UnqualifiedRefundDTO);

    Boolean deleteUnqualifiedRefund(Integer id);

    Boolean weightRecord(ProductionWeightDataDTO data);

    Boolean handleUnqualifiedRefund(UnqualifiedRefundHandleDTO handles);
    
}
