package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.bran.transfer.plan.BranTransferPlanPageDTO;
import com.hvisions.rawmaterial.dto.production.bran.transfer.plan.BranTransferPlanPageQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @description:熟糠传输计划
 * @date 2022/4/22 10:18
 */
public interface BranTransferPlanService {

    Page<BranTransferPlanPageDTO> getBranTransferPlanPageList(BranTransferPlanPageQueryDTO queryDTO);


}
