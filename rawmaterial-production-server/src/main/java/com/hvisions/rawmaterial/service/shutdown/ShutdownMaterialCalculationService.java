package com.hvisions.rawmaterial.service.shutdown;

import com.hvisions.rawmaterial.dto.shutdown.ShutdownMaterialCalculationResponse;
import com.hvisions.rawmaterial.dto.shutdown.FlattenedShutdownMaterialCalculationResponse;

/**
 * 停产物料需求计算服务接口
 * 负责协调各个筒仓的计算逻辑，按照从中心碎料斗向前计算的顺序执行
 */
public interface ShutdownMaterialCalculationService {
    
    /**
     * 计算所有筒仓的停产物料需求数据
     * 按照从中心碎料斗筒仓向前计算的顺序进行处理，并将结果合并为表格结构
     * 
     * @param statisticsStartTime 统计开始时间，可选参数
     * @param materialType 物料类型，可选参数
     * @return 合并表格结构的计算结果
     */
    ShutdownMaterialCalculationResponse calculateAll(String statisticsStartTime, Integer materialType);
    
    /**
     * 计算所有筒仓的停产物料需求数据（平铺结构）
     * 按照从中心碎料斗筒仓向前计算的顺序进行处理，并将结果合并为平铺结构
     * 
     * @param statisticsStartTime 统计开始时间，可选参数
     * @param materialType 物料类型，可选参数
     * @return 平铺结构的计算结果
     */
    FlattenedShutdownMaterialCalculationResponse calculateAllFlattened(String statisticsStartTime, Integer materialType);
}