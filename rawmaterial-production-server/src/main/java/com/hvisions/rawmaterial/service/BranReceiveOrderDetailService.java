package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.PiBaseDataDto;

/**
 * 稻壳工单详情接收自控信息接口
 *
 * <AUTHOR>
 * @since 2023-6-5
 */
public interface BranReceiveOrderDetailService {

    /**
     * 接收稻壳发放任务
     */
    Integer addHuskIssue(PiBaseDataDto piBaseDataDto);

    /**
     * 接收熟糠传输任务
     * @return 返回成功或失败
     */
    Integer addBranTransfer(PiBaseDataDto piBaseDataDto);

    /**
     * 熟糠发放触发接口
     */
    void branIssueTrigger();
}
