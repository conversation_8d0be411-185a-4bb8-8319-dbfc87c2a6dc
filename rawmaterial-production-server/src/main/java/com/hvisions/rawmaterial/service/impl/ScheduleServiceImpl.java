package com.hvisions.rawmaterial.service.impl;

import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.configuration.ScheduleProperties;
import com.hvisions.rawmaterial.constants.ScheduleConstants;
import com.hvisions.rawmaterial.dao.PointRepository;
import com.hvisions.rawmaterial.dao.RestScheduleRepository;
import com.hvisions.rawmaterial.dao.ShutdownDateRepository;
import com.hvisions.rawmaterial.dao.WorkGroupMemberRepository;
import com.hvisions.rawmaterial.dto.*;
import com.hvisions.rawmaterial.entity.schedule.TMpdPoint;
import com.hvisions.rawmaterial.entity.schedule.TMpdRestSchedule;
import com.hvisions.rawmaterial.entity.schedule.TMpdShutdownDate;
import com.hvisions.rawmaterial.entity.schedule.TMpdWorkGroupMember;
import com.hvisions.rawmaterial.service.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 排班表服务实现类
 * @Date: 2025/06/13
 */
@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Autowired
    private RestScheduleRepository restScheduleRepository;

    @Autowired
    private ShutdownDateRepository shutdownDateRepository;

    @Autowired
    private WorkGroupMemberRepository workGroupMemberRepository;

    @Autowired
    private PointRepository pointRepository;

    @Autowired
    private UserClient userClient;

    @Autowired
    private ScheduleProperties scheduleProperties;

    @Override
    public ScheduleResponseDTO getScheduleData(ScheduleQueryDTO query) {
        ScheduleResponseDTO responseDTO = new ScheduleResponseDTO();
        responseDTO.setYear(query.getYear());

        // 获取最大维护月份
        List<Integer> maintainedMonths = findMaintainedMonths(query.getGroupType(), query.getYear());
        if (CollectionUtils.isNotEmpty(maintainedMonths)) {
            int maxMonth = maintainedMonths.stream().mapToInt(Integer::intValue).max().getAsInt();
            responseDTO.setMaxMaintainedMonth(maxMonth);
        }

        // 获取停产日期列表
        List<TMpdShutdownDate> shutdownDates = shutdownDateRepository.findByTypeAndYear(
                query.getGroupType(), query.getYear());
        if (CollectionUtils.isNotEmpty(shutdownDates)) {
            responseDTO.setShutdownDates(shutdownDates);

            // 格式化停产日期为 YYYY-MM-dd 格式
            List<String> formattedDates = shutdownDates.stream()
                .map(date -> String.format("%04d-%02d-%02d",
                    date.getYear(), date.getMonth(), date.getDay()))
                .sorted() // 按日期排序
                .collect(Collectors.toList());
            responseDTO.setFormattedShutdownDates(formattedDates);
        }

        // 获取点位信息数据（取最新的一个）
        TMpdPoint latestPoint = getLatestPoint(query.getGroupType());
        responseDTO.setPointData(latestPoint);

        // 获取休息排班数据并转换为新格式
        List<ScheduleDataDTO> scheduleData = buildScheduleData(query.getGroupType(), query.getYear(), query.getUsername());
        responseDTO.setScheduleData(scheduleData);

        return responseDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean maintainShutdownDates(ShutdownDTO dto) {
        // 验证日期格式并解析日期
        if (CollectionUtils.isEmpty(dto.getShutdownDates())) {
            log.info("停产日期列表为空，班组类型：{}", dto.getGroupType());
            // 即使停产日期为空，也要处理点位信息
            /*if (dto.getPoint() != null) {
                maintainPointData(dto);
            }*/
            return false;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<TMpdShutdownDate> shutdownDates = new ArrayList<>();
        Set<String> processedDates = new HashSet<>(); // 用于去重
        Set<String> processedMonths = new HashSet<>(); // 用于记录涉及的月份

        for (String dateStr : dto.getShutdownDates()) {
            if (dateStr == null || dateStr.trim().isEmpty()) {
                continue;
            }

            try {
                LocalDate localDate = LocalDate.parse(dateStr.trim(), formatter);

                // 避免重复日期
                if (processedDates.contains(dateStr.trim())) {
                    continue;
                }
                processedDates.add(dateStr.trim());

                // 记录涉及的月份（用于后续处理点位信息）
                String monthKey = localDate.getYear() + "-" + String.format("%02d", localDate.getMonthValue());
                processedMonths.add(monthKey);

                // 删除该日期的原有数据
                shutdownDateRepository.deleteByTypeAndYearAndMonthAndDay(
                    dto.getGroupType(),
                    localDate.getYear(),
                    localDate.getMonthValue(),
                    localDate.getDayOfMonth()
                );

                // 创建新的停产日期记录
                TMpdShutdownDate shutdownDate = new TMpdShutdownDate();
                shutdownDate.setType(dto.getGroupType());
                shutdownDate.setYear(localDate.getYear());
                shutdownDate.setMonth(localDate.getMonthValue());
                shutdownDate.setDay(localDate.getDayOfMonth());
                shutdownDate.setIsShutdown(true);
                shutdownDates.add(shutdownDate);

            } catch (DateTimeParseException e) {
                log.error("日期格式错误：{}，期望格式：yyyy-MM-dd", dateStr, e);
                throw new IllegalArgumentException("日期格式错误：" + dateStr + "，期望格式：yyyy-MM-dd");
            }
        }

        // 批量保存新数据
        if (!shutdownDates.isEmpty()) {
            shutdownDateRepository.saveAll(shutdownDates);
            log.info("成功维护停产日期，班组类型：{}，共{}条记录", dto.getGroupType(), shutdownDates.size());
        }

        // 处理点位信息
        if (dto.getPoint() != null && !processedMonths.isEmpty()) {
            maintainPointDataForMonths(dto, processedMonths);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean maintainEmployeeRest(EmployeeRestDTO dto) {
        // 验证日期格式并解析日期
        if (CollectionUtils.isEmpty(dto.getRestDates())) {
            log.info("休息日期列表为空，员工ID：{}，班组类型：{}", dto.getUserId(), dto.getGroupType());
            throw new IllegalArgumentException("休息日期列表为空");
        }

        // 获取最新的班组记录
        List<TMpdWorkGroupMember> currentMembers = workGroupMemberRepository.findCurrentByGroupType(dto.getGroupType());
        if (CollectionUtils.isEmpty(currentMembers)) {
            throw new IllegalArgumentException("未找到班组类型为 " + dto.getGroupType() + " 的有效班组记录");
        }

        TMpdWorkGroupMember currentMember = currentMembers.get(0);
        List<WorkGroupEmployeeDTO> memberList = currentMember.getMemberList();

        // 验证该员工是否在班组成员中
        Optional<WorkGroupEmployeeDTO> employeeOpt = memberList.stream()
                .filter(e -> e.getUserId().equals(dto.getUserId()))
                .findFirst();

        if (!employeeOpt.isPresent()) {
            throw new IllegalArgumentException("该员工" + dto.getUsername() + "未指定班组");
        }

        WorkGroupEmployeeDTO employee = employeeOpt.get();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<TMpdRestSchedule> restSchedules = new ArrayList<>();
        Set<String> processedDates = new HashSet<>(); // 用于去重

        for (String dateStr : dto.getRestDates()) {
            if (dateStr == null || dateStr.trim().isEmpty()) {
                continue;
            }

            try {
                LocalDate localDate = LocalDate.parse(dateStr.trim(), formatter);

                // 避免重复日期
                if (processedDates.contains(dateStr.trim())) {
                    continue;
                }
                processedDates.add(dateStr.trim());

                // 转换为Date对象
                Date scheduleDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

                // 删除该日期的原有数据
                restScheduleRepository.deleteByEmployeeIdAndGroupTypeAndDate(
                    dto.getUserId(), dto.getGroupType(), scheduleDate);

                // 创建休息排班记录
                TMpdRestSchedule restSchedule = new TMpdRestSchedule();
                restSchedule.setEmployeeId(dto.getUserId());
                restSchedule.setEmployeeName(dto.getUsername());
                restSchedule.setGroupType(dto.getGroupType());

                // 设置年月日
                restSchedule.setYear(localDate.getYear());
                restSchedule.setMonth(localDate.getMonthValue());
                restSchedule.setDay(localDate.getDayOfMonth());
                restSchedule.setDate(scheduleDate);

                // 设置班组ID和班次（使用已验证的员工信息）
                Integer groupId = dto.getGroupType() * 10 + employee.getShiftType();
                restSchedule.setGroupId(groupId);
                restSchedule.setShiftType(employee.getShiftType());
                restSchedule.setIsRest(true);

                restSchedules.add(restSchedule);

            } catch (DateTimeParseException e) {
                log.error("日期格式错误：{}，期望格式：yyyy-MM-dd", dateStr, e);
                throw new IllegalArgumentException("日期格式错误：" + dateStr + "，期望格式：yyyy-MM-dd");
            }
        }

        // 批量保存新数据
        if (!restSchedules.isEmpty()) {
            restScheduleRepository.saveAll(restSchedules);
            log.info("成功维护员工休息排班，员工ID：{}，班组类型：{}，共{}条记录",
                    dto.getUserId(), dto.getGroupType(), restSchedules.size());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignWorkGroupMembers(WorkGroupAssignDTO dto) {
        // 获取生效开始日期
        Date effectiveDate = new Date();
        Integer shiftType = dto.getShift();
        Integer groupType = dto.getGroupType();
        
        if (CollectionUtils.isNotEmpty(dto.getUserIds())) {
            // 获取当前班组成员记录
            List<TMpdWorkGroupMember> currentMembers = workGroupMemberRepository.findCurrentByGroupType(groupType);
            TMpdWorkGroupMember currentMember = CollectionUtils.isNotEmpty(currentMembers) ? currentMembers.get(0) : null;
            
            // 如果已有班组成员记录，将其结束日期设置为前一天
            if (currentMember != null) {
                // 创建前一天日期
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(effectiveDate);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                Date previousDay = calendar.getTime();
                
                currentMember.setEffectiveEndDate(previousDay);
                workGroupMemberRepository.save(currentMember);
            }

            // 创建新的班组成员记录
            TMpdWorkGroupMember newMember = new TMpdWorkGroupMember();
            newMember.setGroupType(groupType);
            newMember.setEffectiveStartDate(effectiveDate);
            
            // 获取所有用户信息并创建成员列表
            List<WorkGroupEmployeeDTO> memberList = new ArrayList<>();
            for (Integer userId : dto.getUserIds()) {
                // 获取用户信息
                UserDTO userDTO = null;
                ResultVO<UserDTO> userResult = userClient.getUser(userId);
                if (userResult != null && userResult.isSuccess()) {
                    userDTO = userResult.getData();
                }
                if (userDTO == null) {
                    throw new IllegalArgumentException("未找到用户ID为" + userId + "的用户信息");
                }
                
                // 创建班组成员DTO
                WorkGroupEmployeeDTO employee = new WorkGroupEmployeeDTO();
                employee.setUserId(userId);
                employee.setUsername(userDTO.getUserName());
                employee.setEmployeeCode(userDTO.getUserAccount());
                employee.setShiftType(shiftType);
                
                memberList.add(employee);
            }
            
            // 设置班组成员列表
            newMember.setMemberList(memberList);
            
            // 保存新的班组成员记录
            workGroupMemberRepository.save(newMember);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignWorkGroupMembersBatch(WorkGroupAssignBatchDTO dto) {
        // 获取生效开始日期
        Date effectiveDate = new Date();
        Integer groupType = dto.getGroupType();

        // 校验一个人是否被配置到多个班次
        validateNoDuplicateUserAssignments(dto);

        // 获取当前班组成员记录
        List<TMpdWorkGroupMember> currentMembers = workGroupMemberRepository.findCurrentByGroupType(groupType);
        TMpdWorkGroupMember currentMember = CollectionUtils.isNotEmpty(currentMembers) ? currentMembers.get(0) : null;
        
        // 如果已有班组成员记录，将其结束日期设置为前一天
        if (currentMember != null) {
            // 创建前一天日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(effectiveDate);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date previousDay = calendar.getTime();
            
            currentMember.setEffectiveEndDate(previousDay);
            workGroupMemberRepository.save(currentMember);
        }

        // 创建新的班组成员记录
        TMpdWorkGroupMember newMember = new TMpdWorkGroupMember();
        newMember.setGroupType(groupType);
        newMember.setEffectiveStartDate(effectiveDate);
        
        // 获取所有用户信息并创建成员列表
        List<WorkGroupEmployeeDTO> memberList = new ArrayList<>();
        
        // 处理一班人员
        if (CollectionUtils.isNotEmpty(dto.getFirstShiftUserIds())) {
            addEmployeesToMemberList(memberList, dto.getFirstShiftUserIds(), ScheduleConstants.SHIFT_FIRST);
        }
        
        // 处理二班人员
        if (CollectionUtils.isNotEmpty(dto.getSecondShiftUserIds())) {
            addEmployeesToMemberList(memberList, dto.getSecondShiftUserIds(), ScheduleConstants.SHIFT_SECOND);
        }
        
        // 处理三班人员
        if (CollectionUtils.isNotEmpty(dto.getThirdShiftUserIds())) {
            addEmployeesToMemberList(memberList, dto.getThirdShiftUserIds(), ScheduleConstants.SHIFT_THIRD);
        }
        
        // 设置班组成员列表
        newMember.setMemberList(memberList);
        
        // 保存新的班组成员记录
        workGroupMemberRepository.save(newMember);
        
        return true;
    }
    
    /**
     * 添加员工到成员列表
     * @param memberList 成员列表
     * @param userIds 用户ID列表
     * @param shiftType 班次
     */
    private void addEmployeesToMemberList(List<WorkGroupEmployeeDTO> memberList, List<Integer> userIds, Integer shiftType) {
        for (Integer userId : userIds) {
            // 获取用户信息
            UserDTO userDTO = null;
            ResultVO<UserDTO> userResult = userClient.getUser(userId);
            if (userResult != null && userResult.isSuccess()) {
                userDTO = userResult.getData();
            }
            if (userDTO == null) {
                throw new IllegalArgumentException("未找到用户ID为" + userId + "的用户信息");
            }
            
            // 创建班组成员DTO
            WorkGroupEmployeeDTO employee = new WorkGroupEmployeeDTO();
            employee.setUserId(userId);
            employee.setUsername(userDTO.getUserName());
            employee.setEmployeeCode(userDTO.getUserAccount());
            employee.setShiftType(shiftType);
            
            memberList.add(employee);
        }
    }

    @Override
    public WorkGroupAssignResponse getWorkGroupMembers(Integer groupType) {
        WorkGroupAssignResponse response = new WorkGroupAssignResponse();
        response.setGroupType(groupType);

        // 获取部门ID
        Integer departmentId;
        if (ScheduleConstants.GROUP_TYPE_SORGHUM.equals(groupType)) {
            departmentId = scheduleProperties.getSorghum().getDepartmentId();
        } else if (ScheduleConstants.GROUP_TYPE_CHAFF.equals(groupType)) {
            departmentId = scheduleProperties.getChaff().getDepartmentId();
        } else {
            throw new IllegalArgumentException("不支持的班组类型");
        }

        // 获取所有人员
        List<UserDTO> allUsers = Optional.ofNullable(userClient.getUserListByDepartmentId(departmentId))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .orElse(Collections.emptyList());
        response.setAllEmployees(allUsers);

        // 获取当前班组成员记录
        List<TMpdWorkGroupMember> currentMembers = workGroupMemberRepository.findCurrentByGroupType(groupType);
        if (CollectionUtils.isNotEmpty(currentMembers)) {
            TMpdWorkGroupMember currentMember = currentMembers.get(0);
            List<WorkGroupEmployeeDTO> memberList = currentMember.getMemberList();
            
            if (CollectionUtils.isNotEmpty(memberList)) {
                // 分组处理各个班次的人员
                Map<Integer, List<Integer>> shiftUsersMap = memberList.stream()
                        .collect(Collectors.groupingBy(WorkGroupEmployeeDTO::getShiftType,
                                Collectors.mapping(WorkGroupEmployeeDTO::getUserId, Collectors.toList())));
                
                // 设置一班人员
                if (shiftUsersMap.containsKey(ScheduleConstants.SHIFT_FIRST)) {
                    List<Integer> firstShiftIds = shiftUsersMap.get(ScheduleConstants.SHIFT_FIRST);
                    List<UserDTO> firstShiftUsers = allUsers.stream()
                            .filter(u -> firstShiftIds.contains(u.getId()))
                            .collect(Collectors.toList());
                    response.setFirstShiftEmployees(firstShiftUsers);
                }
                
                // 设置二班人员
                if (shiftUsersMap.containsKey(ScheduleConstants.SHIFT_SECOND)) {
                    List<Integer> secondShiftIds = shiftUsersMap.get(ScheduleConstants.SHIFT_SECOND);
                    List<UserDTO> secondShiftUsers = allUsers.stream()
                            .filter(u -> secondShiftIds.contains(u.getId()))
                            .collect(Collectors.toList());
                    response.setSecondShiftEmployees(secondShiftUsers);
                }
                
                // 设置三班人员
                if (shiftUsersMap.containsKey(ScheduleConstants.SHIFT_THIRD)) {
                    List<Integer> thirdShiftIds = shiftUsersMap.get(ScheduleConstants.SHIFT_THIRD);
                    List<UserDTO> thirdShiftUsers = allUsers.stream()
                            .filter(u -> thirdShiftIds.contains(u.getId()))
                            .collect(Collectors.toList());
                    response.setThirdShiftEmployees(thirdShiftUsers);
                }
            }
        }

        return response;
    }

    @Override
    public WorkGroupAssignResponse getWorkGroupMembersAll(Integer groupType) {
        WorkGroupAssignResponse response = new WorkGroupAssignResponse();
        response.setGroupType(groupType);

        // 获取部门ID
        Integer departmentId;
        if (ScheduleConstants.GROUP_TYPE_SORGHUM.equals(groupType)) {
            departmentId = scheduleProperties.getSorghum().getDepartmentId();
        } else if (ScheduleConstants.GROUP_TYPE_CHAFF.equals(groupType)) {
            departmentId = scheduleProperties.getChaff().getDepartmentId();
        } else {
            throw new IllegalArgumentException("不支持的班组类型");
        }

        // 获取所有人员
        List<UserDTO> allUsers = Optional.ofNullable(userClient.getUserListByDepartmentId(departmentId))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .orElse(Collections.emptyList());
        response.setAllEmployees(allUsers);
        return response;
    }

    @Override
    public Integer getEmployeeShiftByDate(Integer employeeId, Integer groupType, Date date) {
        // 根据日期获取班组成员记录
        List<TMpdWorkGroupMember> members = workGroupMemberRepository.findByGroupTypeAndDate(groupType, date);
        if (CollectionUtils.isEmpty(members)) {
            throw new IllegalArgumentException("该日期未找到有效的班组分配记录");
        }
        
        // 获取第一条记录（最新的记录）
        TMpdWorkGroupMember member = members.get(0);
        List<WorkGroupEmployeeDTO> memberList = member.getMemberList();
        
        // 在成员列表中查找该员工
        Optional<WorkGroupEmployeeDTO> employeeOpt = memberList.stream()
                .filter(e -> e.getUserId().equals(employeeId))
                .findFirst();
                
        if (!employeeOpt.isPresent()) {
            throw new IllegalArgumentException("该员工在指定日期未分配班次");
        }
        
        return employeeOpt.get().getShiftType();
    }

    /**
     * 获取员工当前班次
     * @param employeeId 员工ID
     * @param groupType 班组类型
     * @return 班次
     */
    private Integer getEmployeeShift(Integer employeeId, Integer groupType) {
        // 获取当前班组成员记录
        List<TMpdWorkGroupMember> currentMembers = workGroupMemberRepository.findCurrentByGroupType(groupType);
        if (CollectionUtils.isEmpty(currentMembers)) {
            throw new IllegalArgumentException("当前无有效的班组分配记录");
        }
        
        TMpdWorkGroupMember currentMember = currentMembers.get(0);
        List<WorkGroupEmployeeDTO> memberList = currentMember.getMemberList();
        
        // 在成员列表中查找该员工
        Optional<WorkGroupEmployeeDTO> employeeOpt = memberList.stream()
                .filter(e -> e.getUserId().equals(employeeId))
                .findFirst();
                
        if (!employeeOpt.isPresent()) {
            throw new IllegalArgumentException("该员工未分配班次");
        }
        
        return employeeOpt.get().getShiftType();
    }

    /**
     * 查询已维护的月份
     * @param groupType 班组类型
     * @param year 年份
     * @return 月份列表
     */
    private List<Integer> findMaintainedMonths(Integer groupType, Integer year) {
        List<Integer> months = shutdownDateRepository.findMaintainedMonths(groupType, year);
        return months != null ? months : Collections.emptyList();
    }

    /**
     * 格式化日期为字符串
     * @param date 日期
     * @return 字符串格式日期，例如：2024-06-13
     */
    private String formatDate(Date date) {
        LocalDate localDate = LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return localDate.toString();
    }

    /**
     * 维护点位信息（当停产日期为空时使用）
     * @param dto 停产日期DTO
     */
    private void maintainPointData(ShutdownDTO dto) {
        if (dto.getPoint() == null) {
            return;
        }

        // 获取当前年月
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

        // 删除当前月份的原有点位数据
        pointRepository.deleteByTypeAndYearAndMonth(dto.getGroupType(), currentYear, currentMonth);

        // 创建新的点位记录
        TMpdPoint point = new TMpdPoint();
        point.setType(dto.getGroupType());
        point.setYear(currentYear);
        point.setMonth(currentMonth);
        point.setPoint(dto.getPoint());

        pointRepository.save(point);
        log.info("成功维护点位信息，班组类型：{}，年份：{}，月份：{}，点位：{}",
                dto.getGroupType(), currentYear, currentMonth, dto.getPoint());
    }

    /**
     * 为指定月份维护点位信息
     * @param dto 停产日期DTO
     * @param processedMonths 涉及的月份集合
     */
    private void maintainPointDataForMonths(ShutdownDTO dto, Set<String> processedMonths) {
        if (dto.getPoint() == null || processedMonths.isEmpty()) {
            return;
        }

        List<TMpdPoint> pointsToSave = new ArrayList<>();

        for (String monthKey : processedMonths) {
            String[] parts = monthKey.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 删除该月份的原有点位数据
            pointRepository.deleteByTypeAndYearAndMonth(dto.getGroupType(), year, month);

            // 创建新的点位记录
            TMpdPoint point = new TMpdPoint();
            point.setType(dto.getGroupType());
            point.setYear(year);
            point.setMonth(month);
            point.setPoint(dto.getPoint());

            pointsToSave.add(point);
        }

        if (!pointsToSave.isEmpty()) {
            pointRepository.saveAll(pointsToSave);
            log.info("成功维护点位信息，班组类型：{}，共{}个月份，点位：{}",
                    dto.getGroupType(), pointsToSave.size(), dto.getPoint());
        }
    }

    /**
     * 获取最新的点位信息
     * @param groupType 班组类型
     * @return 最新的点位信息，如果没有则返回null
     */
    private TMpdPoint getLatestPoint(Integer groupType) {
        List<TMpdPoint> points = pointRepository.findLatestByTypeWithLimit(groupType, PageRequest.of(0, 1));
        return points.isEmpty() ? null : points.get(0);
    }

    /**
     * 构建排班数据
     * @param groupType 班组类型
     * @param year 年份
     * @param username 用户名（可选，用于过滤）
     * @return 排班数据列表
     */
    private List<ScheduleDataDTO> buildScheduleData(Integer groupType, Integer year, String username) {
        // 获取休息排班数据
        List<TMpdRestSchedule> restSchedules;
        if (username != null && !username.trim().isEmpty()) {
            // 如果提供了用户名，则按用户名过滤
            restSchedules = restScheduleRepository.findByGroupTypeAndYearAndEmployeeName(groupType, year, username.trim());
        } else {
            // 否则获取所有数据
            restSchedules = restScheduleRepository.findByGroupTypeAndYear(groupType, year);
        }

        if (CollectionUtils.isEmpty(restSchedules)) {
            return Collections.emptyList();
        }

        // 按日期分组
        Map<String, List<TMpdRestSchedule>> dateGroupedSchedules = restSchedules.stream()
                .collect(Collectors.groupingBy(schedule -> formatDate(schedule.getDate())));

        // 构建排班数据列表
        List<ScheduleDataDTO> scheduleDataList = new ArrayList<>();

        for (Map.Entry<String, List<TMpdRestSchedule>> entry : dateGroupedSchedules.entrySet()) {
            String dateKey = entry.getKey();
            List<TMpdRestSchedule> daySchedules = entry.getValue();

            ScheduleDataDTO scheduleData = new ScheduleDataDTO();
            scheduleData.setDay(dateKey);

            // 按班次分组
            Map<Integer, List<TMpdRestSchedule>> shiftGroupedSchedules = daySchedules.stream()
                    .collect(Collectors.groupingBy(TMpdRestSchedule::getShiftType));

            // 处理一班数据
            if (shiftGroupedSchedules.containsKey(ScheduleConstants.SHIFT_FIRST)) {
                List<ScheduleDataDTO.EmployeeDTO> firstShiftEmployees = shiftGroupedSchedules.get(ScheduleConstants.SHIFT_FIRST)
                        .stream()
                        .map(this::convertToEmployeeDTO)
                        .collect(Collectors.toList());
                scheduleData.setFirstShiftRestData(firstShiftEmployees);
            } else {
                scheduleData.setFirstShiftRestData(Collections.emptyList());
            }

            // 处理二班数据
            if (shiftGroupedSchedules.containsKey(ScheduleConstants.SHIFT_SECOND)) {
                List<ScheduleDataDTO.EmployeeDTO> secondShiftEmployees = shiftGroupedSchedules.get(ScheduleConstants.SHIFT_SECOND)
                        .stream()
                        .map(this::convertToEmployeeDTO)
                        .collect(Collectors.toList());
                scheduleData.setSecondShiftRestData(secondShiftEmployees);
            } else {
                scheduleData.setSecondShiftRestData(Collections.emptyList());
            }

            // 处理三班数据
            if (shiftGroupedSchedules.containsKey(ScheduleConstants.SHIFT_THIRD)) {
                List<ScheduleDataDTO.EmployeeDTO> thirdShiftEmployees = shiftGroupedSchedules.get(ScheduleConstants.SHIFT_THIRD)
                        .stream()
                        .map(this::convertToEmployeeDTO)
                        .collect(Collectors.toList());
                scheduleData.setThirdShiftRestData(thirdShiftEmployees);
            } else {
                scheduleData.setThirdShiftRestData(Collections.emptyList());
            }

            scheduleDataList.add(scheduleData);
        }

        // 按日期排序
        scheduleDataList.sort(Comparator.comparing(ScheduleDataDTO::getDay));

        return scheduleDataList;
    }

    /**
     * 转换休息排班记录为员工DTO
     * @param restSchedule 休息排班记录
     * @return 员工DTO
     */
    private ScheduleDataDTO.EmployeeDTO convertToEmployeeDTO(TMpdRestSchedule restSchedule) {
        ScheduleDataDTO.EmployeeDTO employee = new ScheduleDataDTO.EmployeeDTO();
        employee.setUserId(restSchedule.getEmployeeId());
        employee.setUsername(restSchedule.getEmployeeName());
        return employee;
    }

    /**
     * 验证用户是否被重复分配到多个班次
     * @param dto 班组分配批量DTO
     * @throws IllegalArgumentException 如果发现重复分配
     */
    private void validateNoDuplicateUserAssignments(WorkGroupAssignBatchDTO dto) {
        Set<Integer> allUserIds = new HashSet<>();
        List<String> duplicateUsers = new ArrayList<>();

        // 检查一班人员
        if (CollectionUtils.isNotEmpty(dto.getFirstShiftUserIds())) {
            for (Integer userId : dto.getFirstShiftUserIds()) {
                if (!allUserIds.add(userId)) {
                    // 获取用户名用于错误提示
                    String username = getUsernameById(userId);
                    duplicateUsers.add(username + "(ID:" + userId + ")");
                }
            }
        }

        // 检查二班人员
        if (CollectionUtils.isNotEmpty(dto.getSecondShiftUserIds())) {
            for (Integer userId : dto.getSecondShiftUserIds()) {
                if (!allUserIds.add(userId)) {
                    String username = getUsernameById(userId);
                    duplicateUsers.add(username + "(ID:" + userId + ")");
                }
            }
        }

        // 检查三班人员
        if (CollectionUtils.isNotEmpty(dto.getThirdShiftUserIds())) {
            for (Integer userId : dto.getThirdShiftUserIds()) {
                if (!allUserIds.add(userId)) {
                    String username = getUsernameById(userId);
                    duplicateUsers.add(username + "(ID:" + userId + ")");
                }
            }
        }

        // 如果发现重复分配，抛出异常
        if (!duplicateUsers.isEmpty()) {
            String errorMessage = "以下用户被分配到了多个班次，这是不允许的：" + String.join(", ", duplicateUsers);
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果获取失败返回"未知用户"
     */
    private String getUsernameById(Integer userId) {
        try {
            ResultVO<UserDTO> userResult = userClient.getUser(userId);
            if (userResult != null && userResult.isSuccess() && userResult.getData() != null) {
                return userResult.getData().getUserName();
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败，用户ID：{}", userId, e);
        }
        return "未知用户";
    }
}