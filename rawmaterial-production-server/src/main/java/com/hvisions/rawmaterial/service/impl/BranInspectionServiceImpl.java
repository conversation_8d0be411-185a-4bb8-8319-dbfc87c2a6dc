package com.hvisions.rawmaterial.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.rawmaterial.dto.production.bran.inspection.*;
import com.hvisions.rawmaterial.dto.production.bran.inspection.detail.BranInspectionDetailListDTO;
import com.hvisions.rawmaterial.dto.production.bran.inspection.item.BranInspectionItemDTO;
import com.hvisions.rawmaterial.consts.BranInspectionState;
import com.hvisions.rawmaterial.dao.BranInspectionMapper;
import com.hvisions.rawmaterial.entity.TMpdBranInspection;
import com.hvisions.rawmaterial.entity.TMpdBranInspectionDetail;
import com.hvisions.rawmaterial.service.BranInspectionDetailService;
import com.hvisions.rawmaterial.service.BranInspectionItemService;
import com.hvisions.rawmaterial.service.BranInspectionService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.SerialNumberUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 蒸糠质量巡检 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Service
public class BranInspectionServiceImpl extends ServiceImpl<BranInspectionMapper, TMpdBranInspection> implements BranInspectionService {

    @Resource
    private BranInspectionDetailService branInspectionDetailService;

    @Resource
    private BranInspectionItemService branInspectionItemService;

    @Resource
    private SerialNumberUtil serialNumberUtil;


    /*
     * @Description: 分页查询蒸糠质量巡检
     *
     * <AUTHOR>
     * @param pageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.production.bran.inspection.BranInspectionPageDTO>
     */
    @Override
    public Page<BranInspectionPageDTO> getBranInspectionPageList(BranInspectionPageQueryDTO pageQueryDTO) {
        return PageHelperUtil.getPage(baseMapper::getBranInspectionPageList, pageQueryDTO, BranInspectionPageDTO.class);
    }

    @Override
    public BranInspectionPageDTO getBranInspectionByOrderNo(String orderNo) {
        return baseMapper.getBranInspectionByOrderNo(orderNo);
    }

    /*
     * @Description: 新增蒸糠质量巡检任务
     *
     * <AUTHOR>
     * @param branInspectionDTO:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean addBranInspection(BranInspectionDTO branInspectionDTO) {
        TMpdBranInspection branInspection = DtoMapper.convert(branInspectionDTO, TMpdBranInspection.class);
        String number = serialNumberUtil.getSerialNumber("t_mpd_bran_inspection", 3, true);
        branInspection.setOrderNo("XJ" + DateUtil.dateFormat(new Date(), "yyMMdd") + number);
        branInspection.setCreateTime(new Date());
        branInspection.setUpdateTime(new Date());
        boolean res = this.save(branInspection);
        if (res) {
            // 添加详情数据
            branInspectionDetailService.saveBatch(getMachines().stream().map(item -> {
                TMpdBranInspectionDetail detail = new TMpdBranInspectionDetail();
                detail.setInspectionId(branInspection.getId());
                detail.setMachine(item);
                detail.setCreateTime(new Date());
                detail.setCreatorId(branInspectionDTO.getCreatorId());
                detail.setUpdateTime(new Date());
                detail.setUpdaterId(branInspectionDTO.getUpdaterId());
                return detail;
            }).collect(Collectors.toList()));
        }
        return res;
    }

    /*
     * @Description: 修改蒸糠质量巡检任务
     *
     * <AUTHOR>
     * @param branInspectionDTO:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean updateBranInspection(BranInspectionDTO branInspectionDTO) {
        branInspectionDTO.setCreatorId(null);
        branInspectionDTO.setUpdateTime(new Date());
        return this.updateById(DtoMapper.convert(branInspectionDTO, TMpdBranInspection.class));
    }

    /*
     * @Description: 修改蒸糠质量巡检任务
     *
     * <AUTHOR>
     * @param branInspectionDTO:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean reportBranInspection(Integer id) {
        TMpdBranInspection branInspection = new TMpdBranInspection();
        branInspection.setId(id);
        branInspection.setState(BranInspectionState.REPORT);
        return this.updateById(branInspection);
    }

    /*
     * @Description: 删除蒸糠质量巡检任务
     *
     * <AUTHOR>
     * @param id:
     * @return java.lang.Integer
     */
    @Override
    public Boolean deleteBranInspection(Integer id) {
        return this.removeById(id);
    }

    /*
     * @Description: 根据蒸康巡检id获取详情数据
     *
     * <AUTHOR>
     * @param id: 蒸康巡检id
     * @return com.hvisions.purchase.dto.production.bran.inspection.BranInspectionDetailDTO
     */
    @Override
    public BranInspectionDetailDTO getBranInspectionDetail(Integer id) {
        BranInspectionDetailDTO branInspectionDetail = new BranInspectionDetailDTO();
        // 根据id获取蒸康巡检数据
        TMpdBranInspection branInspection = this.getById(id);
        // 获取检查详情
        List<BranInspectionDetailListDTO> details = branInspectionDetailService.getBranInspectionDetailList(id);
        // 获取检查项目说明
        List<BranInspectionItemDTO> items = branInspectionItemService.getBranInspectionItemList();

        branInspectionDetail.setDetails(details);
        branInspectionDetail.setItems(items);
        branInspectionDetail.setInspectionResult(branInspection.getInspectionResult());
        branInspectionDetail.setRemark(branInspection.getRemark());
        return branInspectionDetail;
    }

    /*
     * @Description: 填报
     *
     * <AUTHOR>
     * @param detail:
     * @return java.lang.Boolean
     */
    @Override
    public Boolean workReport(WorkReportDTO workReportDTO) {

        TMpdBranInspection oldBranInspection = baseMapper.selectById(workReportDTO.getId());
        if (BranInspectionState.FINISH.equals(oldBranInspection.getState())) {
            throw new BaseKnownException(10000, "该任务已填报！");
        }
        String imageId = StringUtil.isNotEmpty(workReportDTO.getImageIds()) ? String.join(",", workReportDTO.getImageIds()) : null;
        TMpdBranInspection branInspection = DtoMapper.convert(workReportDTO, TMpdBranInspection.class);
        branInspection.setState(BranInspectionState.FINISH);
        branInspection.setInspectionFinishTime(new Date());
        branInspection.setImageId(imageId);
        branInspection.setCreatorId(null);
        branInspection.setUpdateTime(new Date());
        boolean res = this.updateById(branInspection);
        if (res) {
            List<TMpdBranInspectionDetail> collect = workReportDTO.getDetails()
                    .stream().map(item -> {
                        TMpdBranInspectionDetail detail = new TMpdBranInspectionDetail();
                        detail.setId(item.getId());
                        detail.setWhetherRun(item.getWhetherRun());
                        detail.setDetailResult(item.getDetailResult());
                        detail.setAbnormalDescription(item.getAbnormalDescription());
                        detail.setImprovementMeasures(item.getImprovementMeasures());
                        detail.setUpdaterId(workReportDTO.getUpdaterId());
                        detail.setUpdateTime(new Date());
                        return detail;
                    }).collect(Collectors.toList());
            branInspectionDetailService.updateBatchById(collect);
        }
        return res;
    }

    // 摊晒机集合数据
    public List<String> getMachines() {
        List<String> machines = new ArrayList<>();
        machines.add("1#");
        machines.add("2#");
        machines.add("3#");
        machines.add("4#");
        machines.add("5#");
        return machines;
    }

}
