package com.hvisions.rawmaterial.service.inventory;

import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryDTO;
import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryQueryDTO;
import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryStatsDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 原辅料库存服务接口
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface RawMaterialInventoryService {

    /**
     * 分页查询原辅料库存
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<RawMaterialInventoryDTO> getInventoryByPage(RawMaterialInventoryQueryDTO queryDTO);

    /**
     * 获取所有原辅料库存（不分页）
     * @param queryDTO 查询条件
     * @return 库存列表
     */
    List<RawMaterialInventoryDTO> getAllInventory(RawMaterialInventoryQueryDTO queryDTO);

    /**
     * 根据物料编码获取库存详情
     * @param materialCode 物料编码
     * @param warehouseCode 仓库编码
     * @return 库存详情
     */
    RawMaterialInventoryDTO getInventoryDetail(String materialCode, String warehouseCode);

    /**
     * 获取库存统计信息
     * @return 统计信息
     */
    RawMaterialInventoryStatsDTO getInventoryStats();

    /**
     * 同步库存数据（从MES和SAP系统）
     * @return 同步结果
     */
    Boolean syncInventoryData();

    /**
     * 导出库存数据
     * @param queryDTO 查询条件
     * @return 导出文件路径
     */
    String exportInventoryData(RawMaterialInventoryQueryDTO queryDTO);
}
