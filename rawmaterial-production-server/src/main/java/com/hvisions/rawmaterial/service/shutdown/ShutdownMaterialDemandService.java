package com.hvisions.rawmaterial.service.shutdown;

import com.hvisions.rawmaterial.dto.shutdown.ShutdownMaterialDemandDTO;
import com.hvisions.rawmaterial.dto.shutdown.ShutdownMaterialDemandQuery;
import com.hvisions.rawmaterial.dto.shutdown.ShutdownMaterialDemandQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 停产物料需求提报Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface ShutdownMaterialDemandService {

    /**
     * 分页查询停产物料需求提报
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<ShutdownMaterialDemandDTO> findByCondition(ShutdownMaterialDemandQueryDTO queryDTO);

    /**
     * 根据ID查询停产物料需求提报
     *
     * @param id 主键ID
     * @return 停产物料需求提报DTO
     */
    ShutdownMaterialDemandDTO findById(Integer id);

    /**
     * 新增停产物料需求提报
     *
     * @param demandDTO 停产物料需求提报DTO
     * @return 新增后的DTO
     */
    ShutdownMaterialDemandDTO create(ShutdownMaterialDemandDTO demandDTO);

    /**
     * 更新停产物料需求提报
     *
     * @param demandDTO 停产物料需求提报DTO
     * @return 更新后的DTO
     */
    ShutdownMaterialDemandDTO update(ShutdownMaterialDemandDTO demandDTO);

    /**
     * 删除停产物料需求提报
     *
     * @param id 主键ID
     */
    void delete(Integer id);

    /**
     * 报送需求
     *
     * @param id 需求ID
     * @return 操作结果
     */
    String submitDemand(Integer id);

    /**
     * 批量报送需求
     *
     * @param ids 需求ID列表
     * @return 操作结果
     */
    String batchSubmitDemand(List<Integer> ids);

    /**
     * 接收需求
     *
     * @param id 需求ID
     * @return 操作结果
     */
    String receiveDemand(Integer id);

    /**
     * 批量接收需求
     *
     * @param ids 需求ID列表
     * @return 操作结果
     */
    String batchReceiveDemand(List<Integer> ids);

    /**
     * 驳回需求
     *
     * @param id 需求ID
     * @param reason 驳回原因
     * @return 操作结果
     */
    String rejectDemand(Integer id, String reason);

    /**
     * 获取物料需求计算数据（用于新增时的数据预填充）
     *
     * @param centerCode 中心编码
     * @param workshopCode 车间编码
     * @param demandType 需求类型
     * @return 物料需求计算数据
     */
    List<ShutdownMaterialDemandQuery> getMaterialDemandData(String centerCode, String workshopCode, String demandType);

    /**
     * 生成需求单号
     *
     * @return 需求单号
     */
    String generateDemandNo();

    /**
     * 根据用户权限过滤查询条件
     *
     * @param queryDTO 查询条件
     * @param userDepartment 用户部门
     * @return 过滤后的查询条件
     */
    ShutdownMaterialDemandQueryDTO filterQueryByPermission(ShutdownMaterialDemandQueryDTO queryDTO, String userDepartment);
}
