package com.hvisions.rawmaterial.service.difference;

import com.alibaba.fastjson.JSON;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.advice.UserAuditorAware;
import com.hvisions.rawmaterial.consts.InventoryDifferenceConstants;
import com.hvisions.rawmaterial.dao.InventoryDifferenceDetailRepository;
import com.hvisions.rawmaterial.dao.InventoryDifferenceRepository;
import com.hvisions.rawmaterial.dao.difference.InventoryDifferenceMapper;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceDetailDTO;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifference;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理服务实现类
 * @Date: 2024/07/14
 */
@Slf4j
@Service
public class InventoryDifferenceServiceImpl implements InventoryDifferenceService {

    @Autowired
    private InventoryDifferenceRepository inventoryDifferenceRepository;

    @Autowired
    private InventoryDifferenceDetailRepository inventoryDifferenceDetailRepository;

    @Resource
    private InventoryDifferenceMapper inventoryDifferenceMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Override
    public Page<InventoryDifferenceDTO> queryInventoryDifferences(InventoryDifferenceQueryDTO queryDTO) {
        log.debug("Querying inventory differences with cache key: inventory-difference:{}", queryDTO.hashCode());
        return PageHelperUtil.getPage(inventoryDifferenceMapper::queryInventoryDifferences, queryDTO, InventoryDifferenceDTO.class);
    }

    @Override
    public InventoryDifferenceDTO getInventoryDifferenceById(Integer id) {
        log.debug("Getting inventory difference by id with cache: {}", id);
        InventoryDifferenceDTO dto = inventoryDifferenceMapper.getInventoryDifferenceById(id);
        if (dto == null) {
            throw new IllegalArgumentException("库存差异处理记录不存在");
        }
        
        // 实时查询明细列表
        Date statisticsStartDate = dto.getStatisticsStartDate();
        Date statisticsEndDate = new Date();
        //根据物料编码和统计时间范围查询各个中心的发料数量总和
        List<TMpdInventoryDifferenceDetail> detailEntities = new ArrayList<>();
        if (dto.getMaterialType().equals("稻壳")){
            detailEntities = inventoryDifferenceMapper.getRiceIssuedInventoryDifferenceCenters(
                    dto.getMaterialCode(), statisticsStartDate, statisticsEndDate);
        } else if (dto.getMaterialType().equals("高粱")) {
            detailEntities = inventoryDifferenceMapper.getSorghumIssuedInventoryDifferenceCenters(
                    dto.getMaterialCode(), statisticsStartDate, statisticsEndDate);
        }
        //获取发料数量 计算发料数量在各个中心占有比例公式 发放量/sum(发放量)
        if (CollectionUtils.isNotEmpty(detailEntities)) {
            // 计算总发放量
            BigDecimal totalIssuedQuantity = dto.getIssuedQuantity() == null ? BigDecimal.ZERO : dto.getIssuedQuantity();

            // 计算各中心的分摊比例和分摊量
            List<InventoryDifferenceDetailDTO> detailList = detailEntities.stream()
                    .map(detail -> {
                        InventoryDifferenceDetailDTO detailDTO = convertDetailToDTO(detail);

                        // 计算分摊比例：发放量/sum(发放量)
                        if (totalIssuedQuantity.compareTo(BigDecimal.ZERO) > 0 && detail.getActualQuantity() != null) {
                            BigDecimal shareRatio = detail.getActualQuantity()
                                    .divide(totalIssuedQuantity, 4, RoundingMode.HALF_UP);
                            detailDTO.setShareRatio(shareRatio);

                            // 计算分摊量：差异数量 * 分摊比例
                            if (dto.getDifferenceQuantity() != null) {
                                BigDecimal shareQuantity = dto.getDifferenceQuantity()
                                        .multiply(shareRatio)
                                        .setScale(3, RoundingMode.HALF_UP);
                                detailDTO.setShareQuantity(shareQuantity);
                            }
                        } else {
                            detailDTO.setShareRatio(BigDecimal.ZERO);
                            detailDTO.setShareQuantity(BigDecimal.ZERO);
                        }

                        return detailDTO;
                    })
                    .collect(Collectors.toList());

            dto.setDetailList(detailList);

            log.debug("计算完成，物料{}共{}个中心，总发放量：{}",
                    dto.getMaterialCode(), detailList.size(), totalIssuedQuantity);
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateDifferenceRecords(InventoryDifferenceDTO dto) {
        log.info("开始生成库存差异处理记录");
        
        try {
            // 检查是否已存在待处理的记录
            Optional<TMpdInventoryDifference> existingOpt = inventoryDifferenceRepository
                    .findByMaterialCodeAndErpWarehouseCodeAndStatus(
                            dto.getMaterialCode(),
                            dto.getErpWarehouseCode(),
                            InventoryDifferenceConstants.STATUS_PENDING);
            log.info("检查是否已存在待处理的记录:{}", existingOpt.isPresent());
            if (existingOpt.isPresent()) {
                log.debug("物料{}仓库{}已存在待处理记录，跳过", dto.getMaterialCode(), dto.getErpWarehouseCode());
                throw new IllegalArgumentException("已存在待处理的记录");
            }

            // 获取最新的已处理记录，确定统计开始日期
            List<TMpdInventoryDifference> latestRecords = inventoryDifferenceRepository
                    .findLatestByMaterialAndWarehouse(
                            dto.getMaterialCode(),
                            dto.getErpWarehouseCode(),
                            PageRequest.of(0, 1));
            log.info("获取最新的已处理记录:{}", JSON.toJSONString(latestRecords));
            Date statisticsStartDate;
            BigDecimal mesInitialStock = BigDecimal.ZERO;

            if (CollectionUtils.isNotEmpty(latestRecords)) {
                TMpdInventoryDifference latestRecord = latestRecords.get(0);
                statisticsStartDate = latestRecord.getStatisticsEndDate();
                mesInitialStock = latestRecord.getMesCurrentStock();
            } else {
                // 如果没有历史记录，取用户填的开始时间
                statisticsStartDate = dto.getStatisticsStartDate();
                mesInitialStock = dto.getMesInitialStock();
            }

            // 创建新的差异处理记录
            TMpdInventoryDifference difference = createDifferenceRecord(dto, statisticsStartDate, mesInitialStock);
            log.info("生成差异处理记录:{}", JSON.toJSONString(difference));
            inventoryDifferenceRepository.save(difference);

            log.debug("生成差异处理记录：物料{}，仓库{}，差异数量{}",
                    dto.getMaterialCode(), dto.getErpWarehouseCode(), difference.getDifferenceQuantity());
            return difference.getId();
            
        } catch (Exception e) {
            log.error("生成库存差异处理记录失败", e);
            throw new RuntimeException("生成库存差异处理记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processDifference(InventoryDifferenceProcessDTO processDTO) {
        log.info("开始处理库存差异，差异ID：{}", processDTO.getId());

        try {
            // 查询差异处理记录
            Optional<TMpdInventoryDifference> differenceOpt = inventoryDifferenceRepository.findById(processDTO.getId());
            if (!differenceOpt.isPresent()) {
                throw new IllegalArgumentException("库存差异处理记录不存在");
            }

            TMpdInventoryDifference difference = differenceOpt.get();
            if (!InventoryDifferenceConstants.STATUS_PENDING.equals(difference.getStatus())) {
                throw new IllegalArgumentException("该差异记录已处理，无法重复处理");
            }

            // 获取当前用户信息
            Integer userId = userAuditorAware.getUserId();
            log.info("开始处理库存差异，获取到当前登录用户，用户id：{}", userId);
            String userName = userAuditorAware.getUserNameById(userId);

            // 更新差异处理记录
            difference.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
            difference.setStatisticsEndDate(new Date());
            difference.setProcessTime(new Date());
            difference.setProcessRemark(processDTO.getProcessRemark());
            difference.setProcessorId(userId);
            difference.setProcessorName(userName);
            difference.setReportDate(processDTO.getReportDate());
            difference.setReportQuantity(processDTO.getReportQuantity());

            inventoryDifferenceRepository.save(difference);

            // 保存处理明细
            if (CollectionUtils.isNotEmpty(processDTO.getDetailList())) {
                List<TMpdInventoryDifferenceDetail> detailEntities = processDTO.getDetailList().stream()
                        .map(detailDTO -> {
                            TMpdInventoryDifferenceDetail detail = new TMpdInventoryDifferenceDetail();
                            detail.setActualQuantity(detail.getActualQuantity() == null ? BigDecimal.ZERO : detail.getActualQuantity());
                            BeanUtils.copyProperties(detailDTO, detail);
                            detail.setDifferenceId(processDTO.getId());
                            detail.setProcessTime(new Date());
                            return detail;
                        })
                        .collect(Collectors.toList());

                inventoryDifferenceDetailRepository.saveAll(detailEntities);
            }

            log.info("库存差异处理完成，差异ID：{}", processDTO.getId());
            // 生成新的差异记录
            InventoryDifferenceDTO info = new InventoryDifferenceDTO();
            BeanUtils.copyProperties(difference, info);
            log.info("开始生成新的差异处理记录，差异信息：{}", JSON.toJSONString( info));
            generateDifferenceRecords(info);
            return true;

        } catch (Exception e) {
            log.error("处理库存差异失败，差异ID：{}", processDTO.getId(), e);
            throw new RuntimeException("处理库存差异失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer syncSapStock(List<SapStockSyncDTO> syncDTOList) {
        log.info("开始同步SAP库存，同步数量：{}", syncDTOList.size());

        try {
            int syncCount = 0;

            for (SapStockSyncDTO syncDTO : syncDTOList) {
                // 查找对应的待处理差异记录
                Optional<TMpdInventoryDifference> differenceOpt = inventoryDifferenceRepository
                        .findByMaterialCodeAndErpWarehouseCodeAndStatus(
                                syncDTO.getMaterialCode(),
                                syncDTO.getErpWarehouseCode(),
                                InventoryDifferenceConstants.STATUS_PENDING);

                if (differenceOpt.isPresent()) {
                    TMpdInventoryDifference difference = differenceOpt.get();
                    difference.setSapStock(syncDTO.getSapStock());
                    difference.setSapSyncTime(syncDTO.getSyncTime());

                    // 重新计算差异数量
                    calculateDifferenceQuantity(difference);

                    inventoryDifferenceRepository.save(difference);
                    syncCount++;

                    log.debug("同步SAP库存：物料{}，仓库{}，库存{}",
                            syncDTO.getMaterialCode(), syncDTO.getErpWarehouseCode(), syncDTO.getSapStock());
                }
            }

            log.info("SAP库存同步完成，成功同步{}条记录", syncCount);
            return syncCount;

        } catch (Exception e) {
            log.error("同步SAP库存失败", e);
            throw new RuntimeException("同步SAP库存失败：" + e.getMessage());
        }
    }

    @Override
    public Boolean syncSingleSapStock(String materialCode, String erpWarehouseCode) {
        log.info("开始同步单个物料SAP库存，物料：{}，仓库：{}", materialCode, erpWarehouseCode);

        try {
            // 这里应该调用SAP接口获取实时库存
            // 暂时模拟返回数据
            SapStockSyncDTO syncDTO = new SapStockSyncDTO();
            syncDTO.setMaterialCode(materialCode);
            syncDTO.setErpWarehouseCode(erpWarehouseCode);
            syncDTO.setSapStock(BigDecimal.ZERO); // 实际应该从SAP获取
            syncDTO.setSyncTime(new Date());

            List<SapStockSyncDTO> syncList = Collections.singletonList(syncDTO);
            Integer syncCount = syncSapStock(syncList);

            return syncCount > 0;

        } catch (Exception e) {
            log.error("同步单个物料SAP库存失败，物料：{}，仓库：{}", materialCode, erpWarehouseCode, e);
            return false;
        }
    }

    @Override
    public List<InventoryDifferenceDTO> exportDifferences(InventoryDifferenceQueryDTO queryDTO) {
        // 设置大的页面大小来获取所有数据
        queryDTO.setPage(1);
        queryDTO.setPageSize(10000);

        Page<InventoryDifferenceDTO> page = queryInventoryDifferences(queryDTO);
        return page.getContent();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchProcessDifferences(List<InventoryDifferenceProcessDTO> processDTOList) {
        int processedCount = 0;

        for (InventoryDifferenceProcessDTO processDTO : processDTOList) {
            try {
                Boolean result = processDifference(processDTO);
                if (result) {
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("批量处理差异失败，差异ID：{}", processDTO.getId(), e);
            }
        }

        log.info("批量处理差异完成，成功处理{}条记录", processedCount);
        return processedCount;
    }

    /**
     * 创建差异处理记录
     * @param info 物料仓库信息
     * @param statisticsStartDate 统计开始日期
     * @param mesInitialStock MES期初库存
     * @return 差异处理记录
     */
    private TMpdInventoryDifference createDifferenceRecord(InventoryDifferenceDTO info, Date statisticsStartDate, BigDecimal mesInitialStock) {
        TMpdInventoryDifference difference = new TMpdInventoryDifference();
        difference.setMaterialType(info.getMaterialType());
        difference.setMaterialCode(info.getMaterialCode());
        difference.setMaterialName(info.getMaterialName());
        difference.setErpWarehouseCode(info.getErpWarehouseCode());
        difference.setErpWarehouseName(info.getErpWarehouseName());
        difference.setDepartment(InventoryDifferenceConstants.DEFAULT_DEPARTMENT);
        difference.setUnit(info.getUnit());
        difference.setStatisticsStartDate(statisticsStartDate);
        difference.setStatus(InventoryDifferenceConstants.STATUS_PENDING);

        Date currentDate = new Date();

        // 获取MES当前库存
        // 需要计算所有中心系统同步的数据
        BigDecimal mesCurrentStock = inventoryDifferenceMapper.getMesCurrentStock(info.getMaterialCode(), statisticsStartDate, currentDate);
        difference.setMesCurrentStock(mesCurrentStock != null ? mesCurrentStock : BigDecimal.ZERO);
        difference.setMesInitialStock(mesInitialStock);

        // 获取地磅收货数量
        BigDecimal weighbridgeQuantity = inventoryDifferenceMapper.getWeighbridgeReceiptQuantity(
                info.getMaterialCode(), statisticsStartDate, currentDate);
        difference.setWeighbridgeReceiptQuantity(weighbridgeQuantity != null ? weighbridgeQuantity : BigDecimal.ZERO);

        // 获取固废提报数量
        BigDecimal solidWasteQuantity = inventoryDifferenceMapper.getSolidWasteQuantity(
                info.getMaterialCode(), statisticsStartDate, currentDate);
        difference.setSolidWasteQuantity(solidWasteQuantity != null ? solidWasteQuantity : BigDecimal.ZERO);

        // 获取发料数量
        //根据物料类型去查询发料数量
        BigDecimal issuedQuantity = BigDecimal.ZERO;
        if (info.getMaterialType().equals("稻壳")){
            issuedQuantity = inventoryDifferenceMapper.getRiceIssuedQuantity(
                    info.getMaterialCode(), statisticsStartDate, currentDate);
        } else if (info.getMaterialType().equals("高粱")) {
            issuedQuantity = inventoryDifferenceMapper.getSorghumIssuedQuantity(
                    info.getMaterialCode(), statisticsStartDate, currentDate);
        }
        difference.setIssuedQuantity(issuedQuantity != null ? issuedQuantity : BigDecimal.ZERO);

        // 设置SAP库存（默认为0，需要手动同步）
        difference.setSapStock(BigDecimal.ZERO);

        // 计算差异数量
        calculateDifferenceQuantity(difference);

        return difference;
    }

    /**
     * 计算差异数量
     * @param difference 差异处理记录
     */
    private void calculateDifferenceQuantity(TMpdInventoryDifference difference) {
        // 差异数量 = MES当前库存 - (MES期初库存 + 地磅收货数量 - 固废提报数量 - 发料数量)
        BigDecimal theoreticalStock = difference.getMesInitialStock()
                .add(difference.getWeighbridgeReceiptQuantity())
                .subtract(difference.getSolidWasteQuantity())
                .subtract(difference.getIssuedQuantity());

        BigDecimal differenceQuantity = difference.getMesCurrentStock().subtract(theoreticalStock);
        difference.setDifferenceQuantity(differenceQuantity);
    }

    @Override
    public List<MaterialQuaryDTO> getMaterialWarehouseInfo() {
        return inventoryDifferenceMapper.getMaterialWarehouseInfo();
    }

    @Override
    public InventoryDifferenceDTO getInventoryDifferenceByIdDetails(Integer id) {
        InventoryDifferenceDTO dto = inventoryDifferenceMapper.getInventoryDifferenceById(id);
        if (dto == null) {
            throw new IllegalArgumentException("库存差异处理记录不存在");
        }

        // 查询明细列表
        List<TMpdInventoryDifferenceDetail> detailEntities = inventoryDifferenceDetailRepository.findByDifferenceId(id);
        if (CollectionUtils.isNotEmpty(detailEntities)) {
            // 计算各中心的分摊比例和分摊量
            List<InventoryDifferenceDetailDTO> detailList = detailEntities.stream()
                    .map(detail -> {
                        InventoryDifferenceDetailDTO detailDTO = convertDetailToDTO(detail);
                        return detailDTO;
                    })
                    .collect(Collectors.toList());

            dto.setDetailList(detailList);
        }

        return dto;
    }

    /**
     * 转换明细实体为DTO
     * @param detail 明细实体
     * @return 明细DTO
     */
    private InventoryDifferenceDetailDTO convertDetailToDTO(TMpdInventoryDifferenceDetail detail) {
        InventoryDifferenceDetailDTO dto = new InventoryDifferenceDetailDTO();
        BeanUtils.copyProperties(detail, dto);

        // 映射发料数量字段（actualQuantity -> issuedQuantity）
        if (detail.getActualQuantity() != null) {
            dto.setIssuedQuantity(detail.getActualQuantity());
        }

        return dto;
    }
}
