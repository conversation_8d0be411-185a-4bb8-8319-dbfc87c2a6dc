package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.bran.production.plan.BranProductionPlanPageDTO;
import com.hvisions.rawmaterial.dto.production.bran.production.plan.BranProductionPlanPageQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @description:熟糠生产计划
 * @date 2022/4/22 10:18
 */
public interface BranProductionPlanService {

    Page<BranProductionPlanPageDTO> getBranProductionPlanPageList(BranProductionPlanPageQueryDTO queryDTO);


}
