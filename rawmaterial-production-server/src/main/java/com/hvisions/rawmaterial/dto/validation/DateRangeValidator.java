package com.hvisions.rawmaterial.dto.validation;

import org.springframework.util.ReflectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 日期范围验证器
 * @Date: 2024/07/14
 */
public class DateRangeValidator implements ConstraintValidator<DateRange, Object> {

    private String startField;
    private String endField;

    @Override
    public void initialize(DateRange constraintAnnotation) {
        this.startField = constraintAnnotation.startField();
        this.endField = constraintAnnotation.endField();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        try {
            Field startFieldObj = ReflectionUtils.findField(value.getClass(), startField);
            Field endFieldObj = ReflectionUtils.findField(value.getClass(), endField);

            if (startFieldObj == null || endFieldObj == null) {
                return true;
            }

            ReflectionUtils.makeAccessible(startFieldObj);
            ReflectionUtils.makeAccessible(endFieldObj);

            Date startDate = (Date) ReflectionUtils.getField(startFieldObj, value);
            Date endDate = (Date) ReflectionUtils.getField(endFieldObj, value);

            if (startDate == null || endDate == null) {
                return true;
            }

            return !startDate.after(endDate);
        } catch (Exception e) {
            return false;
        }
    }
}