package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 批量班组人员分配DTO
 * @Date: 2024/06/13
 */
@Data
@ApiModel(value = "批量班组人员分配信息")
public class WorkGroupAssignBatchDTO {

    @ApiModelProperty(value = "班组类型：1-高粱组，2-稻壳组")
    @NotNull(message = "班组类型不能为空")
    private Integer groupType;

    @ApiModelProperty(value = "一班人员ID列表")
    private List<Integer> firstShiftUserIds;
    
    @ApiModelProperty(value = "二班人员ID列表")
    private List<Integer> secondShiftUserIds;
    
    @ApiModelProperty(value = "三班人员ID列表")
    private List<Integer> thirdShiftUserIds;

} 