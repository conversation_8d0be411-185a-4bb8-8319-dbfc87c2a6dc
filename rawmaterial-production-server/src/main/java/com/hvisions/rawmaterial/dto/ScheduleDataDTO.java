package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 排班数据DTO
 * @Date: 2024/06/13
 */
@Data
@ApiModel(value = "排班数据信息")
public class ScheduleDataDTO {

    @ApiModelProperty(value = "日期，格式为YYYY-MM-dd")
    private String day;

    @ApiModelProperty(value = "一班休息人员列表")
    private List<EmployeeDTO> firstShiftRestData;

    @ApiModelProperty(value = "二班休息人员列表")
    private List<EmployeeDTO> secondShiftRestData;

    @ApiModelProperty(value = "三班休息人员列表")
    private List<EmployeeDTO> thirdShiftRestData;

    @Data
    @ApiModel(value = "员工信息")
    public static class EmployeeDTO {
        
        @ApiModelProperty(value = "用户ID")
        private Integer userId;
        
        @ApiModelProperty(value = "用户姓名")
        private String username;
    }
}
