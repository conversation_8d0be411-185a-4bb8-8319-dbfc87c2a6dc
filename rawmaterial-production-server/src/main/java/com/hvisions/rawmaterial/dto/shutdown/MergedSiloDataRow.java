package com.hvisions.rawmaterial.dto.shutdown;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 合并筒仓数据行模型
 * 表示合并表格结构中的一行数据，包含一个车间在所有筒仓类型中的完整信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MergedSiloDataRow {
    
    /**
     * 车间ID
     */
    @NotBlank(message = "车间ID不能为空")
    private String workshopId;
    
    /**
     * 车间名称
     */
    @NotBlank(message = "车间名称不能为空")
    private String workshopName;
    
    /**
     * 前处理存储仓列数据
     */
    @Valid
    private SiloColumnData preProcessingStorage;
    
    /**
     * 后处理暂存仓列数据
     */
    @Valid
    private SiloColumnData postProcessingTemp;
    
    /**
     * 中心缓存仓列数据
     */
    @Valid
    private SiloColumnData centralBuffer;
    
    /**
     * 中心碎料仓列数据
     */
    @Valid
    private SiloColumnData centralMaterial;
    
    /**
     * 中心碎料斗筒仓列数据（包含详细信息）
     */
    @Valid
    private CentralMaterialHopperColumnData centralMaterialHopper;
    
    /**
     * 默认构造函数
     */
    public MergedSiloDataRow() {
        // 初始化为空，在数据合并时根据实际数据填充
    }
    
    /**
     * 带参数构造函数
     */
    public MergedSiloDataRow(String workshopId, String workshopName) {
        this.workshopId = workshopId;
        this.workshopName = workshopName;
    }
}