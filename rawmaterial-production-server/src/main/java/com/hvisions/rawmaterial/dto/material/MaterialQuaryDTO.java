package com.hvisions.rawmaterial.dto.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "原辅料查询参数")
public class MaterialQuaryDTO {

    @ApiModelProperty(value = "物料ID")
    private Integer id;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编号")
    private String materialCode;
}
