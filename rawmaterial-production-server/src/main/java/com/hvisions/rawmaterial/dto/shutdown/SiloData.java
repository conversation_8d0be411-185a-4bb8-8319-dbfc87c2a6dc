package com.hvisions.rawmaterial.dto.shutdown;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 筒仓数据基础抽象类
 * 定义所有筒仓类型的通用属性和方法
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class SiloData {
    
    /**
     * 筒仓ID
     */
    @NotBlank(message = "筒仓ID不能为空")
    protected String siloId;
    
    /**
     * 筒仓名称
     */
    @NotBlank(message = "筒仓名称不能为空")
    protected String siloName;
    
    /**
     * 库存量（从中控系统实时获取）
     */
    @NotNull(message = "库存量不能为空")
    @DecimalMin(value = "0", message = "库存量不能为负数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    protected BigDecimal inventory;
    
    /**
     * 剩余发放量
     */
    @NotNull(message = "剩余发放量不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    protected BigDecimal remainingDistribution;
    
    /**
     * 默认构造函数
     */
    protected SiloData() {
        this.inventory = BigDecimal.ZERO;
        this.remainingDistribution = BigDecimal.ZERO;
    }
    
    /**
     * 带参数构造函数
     */
    protected SiloData(String siloId, String siloName) {
        this();
        this.siloId = siloId;
        this.siloName = siloName;
    }
}