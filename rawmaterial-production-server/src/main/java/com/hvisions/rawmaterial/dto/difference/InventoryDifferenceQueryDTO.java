package com.hvisions.rawmaterial.dto.difference;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "库存差异处理查询条件")
public class InventoryDifferenceQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料编码", example = "MAT001", notes = "物料编码，支持模糊查询")
    private String materialCode;

    @ApiModelProperty(value = "物料名称", example = "原料A", notes = "物料名称，支持模糊查询")
    private String materialName;

    @ApiModelProperty(value = "ERP仓库编码", example = "WH001", notes = "ERP仓库编码，支持模糊查询")
    private String erpWarehouseCode;

    @ApiModelProperty(value = "ERP仓库名称", example = "原料仓库", notes = "ERP仓库名称，支持模糊查询")
    private String erpWarehouseName;

    @ApiModelProperty(value = "所属部门", example = "原辅料管理部", notes = "所属部门，支持模糊查询")
    private String department;

    @ApiModelProperty(value = "状态", example = "1", notes = "状态：1-待处理，2-已处理", allowableValues = "1,2")
    private Integer status;

    @ApiModelProperty(value = "状态列表", example = "[1,2]", notes = "状态列表，支持多选查询")
    private List<Integer> statusList;

    @ApiModelProperty(value = "统计开始日期-起始", example = "2024-01-01 00:00:00", notes = "统计开始日期范围查询的起始时间")
    private Date statisticsStartDateBegin;

    @ApiModelProperty(value = "统计开始日期-结束", example = "2024-01-31 23:59:59", notes = "统计开始日期范围查询的结束时间")
    private Date statisticsStartDateEnd;

    @ApiModelProperty(value = "统计结束日期-起始", example = "2024-01-01 00:00:00", notes = "统计结束日期范围查询的起始时间")
    private Date statisticsEndDateBegin;

    @ApiModelProperty(value = "统计结束日期-结束", example = "2024-01-31 23:59:59", notes = "统计结束日期范围查询的结束时间")
    private Date statisticsEndDateEnd;

    @ApiModelProperty(value = "处理人姓名", example = "张三", notes = "处理人姓名，支持模糊查询")
    @Size(max = 50, message = "处理人姓名长度不能超过50个字符")
    private String processorName;

    @ApiModelProperty(value = "处理时间-起始", example = "2024-01-01 00:00:00", notes = "处理时间范围查询的起始时间")
    private Date processTimeBegin;

    @ApiModelProperty(value = "处理时间-结束", example = "2024-01-31 23:59:59", notes = "处理时间范围查询的结束时间")
    private Date processTimeEnd;

    @ApiModelProperty(value = "差异数量-最小值", example = "0.00", notes = "差异数量范围查询的最小值")
    private BigDecimal differenceQuantityMin;

    @ApiModelProperty(value = "差异数量-最大值", example = "1000.00", notes = "差异数量范围查询的最大值")
    private BigDecimal differenceQuantityMax;

    @ApiModelProperty(value = "是否有差异", example = "true", notes = "是否有差异：true-有差异，false-无差异")
    private Boolean hasDifference;

    @ApiModelProperty(value = "是否已同步SAP", example = "true", notes = "是否已同步SAP：true-已同步，false-未同步")
    private Boolean sapSynced;

    @ApiModelProperty(value = "创建时间-起始", example = "2024-01-01 00:00:00", notes = "创建时间范围查询的起始时间")
    private Date createTimeBegin;

    @ApiModelProperty(value = "创建时间-结束", example = "2024-01-31 23:59:59", notes = "创建时间范围查询的结束时间")
    private Date createTimeEnd;
}
