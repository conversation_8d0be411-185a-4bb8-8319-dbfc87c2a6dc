package com.hvisions.rawmaterial.dto.transfer;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 转运任务DTO
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@ApiModel(description = "转运任务DTO")
public class TransferTaskDTO {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "工单号")
    private String orderNo;

    @ApiModelProperty(value = "工单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "当前库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "发出仓库ID")
    private Integer sendWarehouseId;

    @ApiModelProperty(value = "接收仓库ID")
    private Integer acceptWarehouseId;

    @ApiModelProperty(value = "状态（0：执行中，1：已完成）")
    private String status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
