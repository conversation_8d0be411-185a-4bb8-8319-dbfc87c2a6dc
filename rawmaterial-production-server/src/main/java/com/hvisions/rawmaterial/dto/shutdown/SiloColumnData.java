package com.hvisions.rawmaterial.dto.shutdown;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 筒仓列数据模型
 * 用于合并表格结构中的基础筒仓数据列
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiloColumnData {
    
    /**
     * 库存量
     */
    @NotNull(message = "库存量不能为空")
    @DecimalMin(value = "0", message = "库存量不能为负数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal inventory;
    
    /**
     * 剩余发放量
     */
    @NotNull(message = "剩余发放量不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal remainingDistribution;
    
    /**
     * 默认构造函数
     */
    public SiloColumnData() {
        this.inventory = BigDecimal.ZERO;
        this.remainingDistribution = BigDecimal.ZERO;
    }
    
    /**
     * 带参数构造函数
     */
    public SiloColumnData(BigDecimal inventory, BigDecimal remainingDistribution) {
        this.inventory = inventory != null ? inventory : BigDecimal.ZERO;
        this.remainingDistribution = remainingDistribution != null ? remainingDistribution : BigDecimal.ZERO;
    }
}