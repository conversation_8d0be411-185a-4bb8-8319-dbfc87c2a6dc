package com.hvisions.rawmaterial.enums;

/**
 * 停产物料需求类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public enum ShutdownDemandTypeEnum {

    BEFORE_10_DAYS("停产前10天", "BEFORE_10_DAYS"),
    BEFORE_7_DAYS("停产前7天", "BEFORE_7_DAYS"),
    BEFORE_5_DAYS("停产前5天", "BEFORE_5_DAYS"),
    BEFORE_3_DAYS("停产前3天", "BEFORE_3_DAYS"),
    BEFORE_1_DAY("停产前1天", "BEFORE_1_DAY"),
    BEFORE_8_HOURS("停产前8小时", "BEFORE_8_HOURS");

    private final String description;
    private final String code;

    ShutdownDemandTypeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据描述获取枚举
     */
    public static ShutdownDemandTypeEnum getByDescription(String description) {
        for (ShutdownDemandTypeEnum type : values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据代码获取枚举
     */
    public static ShutdownDemandTypeEnum getByCode(String code) {
        for (ShutdownDemandTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为8小时类型（用于区分日计划还是月计划）
     */
    public boolean is8HoursType() {
        return this == BEFORE_8_HOURS;
    }
}
