package com.hvisions.rawmaterial.controller.visualization;

import com.hvisions.rawmaterial.dto.visualization.WineDetailDTO;
import com.hvisions.rawmaterial.dto.visualization.WineFloorDTO;
import com.hvisions.rawmaterial.dto.visualization.WineInventoryDTO;
import com.hvisions.rawmaterial.service.visualization.WineInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 回酒库存可视化控制器
 * <AUTHOR>
 * @date 2025-09-04
 */
@Slf4j
@RestController
@RequestMapping("/visualization/wine")
@Api(tags = "回酒库存可视化接口")
public class WineInventoryVisualizationController {

    @Resource
    private WineInventoryService wineInventoryService;

    /**
     * 获取所有楼层的回酒库存可视化数据
     * @return 楼层回酒库存列表
     */
    @GetMapping("/floors")
    @ApiOperation(value = "获取所有楼层的回酒库存可视化数据", notes = "返回709-719楼层的回酒库存信息，每个楼层包含5个回酒罐")
    public List<WineFloorDTO> getAllFloorsWineInventory() {
        log.info("接收到获取所有楼层回酒库存可视化数据的请求");
        return wineInventoryService.getAllFloorsWineInventory();
    }

    /**
     * 根据楼层编号获取回酒库存数据
     * @param floorCode 楼层编号
     * @return 楼层回酒库存信息
     */
    @GetMapping("/floor/{floorCode}")
    @ApiOperation(value = "根据楼层编号获取回酒库存数据", notes = "根据楼层编号（如709、710等）获取该楼层的回酒库存信息")
    public WineFloorDTO getWineInventoryByFloor(
            @ApiParam(value = "楼层编号", required = true, example = "709")
            @PathVariable String floorCode) {
        log.info("接收到获取楼层回酒库存数据的请求，楼层编号：{}", floorCode);
        return wineInventoryService.getWineInventoryByFloor(floorCode);
    }

    /**
     * 根据回酒罐编号获取详细信息
     * @param tankCode 回酒罐编号
     * @return 回酒罐详细信息
     */
    @GetMapping("/detail/{tankCode}")
    @ApiOperation(value = "根据回酒罐编号获取详细信息", notes = "根据回酒罐编号（如709-01、710-02等）获取详细信息，包含历史记录")
    public WineDetailDTO getWineDetailByTankCode(
            @ApiParam(value = "回酒罐编号", required = true, example = "709-01")
            @PathVariable String tankCode) {
        log.info("接收到获取回酒罐详细信息的请求，回酒罐编号：{}", tankCode);
        return wineInventoryService.getWineDetailByTankCode(tankCode);
    }

    /**
     * 获取所有回酒罐的基础信息
     * @return 回酒罐基础信息列表
     */
    @GetMapping("/inventory")
    @ApiOperation(value = "获取所有回酒罐的基础信息", notes = "获取所有楼层所有回酒罐的基础库存信息列表")
    public List<WineInventoryDTO> getAllWineInventory() {
        log.info("接收到获取所有回酒罐基础信息的请求");
        return wineInventoryService.getAllWineInventory();
    }
}
