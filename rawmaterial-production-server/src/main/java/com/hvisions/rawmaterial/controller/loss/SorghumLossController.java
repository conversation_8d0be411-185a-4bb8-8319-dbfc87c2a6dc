package com.hvisions.rawmaterial.controller.loss;

import com.hvisions.powder.dto.qudou.SapPostVO;
import com.hvisions.rawmaterial.dto.production.loss.LossPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.loss.SyncLossDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDetailDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossPageDTO;
import com.hvisions.rawmaterial.service.SorghumLossService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱损耗 -- 固废提报
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/loss")
@Api(tags = "高粱损耗")
public class SorghumLossController {

    @Resource
    private SorghumLossService sorghumLossService;


    @ApiOperation(value = "分页查询高粱损耗")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumLossPageDTO> getSorghumLossPageList(@RequestBody LossPageQueryDTO queryDTO) {
        return sorghumLossService.getSorghumLossPageList(queryDTO);
    }


    @ApiOperation(value = "高粱损耗同步")
    @RequestMapping(value = "/sync", method = RequestMethod.POST)
    public void syncSorghumLoss(@RequestBody SyncLossDTO syncLossDTO) {
        sorghumLossService.syncSorghumLoss(syncLossDTO);
    }

    @ApiOperation(value = "高粱损耗新增修改")
    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    public Integer insertOrUpdateSorghumLoss(@RequestBody SorghumLossDTO sorghumLossDTO) {
        return sorghumLossService.insertOrUpdateSorghumLoss(sorghumLossDTO);
    }

    @ApiOperation(value = "高粱损耗冲销")
    @RequestMapping(value = "/write/off", method = RequestMethod.POST)
    public Integer writeOff(@RequestBody List<String> matDocs) {
        return sorghumLossService.writeOff(matDocs);
    }

    @ApiOperation(value = "未同步的高粱损耗删除操作")
    @DeleteMapping(value = "/delete/{id}")
    public Integer deleteOff(@PathVariable Integer id) {
        return sorghumLossService.deleteById(id);
    }

    @ApiOperation(value = "根据库存调整记录单号获取损耗详情")
    @GetMapping(value = "/getSorghumLossDetailByCertificateNumber/{certificateNumber}")
    public SorghumLossDetailDTO getSorghumLossDetailByCertificateNumber(@PathVariable String certificateNumber) {
        return sorghumLossService.getSorghumLossDetailByCertificateNumber(certificateNumber);
    }

    @ApiOperation(value = "根据中心id获取中心编码")
    @DeleteMapping(value = "/getCenterCodeById/{centerId}")
    public String getCenterCodeById(@PathVariable Integer centerId) {
        return sorghumLossService.getCenterCodeById(centerId);
    }

    @ApiOperation(value = "高粱损耗详情删除")
    @RequestMapping(value = "/deleteSorghumLossDetail", method = RequestMethod.POST)
    public Integer deleteSorghumLossDetail(@RequestBody SorghumLossDetailDTO sorghumLossDetailDTO) {
        return sorghumLossService.deleteSorghumLossDetail(sorghumLossDetailDTO);
    }

    @ApiOperation(value = "高粱库存调整时生成损耗记录")
    @RequestMapping(value = "/checkAndInsertBySapPost", method = RequestMethod.POST)
    public Integer checkAndInsertBySapPost(@RequestBody SapPostVO sapPostVO) {
        return sorghumLossService.checkAndInsertBySapPost(sapPostVO);
    }

    @ApiOperation(value = "高粱库存调整冲销删除损耗记录")
    @RequestMapping(value = "/revokeDetailBySapPost", method = RequestMethod.POST)
    public Integer revokeDetailBySapPost(@RequestBody SapPostVO sapPostVO) {
        return sorghumLossService.revokeDetailBySapPost(sapPostVO);
    }
}
