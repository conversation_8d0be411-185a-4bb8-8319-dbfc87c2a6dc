package com.hvisions.rawmaterial.controller.difference;

import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.difference.*;
import com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO;
import com.hvisions.rawmaterial.service.difference.InventoryDifferenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 原辅料库存盘点差异处理控制器
 * 
 * 提供库存差异处理的完整功能，包括：
 * - 差异记录的查询和管理
 * - 差异处理和批量处理
 * - SAP库存同步
 * - 统计信息查询
 * - 数据导出功能
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/rawmaterial/material-difference")
@Api(tags = "原辅料差异处理", description = "原辅料差异处理相关接口")
@Validated
public class MaterialDifferenceController {

    @Autowired
    private InventoryDifferenceService inventoryDifferenceService;
    
    @Autowired
    private com.hvisions.rawmaterial.service.async.AsyncTaskService asyncTaskService;

    /**
     * 分页查询库存差异处理记录
     * 
     * @param queryDTO 查询条件，包含分页参数和筛选条件
     * @return 分页查询结果
     */
    @ApiOperation(value = "分页查询库存差异处理记录", notes = "支持多种筛选条件的分页查询")
    @PostMapping("/page")
    public Page<InventoryDifferenceDTO> queryInventoryDifferences(
            @ApiParam(value = "查询条件", required = true) 
            @Valid @RequestBody InventoryDifferenceQueryDTO queryDTO) {
        log.info("分页查询库存差异处理记录，查询条件：{}", queryDTO);
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        log.info("查询成功，返回{}条记录", result.getTotalElements());
        return result;
    }

    /**
     * 根据ID查询差异处理记录详情
     * 
     * @param id 差异处理记录ID
     * @return 差异处理记录详情
     */
    @ApiOperation(value = "根据ID查询差异处理记录详情", notes = "获取指定ID的差异处理记录完整信息")
    @GetMapping("/{id}")
    public InventoryDifferenceDTO getInventoryDifferenceById(
            @ApiParam(value = "差异处理记录ID", required = true, example = "1") 
            @PathVariable @NotNull Integer id) {
        
        log.info("查询差异处理记录详情，ID：{}", id);

        InventoryDifferenceDTO result = inventoryDifferenceService.getInventoryDifferenceById(id);
        log.info("查询成功，记录ID：{}，物料编码：{}", id, result.getMaterialCode());
        return result;
    }

    /**
     * 查看差异处理详情
     *
     * @param id 差异记录ID
     * @return 差异处理详情
     */
    @ApiOperation(value = "获取差异处理详情", notes = "获取指定差异记录的详细处理信息")
    @GetMapping("/{id}/details")
    public InventoryDifferenceDTO getInventoryDifferenceDetails(
            @ApiParam(value = "差异记录ID", required = true, example = "1")
            @PathVariable @NotNull Integer id) {

        log.info("获取差异处理详情，ID：{}", id);

        InventoryDifferenceDTO result = inventoryDifferenceService.getInventoryDifferenceByIdDetails(id);
        return result;
    }

    /**
     * 物料查询
     */
    @ApiOperation(value = "物料查询", notes = "获取所有物料信息")
    @GetMapping("/material")
    public List<MaterialQuaryDTO> getMaterials() {
        log.info("开始查询物料信息");
        List<MaterialQuaryDTO> result = inventoryDifferenceService.getMaterialWarehouseInfo();
        log.info("查询成功，返回{}条物料信息", result.size());
        return result;
    }


    /**
     * 生成差异处理记录
     * 
     * @return 生成结果信息
     */
    @ApiOperation(value = "生成差异处理记录", notes = "系统自动计算MES与SAP库存差异并生成处理记录")
    @PostMapping("/generate")
    public Integer generateDifferenceRecords(@RequestBody InventoryDifferenceDTO inventoryDifferenceDTO) {
        log.info("开始生成差异处理记录");
        Integer count = inventoryDifferenceService.generateDifferenceRecords(inventoryDifferenceDTO);
        return count;
    }

    /**
     * 处理库存差异
     * 
     * @param processDTO 差异处理请求
     * @return 处理结果
     */
    @ApiOperation(value = "处理库存差异", notes = "处理指定的库存差异记录，更新状态并创建新的待处理记录")
    @PostMapping("/process")
    public Boolean processDifference(
            @ApiParam(value = "差异处理请求", required = true) 
            @Valid @RequestBody InventoryDifferenceProcessDTO processDTO) {
        
        log.info("处理库存差异，请求：{}", processDTO);

        Boolean success = inventoryDifferenceService.processDifference(processDTO);
        log.info("差异处理完成，结果：{}", success);
        return success;
    }

    /**
     * 批量处理差异
     * 
     * @param batchProcessDTO 批量处理请求
     * @param request HTTP请求对象
     * @return 批量处理结果
     */
    @ApiOperation(value = "批量处理差异", notes = "批量处理多个差异记录")
    @PostMapping("/batch-process")
    public Map<String, Object> batchProcessDifferences(
            @ApiParam(value = "批量处理请求", required = true) 
            @Valid @RequestBody InventoryDifferenceBatchProcessDTO batchProcessDTO,
            HttpServletRequest request) {

        log.info("批量处理差异，请求：{}", batchProcessDTO);

        // 将批量处理DTO转换为处理DTO列表
        List<InventoryDifferenceProcessDTO> processList = convertBatchToProcessList(batchProcessDTO);

        Integer successCount = inventoryDifferenceService.batchProcessDifferences(processList);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "批量处理完成");
        result.put("successCount", successCount);
        result.put("totalCount", processList.size());

        log.info("批量处理完成，成功处理{}条记录", successCount);
        return result;
    }

    /**
     * 同步SAP库存
     * 
     * @param syncDTOList SAP库存同步数据列表
     * @return 同步结果
     */
    @ApiOperation(value = "同步SAP库存", notes = "批量同步SAP库存数据")
    @PostMapping("/sync-sap-stock")
    public ResponseEntity<Map<String, Object>> syncSapStock(
            @ApiParam(value = "SAP库存同步数据列表", required = true) 
            @Valid @RequestBody @NotEmpty List<SapStockSyncDTO> syncDTOList) {
        
        log.info("同步SAP库存，数据条数：{}", syncDTOList.size());
        
        try {
            Integer successCount = inventoryDifferenceService.syncSapStock(syncDTOList);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "SAP库存同步完成");
            result.put("successCount", successCount);
            result.put("totalCount", syncDTOList.size());
            
            log.info("SAP库存同步完成，成功同步{}条记录", successCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("同步SAP库存失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "SAP库存同步失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 手动同步单个物料的SAP库存
     * 
     * @param materialCode 物料编码
     * @param erpWarehouseCode ERP仓库编码
     * @return 同步结果
     */
    @ApiOperation(value = "手动同步单个物料的SAP库存", notes = "手动同步指定物料和仓库的SAP库存")
    @PostMapping("/sync-single-sap-stock")
    public ResponseEntity<Map<String, Object>> syncSingleSapStock(
            @ApiParam(value = "物料编码", required = true, example = "MAT001") 
            @RequestParam @NotNull String materialCode,
            @ApiParam(value = "ERP仓库编码", required = true, example = "WH001") 
            @RequestParam @NotNull String erpWarehouseCode) {
        
        log.info("手动同步单个物料SAP库存，物料编码：{}，仓库编码：{}", materialCode, erpWarehouseCode);
        
        try {
            Boolean success = inventoryDifferenceService.syncSingleSapStock(materialCode, erpWarehouseCode);
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "SAP库存同步成功" : "SAP库存同步失败");
            result.put("materialCode", materialCode);
            result.put("erpWarehouseCode", erpWarehouseCode);
            
            log.info("单个物料SAP库存同步完成，结果：{}", success);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动同步单个物料SAP库存失败，物料编码：{}，仓库编码：{}", materialCode, erpWarehouseCode, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "SAP库存同步失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 导出差异记录
     *
     * @param queryDTO 查询条件
     * @return 导出文件信息
     */
    @ApiOperation(value = "导出差异记录", notes = "根据查询条件导出差异记录到Excel文件")
    @PostMapping("/export")
    public ResponseEntity<Map<String, Object>> exportDifferenceRecords(
            @ApiParam(value = "查询条件")
            @Valid @RequestBody InventoryDifferenceQueryDTO queryDTO) {

        log.info("导出差异记录，查询条件：{}", queryDTO);

        try {
            List<InventoryDifferenceDTO> exportData = inventoryDifferenceService.exportDifferences(queryDTO);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "导出成功");
            result.put("exportCount", exportData.size());
            result.put("data", exportData);

            log.info("导出差异记录成功，导出{}条记录", exportData.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("导出差异记录失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "导出失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 将批量处理DTO转换为处理DTO列表
     * 
     * @param batchProcessDTO 批量处理DTO
     * @return 处理DTO列表
     */
    private List<InventoryDifferenceProcessDTO> convertBatchToProcessList(InventoryDifferenceBatchProcessDTO batchProcessDTO) {
        List<InventoryDifferenceProcessDTO> processList = new java.util.ArrayList<>();
        
        for (InventoryDifferenceBatchDetailDTO batchDetail : batchProcessDTO.getBatchDetails()) {
            InventoryDifferenceProcessDTO processDTO = new InventoryDifferenceProcessDTO();
            processDTO.setId(batchDetail.getDifferenceId());
            processDTO.setStatisticsEndDate(batchProcessDTO.getStatisticsEndDate());
            processDTO.setProcessRemark(batchDetail.getProcessRemark());
            processDTO.setDetailList(batchDetail.getDetailList());
            
            processList.add(processDTO);
        }
        
        return processList;
    }
}
