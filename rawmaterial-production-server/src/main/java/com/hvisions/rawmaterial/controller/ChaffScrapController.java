package com.hvisions.rawmaterial.controller;


import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapHandleDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapPageDTO;
import com.hvisions.rawmaterial.dto.production.chaff.scrap.ChaffScrapQueryDTO;
import com.hvisions.rawmaterial.service.ChaffScrapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 蒸糠报废处理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */
@RestController
@RequestMapping("/chaff/scrap")
@Api(tags = "蒸糠报废处理")
public class ChaffScrapController {

    @Resource
    private ChaffScrapService chaffScrapService;


    @ApiOperation(value = "分页查询蒸糠报废处理")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<ChaffScrapPageDTO> getChaffScrapPageList(@RequestBody ChaffScrapQueryDTO queryDTO) {
        return chaffScrapService.getChaffScrapPageList(queryDTO);
    }

    @ApiOperation(value = "删除蒸糠报废处理")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    public Boolean deleteChaffScrap(@PathVariable Integer id) {
        return chaffScrapService.deleteChaffScrap(id);
    }

    @ApiOperation(value = "处理蒸糠报废处理")
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public Boolean handleChaffScrap(@RequestBody ChaffScrapHandleDTO handles) {
        return chaffScrapService.handleChaffScrap(handles);
    }

    @ApiOperation(value = "新增蒸糠报废")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Boolean addChaffScrap(@RequestBody ChaffScrapDTO chaffScrapDTO) {
        return chaffScrapService.addChaffScrap(chaffScrapDTO);
    }

}
