package com.hvisions.rawmaterial.controller.inventory;

import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryDTO;
import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryQueryDTO;
import com.hvisions.rawmaterial.dto.inventory.RawMaterialInventoryStatsDTO;
import com.hvisions.rawmaterial.service.inventory.RawMaterialInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 原辅料库存控制器
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@RestController
@RequestMapping("/inventory/rawmaterial")
@Api(tags = "原辅料库存管理")
public class RawMaterialInventoryController {

    @Autowired
    private RawMaterialInventoryService rawMaterialInventoryService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询原辅料库存", notes = "支持多条件查询和分页")
    public Page<RawMaterialInventoryDTO> getInventoryByPage(@RequestBody RawMaterialInventoryQueryDTO queryDTO) {
        log.info("分页查询原辅料库存，查询条件：{}", queryDTO);
        return rawMaterialInventoryService.getInventoryByPage(queryDTO);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询所有原辅料库存", notes = "获取所有库存数据，不分页")
    public List<RawMaterialInventoryDTO> getAllInventory(@RequestBody(required = false) RawMaterialInventoryQueryDTO queryDTO) {
        log.info("查询所有原辅料库存");
        return rawMaterialInventoryService.getAllInventory(queryDTO);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取库存详情", notes = "根据物料编码和仓库编码获取详细信息")
    public RawMaterialInventoryDTO getInventoryDetail(
            @ApiParam(value = "物料编码", required = true) @RequestParam String materialCode,
            @ApiParam(value = "仓库编码", required = true) @RequestParam String warehouseCode) {
        log.info("获取库存详情，物料编码：{}，仓库编码：{}", materialCode, warehouseCode);
        return rawMaterialInventoryService.getInventoryDetail(materialCode, warehouseCode);
    }

    @GetMapping("/stats")
    @ApiOperation(value = "获取库存统计信息", notes = "获取库存汇总统计数据")
    public RawMaterialInventoryStatsDTO getInventoryStats() {
        log.info("获取库存统计信息");
        return rawMaterialInventoryService.getInventoryStats();
    }

    @PostMapping("/sync")
    @ApiOperation(value = "同步库存数据", notes = "从MES和SAP系统同步最新库存数据")
    public Boolean syncInventoryData() {
        log.info("同步库存数据");
        return rawMaterialInventoryService.syncInventoryData();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出库存数据", notes = "导出Excel格式的库存数据")
    public String exportInventoryData(@RequestBody(required = false) RawMaterialInventoryQueryDTO queryDTO) {
        log.info("导出库存数据");
        return rawMaterialInventoryService.exportInventoryData(queryDTO);
    }
}
