package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.production.flour.transfer.detail.FlourTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.service.FlourTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱粉转运工单-二期改为碎料转运任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/flour/transfer/order")
@Api(tags = "高粱粉转运工单-二期改为碎料转运任务")
public class FlourTransferOrderController {

    @Resource
    private FlourTransferOrderService branTransferOrderService;


    @ApiOperation(value = "分页查询高粱粉转运工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<FlourTransferOrderPageDTO> getFlourTransferOrderPageList(@RequestBody FlourTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getFlourTransferOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱粉转运工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "分页查询高粱粉转运工单详细")
    @RequestMapping(value = "/getFlourTransferOrderDetailPageList", method = RequestMethod.POST)
    public Page<FlourTransferDetailListDTO> getFlourTransferOrderDetailPageList(@RequestBody FlourTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getFlourTransferOrderDetailPageList(queryDTO);
    }
}
