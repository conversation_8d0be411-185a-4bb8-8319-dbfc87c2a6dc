package com.hvisions.rawmaterial.controller.schedule;

import com.hvisions.rawmaterial.dto.*;
import com.hvisions.rawmaterial.service.ScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 排班管理控制器
 * @Date: 2024/06/13
 */
@Slf4j
@RestController
@RequestMapping("/schedule-management")
@Api(tags = "排班管理")
public class ScheduleManagementController {

    @Autowired
    private ScheduleService scheduleService;

    /**
     * 获取排班表数据
     */
    @PostMapping("/data")
    @ApiOperation("获取排班表数据")
    public ScheduleResponseDTO getScheduleData(@RequestBody ScheduleQueryDTO query) {
        return scheduleService.getScheduleData(query);
    }
    
    /**
     * 维护停产日期
     */
    @PostMapping("/shutdown/maintain")
    @ApiOperation("维护停产日期")
    public Boolean maintainShutdownDates(@Valid @RequestBody ShutdownDTO dto) {
      return scheduleService.maintainShutdownDates(dto);
    }
    
    /**
     * 维护休息排班
     */
    @PostMapping("/rest/maintain")
    @ApiOperation("维护休息排班")
    public Boolean maintainEmployeeRest(@Valid @RequestBody EmployeeRestDTO dto) {
        return scheduleService.maintainEmployeeRest(dto);
    }
    
    /**
     * 班组人员分配
     */
    //@PostMapping("/workgroup/assign")
    //@ApiOperation("班组人员分配")
    public Boolean assignWorkGroupMembers(@Valid @RequestBody WorkGroupAssignDTO dto) {
        return scheduleService.assignWorkGroupMembers(dto);
    }
    
    /**
     * 批量班组人员分配
     */
    @PostMapping("/workgroup/assign/batch")
    @ApiOperation("批量班组人员分配")
    public Boolean assignWorkGroupMembersBatch(@Valid @RequestBody WorkGroupAssignBatchDTO dto) {
        return scheduleService.assignWorkGroupMembersBatch(dto);
    }
    
    /**
     * 获取班组人员列表
     */
    @GetMapping("/workgroup/{groupType}")
    @ApiOperation("获取班组人员列表")
    public WorkGroupAssignResponse getWorkGroupMembers(@PathVariable Integer groupType) {
        return scheduleService.getWorkGroupMembers(groupType);
    }

    /**
     * 获取班组人员列表-所有无过滤
     */
    @GetMapping("/workgroup/all/{groupType}")
    @ApiOperation("获取班组人员列表-所有无过滤")
    public WorkGroupAssignResponse getWorkGroupMembersAll(@PathVariable Integer groupType) {
        return scheduleService.getWorkGroupMembersAll(groupType);
    }

} 