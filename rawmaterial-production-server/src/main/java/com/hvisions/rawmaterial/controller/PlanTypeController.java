package com.hvisions.rawmaterial.controller;

import com.hvisions.rawmaterial.dto.production.plan.type.PlanTypeDTO;
import com.hvisions.rawmaterial.dto.production.plan.type.PlanTypePageDTO;
import com.hvisions.rawmaterial.dto.production.plan.type.PlanTypePageQueryDTO;
import com.hvisions.rawmaterial.service.PlanTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 工单类型
 * @author: Jcao
 * @time: 2022/4/22 15:22
 */
@RestController
@RequestMapping(value = "/plan/type")
@Api(tags = "计划类型")
public class PlanTypeController {
    
    @Resource
    private PlanTypeService planTypeService;


    @ApiOperation(value = "分页查询计划类型")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<PlanTypePageDTO> getPlanTypePageList(@RequestBody PlanTypePageQueryDTO planTypePageQueryDTO) {
        return planTypeService.getPlanTypePageList(planTypePageQueryDTO);
    }

    @ApiOperation(value = "新增计划类型")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Integer addPlanType(@Valid @RequestBody PlanTypeDTO planTypeDTO) {
        return planTypeService.addPlanType(planTypeDTO);
    }

    @ApiOperation(value = "修改计划类型")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public Integer updatePlanType(@Valid @RequestBody PlanTypeDTO planTypeDTO) {
        return planTypeService.updatePlanType(planTypeDTO);
    }

    @ApiOperation(value = "删除计划类型")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    public Integer deletePlanType(@PathVariable Integer id) {
        return planTypeService.deletePlanType(id);
    }
    
    
}
