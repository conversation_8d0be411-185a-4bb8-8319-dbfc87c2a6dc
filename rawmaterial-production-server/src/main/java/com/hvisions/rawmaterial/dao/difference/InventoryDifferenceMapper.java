package com.hvisions.rawmaterial.dao.difference;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.difference.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifference;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理Mapper接口
 * @Date: 2024/07/14
 */
@Mapper
public interface InventoryDifferenceMapper extends BaseMapper<TMpdInventoryDifference> {

    /**
     * 分页查询库存差异处理记录
     * @param queryDTO 查询条件
     * @return 差异处理记录列表
     */
    List<InventoryDifferenceDTO> queryInventoryDifferences(@Param("queryDTO") InventoryDifferenceQueryDTO queryDTO);

    /**
     * 根据ID查询差异处理记录详情
     * @param id 差异处理ID
     * @return 差异处理记录详情
     */
    InventoryDifferenceDTO getInventoryDifferenceById(@Param("id") Integer id);

    /**
     * 查询待处理的差异记录
     * @return 待处理的差异记录列表
     */
    List<InventoryDifferenceDTO> getPendingDifferences();

    /**
     * 根据物料和仓库查询最新的差异处理记录
     * @param materialCode 物料编码
     * @param erpWarehouseCode ERP仓库编码
     * @return 最新的差异处理记录
     */
    InventoryDifferenceDTO getLatestDifferenceByMaterialAndWarehouse(@Param("materialCode") String materialCode, 
                                                                     @Param("erpWarehouseCode") String erpWarehouseCode);

    /**
     * 统计MES当前库存
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return MES当前库存
     */
    BigDecimal getMesCurrentStock(@Param("materialCode") String materialCode,
                                  @Param("startDate") Date startDate,
                                  @Param("endDate") Date endDate);

    /**
     * 统计地磅收货数量
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地磅收货数量
     */
    BigDecimal getWeighbridgeReceiptQuantity(@Param("materialCode") String materialCode,
                                             @Param("startDate") Date startDate, 
                                             @Param("endDate") Date endDate);

    /**
     * 统计固废提报数量
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 固废提报数量
     */
    BigDecimal getSolidWasteQuantity(@Param("materialCode") String materialCode,
                                     @Param("startDate") Date startDate, 
                                     @Param("endDate") Date endDate);

    /**
     * 统计发料数量
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 发料数量
     */
    BigDecimal getIssuedQuantity(@Param("materialCode") String materialCode,
                                 @Param("startDate") Date startDate, 
                                 @Param("endDate") Date endDate);

    /**
     * 获取物料和仓库的基本信息
     * @return 物料和仓库信息列表
     */
    List<MaterialQuaryDTO> getMaterialWarehouseInfo();
    
    /**
     * 获取待处理差异统计信息
     * @return 待处理差异统计信息
     */
    Map<String, Object> getPendingStatistics();
    
    /**
     * 获取已处理差异统计信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 已处理差异统计信息
     */
    Map<String, Object> getProcessedStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 按部门统计待处理差异
     * @return 部门统计信息
     */
    List<Map<String, Object>> getPendingStatisticsByDepartment();
    
    /**
     * 按物料类型统计待处理差异
     * @return 物料类型统计信息
     */
    List<Map<String, Object>> getPendingStatisticsByMaterialType();
    
    /**
     * 按处理人统计已处理差异
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 处理人统计信息
     */
    List<Map<String, Object>> getProcessedStatisticsByProcessor(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 按日期统计已处理差异
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期统计信息
     */
    List<Map<String, Object>> getProcessedStatisticsByDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 批量查询库存差异数据
     * @param materialCodes 物料编码列表
     * @param warehouseCodes 仓库编码列表
     * @return 查询结果
     */
    List<Object> batchQueryByMaterialAndWarehouse(@Param("materialCodes") List<String> materialCodes, 
                                                  @Param("warehouseCodes") List<String> warehouseCodes);
    
    /**
     * 批量更新状态
     * @param ids 记录ID列表
     * @param status 目标状态
     * @param updateTime 更新时间
     * @return 更新记录数
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, 
                         @Param("status") Integer status, 
                         @Param("updateTime") Date updateTime);
    
    /**
     * 批量插入库存差异记录
     * @param differences 差异记录列表
     * @return 插入记录数
     */
    int batchInsertDifferences(@Param("list") List<Object> differences);
    
    /**
     * 获取统计信息
     * @return 统计信息
     */
    Map<String, Object> getStatistics();
    
    /**
     * 获取热点物料统计
     * @return 热点物料统计
     */
    List<Map<String, Object>> getHotMaterials();

    /**
     * 获取稻壳发放数量
     * @param materialCode 物料编码
     * @param startDate 统计开始日期
     * @param endDate 当前日期
     * @return 稻壳发放数量
     */
    BigDecimal getRiceIssuedQuantity(
            @Param("materialCode")String materialCode,
            @Param("startDate")Date startDate,
            @Param("endDate")Date endDate);

    /**
     * 获取搞了发放数量
     * @param materialCode 物料编码
     * @param startDate 统计开始日期
     * @param endDate 当前日期
     * @return
     */
    BigDecimal getSorghumIssuedQuantity(@Param("materialCode")String materialCode,
                                        @Param("startDate")Date startDate,
                                        @Param("endDate")Date endDate);

    /**
     * 获取库存差异处理中心数据
     * @param materialCode 物料编码
     * @param startDate 统计开始日期
     * @param endDate 统计结束日期
     * @return 库存差异处理中心数据
     */
    List<TMpdInventoryDifferenceDetail> getRiceIssuedInventoryDifferenceCenters(
            @Param("materialCode")String materialCode,
            @Param("startDate")Date startDate,
            @Param("endDate")Date endDate);

    /**
     * 获取搞了发放中心数据
     * @param materialCode
     * @param startDate
     * @param endDate
     * @return
     */
    List<TMpdInventoryDifferenceDetail> getSorghumIssuedInventoryDifferenceCenters(@Param("materialCode")String materialCode,
                                                                                   @Param("startDate")Date startDate,
                                                                                   @Param("endDate")Date endDate);
}
