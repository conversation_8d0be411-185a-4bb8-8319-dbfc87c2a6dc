package com.hvisions.rawmaterial.dao;

import com.hvisions.rawmaterial.entity.TMpdWineRecycleRequirement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 回酒日需求数据访问接口
 * <AUTHOR>
 */
public interface WineRecycleRequirementRepository extends JpaRepository<TMpdWineRecycleRequirement, Integer>, JpaSpecificationExecutor<TMpdWineRecycleRequirement> {
    
    /**
     * 根据中心ID和车间ID查询在途的需求量总和（状态为已报送）
     * @param brewCenterId 酿酒中心ID
     * @param workshopId 车间ID
     * @return 在途需求量总和
     */
    @Query("SELECT SUM(t.reportAmount) FROM TMpdWineRecycleRequirement t WHERE t.brewCenterId = :brewCenterId AND t.workshopId = :workshopId AND t.status = 1")
    Double sumWineInTransitByBrewCenterAndWorkshop(@Param("brewCenterId") Integer brewCenterId, @Param("workshopId") Integer workshopId);
    
    /**
     * 根据中心ID、车间ID和计划日期查询当天的日需求
     * @param brewCenterId 酿酒中心ID
     * @param workshopId 车间ID
     * @param planDate 计划日期
     * @return 日需求列表
     */
    List<TMpdWineRecycleRequirement> findByBrewCenterIdAndWorkshopIdAndPlanDate(Integer brewCenterId, Integer workshopId, Date planDate);
    
    /**
     * 根据需求单号查询（模糊匹配）
     * @param requirementNo 需求单号
     * @return 日需求列表
     */
    List<TMpdWineRecycleRequirement> findByRequirementNoContaining(String requirementNo);
} 