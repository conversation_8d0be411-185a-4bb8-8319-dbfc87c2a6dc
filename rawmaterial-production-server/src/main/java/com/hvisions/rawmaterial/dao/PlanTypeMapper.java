package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.production.plan.type.PlanTypePageDTO;
import com.hvisions.rawmaterial.dto.production.plan.type.PlanTypePageQueryDTO;
import com.hvisions.rawmaterial.entity.TMpdPlanType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 计划类型
 * @author: Jcao
 * @time: 2022/4/22 15:40
 */
@Mapper
public interface PlanTypeMapper extends BaseMapper<TMpdPlanType> {

    List<PlanTypePageDTO> getPlanTypePageList(PlanTypePageQueryDTO planTypePageQueryDTO);

}
