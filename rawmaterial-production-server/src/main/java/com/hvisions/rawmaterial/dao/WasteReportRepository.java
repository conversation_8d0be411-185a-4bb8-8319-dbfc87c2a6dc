package com.hvisions.rawmaterial.dao;

import com.hvisions.rawmaterial.entity.TMpdWasteReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;

/**
 * 固废提报数据访问接口
 * <AUTHOR>
 */
public interface WasteReportRepository extends JpaRepository<TMpdWasteReport, Integer>, JpaSpecificationExecutor<TMpdWasteReport> {
    
    /**
     * 获取当天最大的序列号
     * @param datePrefix 日期前缀，格式：YYMMDD
     * @return 最大序列号
     */
    @Query(value = "SELECT MAX(SUBSTRING(order_no, 9)) FROM t_mpd_waste_report WHERE order_no LIKE CONCAT('GF', :datePrefix, '%')", nativeQuery = true)
    String findMaxSerialNumber(@Param("datePrefix") String datePrefix);
} 