package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.production.loss.LossPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDetailDTO;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossPageDTO;
import com.hvisions.rawmaterial.entity.TMpdSorghumLoss;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱损耗
 * @date 2022/4/22 10:18
 */
@Mapper
public interface SorghumLossMapper extends BaseMapper<TMpdSorghumLoss> {

    List<SorghumLossPageDTO> getSorghumLossPageList(LossPageQueryDTO queryDTO);

    SorghumLossDetailDTO selectDetailByCertificateNumber(String certificateNumber);

    Integer getCenterIdByWarehouseCode(String code);

    String getCenterCodeById(Integer centerId);

    /**
     * 查询指定日期前缀的工单号数量
     * @param orderNoPrefix 工单号前缀，格式：GF+YYMMDD
     * @return 当天已生成的工单号数量
     */
    Integer countByOrderNoPrefix(String orderNoPrefix);

}
