package com.hvisions.rawmaterial.entity.schedule;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 休息排班表
 * <AUTHOR>
 */
@Data
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_rest_schedule")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_rest_schedule", comment = "休息排班表")
public class TMpdRestSchedule extends SysBase {
    
    /**
     * 员工ID
     */
    @Column(columnDefinition = "int COMMENT '员工ID'")
    private Integer employeeId;
    
    /**
     * 员工姓名
     */
    @Column(columnDefinition = "varchar(255) COMMENT '员工姓名'")
    private String employeeName;
    
    /**
     * 班组ID
     */
    @Column(columnDefinition = "int COMMENT '班组ID'")
    private Integer groupId;
    
    /**
     * 班组类型：1-高粱处理，2-稻壳处理
     */
    @Column(columnDefinition = "int COMMENT '班组类型：1-高粱处理，2-稻壳处理'")
    private Integer groupType;
    
    /**
     * 班次：1-一班，2-二班，3-三班
     */
    @Column(columnDefinition = "int COMMENT '班次：1-一班，2-二班，3-三班'")
    private Integer shiftType;
    
    /**
     * 年份
     */
    @Column(columnDefinition = "int COMMENT '年份'")
    private Integer year;
    
    /**
     * 月份
     */
    @Column(columnDefinition = "int COMMENT '月份'")
    private Integer month;
    
    /**
     * 日
     */
    @Column(columnDefinition = "int COMMENT '日'")
    private Integer day;
    
    /**
     * 日期
     */
    @Temporal(TemporalType.DATE)
    @Column(columnDefinition = "date COMMENT '日期'")
    private Date date;
    
    /**
     * 是否休息
     */
    @Column(columnDefinition = "bit(1) COMMENT '是否休息'")
    private Boolean isRest;
    
    /**
     * 备注
     */
    @Column(length = 200, columnDefinition = "varchar(200) COMMENT '备注'")
    private String remark;
} 