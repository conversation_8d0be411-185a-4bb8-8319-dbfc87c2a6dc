package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 蒸糠质量巡检参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_bran_inspection_parameter")
public class TMpdBranInspectionParameter extends SysBase {

    /**
     * 巡检周期
     */
    private Integer inspectionCycle;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 文件id
     */
    private Integer fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 基地id
     */
    private Integer productionBaseId;

    /**
     * 基地
     */
    private String productionBase;

    /**
     * 原辅料管理部id
     */
    private Integer centerId;

    /**
     * 原辅料管理部
     */
    private String center;

    /**
     * 车间id
     */
    private Integer locationId;

    /**
     * 车间
     */
    private String location;

    /**
     * 产线id
     */
    private Integer productionLineId;

    /**
     * 产线
     */
    private String productionLine;

    /**
     * 自动生成巡检任务;0-false、1-true
     */
    private Boolean generatedTask;

    /**
     * 定时任务id
     */
    private Integer timerId;

}
