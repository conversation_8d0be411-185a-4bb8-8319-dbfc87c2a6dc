package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 高粱损耗
 * @date 2022/4/25 10:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_sorghum_loss")
public class TMpdSorghumLoss extends SysBase {

    /**
     * 中心id
     */
    private Integer centerId;
    /**
     * 中心编码
     */
    private String center;


    /**
     * 物料id
     */
    private Integer materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料单位
     */
    private String unit;

    /**
     * 统计开始日期,上月24号
     */
    private Date startDate;
    /**
     * 统计结束日期，本月23号
     */
    private Date endDate;

    /**
     * 发放数量
     */
    private BigDecimal issueQuantity;
    /**
     * 损耗数量
     */
    private BigDecimal lossQuantity;

    /**
     * 物料凭证编号
     */
    private String matDoc;
    /**
     * 物料凭证年度
     */
    private String docYear;

    /**
     * 过账时间
     */
    private Date postingTime;
    /**
     * 是否过账：0-未过账，1-已过账
     */
    private String state;


    /**
     * 工单号：GF+YYMMDD+三位流水
     */
    private String orderNo;


}
