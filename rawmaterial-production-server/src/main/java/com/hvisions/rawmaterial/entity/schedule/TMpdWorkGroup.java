package com.hvisions.rawmaterial.entity.schedule;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * 班组信息表
 * <AUTHOR>
 */
@Data
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_work_group")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_work_group", comment = "班组信息表")
public class TMpdWorkGroup extends SysBase {
    
    /**
     * 班组名称
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '班组名称'")
    private String groupName;
    
    /**
     * 班组编号
     */
    @Column(length = 20, columnDefinition = "varchar(20) COMMENT '班组编号'")
    private String groupCode;
    
    /**
     * 班组类型：1-高粱处理，2-稻壳处理
     */
    @Column(columnDefinition = "int COMMENT '班组类型：1-高粱处理，2-稻壳处理'")
    private Integer groupType;
    
    /**
     * 班次：1-一班，2-二班，3-三班
     */
    @Column(columnDefinition = "int COMMENT '班次：1-一班，2-二班，3-三班'")
    private Integer shiftType;
    
    /**
     * 描述
     */
    @Column(length = 200, columnDefinition = "varchar(200) COMMENT '描述'")
    private String description;
} 