package com.hvisions.rawmaterial.entity.schedule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 停产日期表
 * <AUTHOR>
 */
@Table(name = "t_mpd_shutdown_date")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_shutdown_date", comment = "停产日期表")
@Data
@TableName("t_mpd_shutdown_date")
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
public class TMpdShutdownDate extends SysBase {
    
    /**
     * 类型：1-高粱处理，2-稻壳处理
     */
    @Column(columnDefinition = "int COMMENT '类型：1-高粱处理，2-稻壳处理'")
    private Integer type;
    
    /**
     * 年份
     */
    @Column(columnDefinition = "int COMMENT '年份'")
    private Integer year;
    
    /**
     * 月份
     */
    @Column(columnDefinition = "int COMMENT '月份'")
    private Integer month;
    
    /**
     * 日
     */
    @Column(columnDefinition = "int COMMENT '日'")
    private Integer day;
    
    /**
     * 是否停产
     */
    @Column(columnDefinition = "bit(1) COMMENT '是否停产'")
    private Boolean isShutdown;
    
    /**
     * 备注
     */
    @Column(length = 200, columnDefinition = "varchar(200) COMMENT '备注'")
    private String remark;
} 