-- 同步操作日志表
CREATE TABLE IF NOT EXISTS `t_mpd_sync_operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `module_name` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `business_type` int(1) DEFAULT NULL COMMENT '业务类型（0=错误，1=新增主表，2=新增详情，3=更新详情，4=更新主表汇总，5=跳过重复记录）',
  `operation_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `task_no` varchar(50) DEFAULT NULL COMMENT '任务号',
  `order_no` varchar(20) DEFAULT NULL COMMENT '工单号',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(50) DEFAULT NULL COMMENT '物料名称',
  `unique_id` varchar(100) DEFAULT NULL COMMENT '唯一标识',
  `main_task_id` int(11) DEFAULT NULL COMMENT '关联主表ID',
  `detail_id` int(11) DEFAULT NULL COMMENT '关联详情ID',
  `quantity` decimal(20,6) DEFAULT NULL COMMENT '操作数量',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operation_result` int(1) DEFAULT NULL COMMENT '操作结果（0=失败，1=成功）',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` int(11) DEFAULT NULL COMMENT '更新人ID',
  `site_num` varchar(255) DEFAULT NULL COMMENT '租户',
  `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_module_name` (`module_name`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_task_no` (`task_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_result` (`operation_result`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步操作日志';

-- 插入示例数据
INSERT INTO `t_mpd_sync_operation_log` (
  `module_name`, `business_type`, `operation_desc`, `task_no`, `order_no`, 
  `material_code`, `material_name`, `unique_id`, `main_task_id`, `detail_id`, 
  `quantity`, `operation_time`, `operation_result`, `operator`, `remark`
) VALUES 
('高粱入仓任务同步', 1, '新增主表任务', 'TASK20250723001', 'RC250723001', 
 '11000544', 'LGL1', 'GL2-1_20250723001', 1, NULL, 
 52142.000000, '2025-07-23 10:30:00', 1, '中控系统', '示例数据'),
('高粱入仓任务同步', 2, '新增详情记录', 'TASK20250723001', 'RC250723001', 
 '11000544', 'LGL1', 'GL2-1_20250723001', 1, 1, 
 52142.000000, '2025-07-23 10:30:01', 1, '中控系统', '示例数据'),
('高粱入仓任务同步', 5, '跳过重复记录', 'TASK20250723002', NULL, 
 '11000544', 'LGL1', 'GL2-1_20250723002', NULL, NULL, 
 52142.000000, '2025-07-23 10:35:00', 1, '中控系统', '示例数据');
