-- 曲斗库存表
CREATE TABLE IF NOT EXISTS `t_mpd_qudou_inventory` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qu_dou_code` varchar(20) NOT NULL COMMENT '曲斗编号',
  `qu_dou_name` varchar(50) DEFAULT NULL COMMENT '曲斗名称',
  `empty_count` int(11) DEFAULT 0 COMMENT '空斗数',
  `full_count` int(11) DEFAULT 0 COMMENT '满斗数',
  `weight` decimal(20,6) DEFAULT 0.000000 COMMENT '曲斗重量(kg)',
  `status` varchar(1) DEFAULT '0' COMMENT '状态：0-正常，1-检修',
  `data_update_time` datetime DEFAULT NULL COMMENT '数据更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_qu_dou_code` (`qu_dou_code`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曲斗库存';

-- 曲斗跨详细信息表
CREATE TABLE IF NOT EXISTS `t_mpd_qudou_span_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qu_dou_code` varchar(20) NOT NULL COMMENT '曲斗编号',
  `workshop_code` varchar(20) NOT NULL COMMENT '车间编号',
  `workshop_name` varchar(50) DEFAULT NULL COMMENT '车间名称',
  `span_code` varchar(20) NOT NULL COMMENT '跨编号',
  `span_name` varchar(50) DEFAULT NULL COMMENT '跨名称',
  `flour_type` varchar(20) DEFAULT 'QF1' COMMENT '曲粉类型',
  `empty_count` int(11) DEFAULT 0 COMMENT '空斗数',
  `full_count` int(11) DEFAULT 0 COMMENT '满斗数',
  `in_transit_count` int(11) DEFAULT 0 COMMENT '在途斗数',
  `flour_weight` decimal(20,6) DEFAULT 0.000000 COMMENT '曲粉重量(kg)',
  `data_update_time` datetime DEFAULT NULL COMMENT '数据更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_qudou_workshop_span` (`qu_dou_code`, `workshop_code`, `span_code`),
  KEY `idx_qu_dou_code` (`qu_dou_code`),
  KEY `idx_workshop_code` (`workshop_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曲斗跨详细信息';

-- 插入测试数据
INSERT INTO `t_mpd_qudou_inventory` (`qu_dou_code`, `qu_dou_name`, `empty_count`, `full_count`, `weight`, `status`, `data_update_time`, `remark`) VALUES
('709', '曲斗709', 18, 22, 11000.000000, '0', NOW(), '正常运行'),
('710', '曲斗710', 19, 21, 10500.000000, '0', NOW(), '正常运行'),
('711', '曲斗711', 10, 30, 15000.000000, '0', NOW(), '正常运行'),
('712', '曲斗712', 18, 21, 10500.000000, '0', NOW(), '正常运行'),
('713', '曲斗713', 19, 21, 10500.000000, '0', NOW(), '正常运行');

-- 插入曲斗709的跨详细信息测试数据
INSERT INTO `t_mpd_qudou_span_detail` (`qu_dou_code`, `workshop_code`, `workshop_name`, `span_code`, `span_name`, `flour_type`, `empty_count`, `full_count`, `in_transit_count`, `flour_weight`, `data_update_time`) VALUES
-- 709一车间
('709', '70901', '709一车间', '01', '01跨', 'QF1', 2, 2, 1, 1000.000000, NOW()),
('709', '70902', '709一车间', '02', '02跨', 'QF1', 2, 2, 0, 1000.000000, NOW()),
-- 709二车间
('709', '70903', '709二车间', '03', '03跨', 'QF1', 2, 2, 0, 1000.000000, NOW()),
('709', '70904', '709二车间', '04', '04跨', 'QF1', 1, 3, 1, 1500.000000, NOW()),
-- 709三车间
('709', '70905', '709三车间', '05', '05跨', 'QF1', 2, 2, 0, 1000.000000, NOW()),
('709', '70906', '709三车间', '06', '06跨', 'QF1', 2, 2, 0, 1000.000000, NOW()),
-- 709四车间
('709', '70907', '709四车间', '07', '07跨', 'QF1', 2, 2, 1, 1000.000000, NOW()),
('709', '70908', '709四车间', '08', '08跨', 'QF1', 1, 3, 1, 1500.000000, NOW()),
-- 709五车间
('709', '70909', '709五车间', '09', '09跨', 'QF1', 2, 2, 0, 1000.000000, NOW()),
('709', '70910', '709五车间', '10', '10跨', 'QF1', 2, 2, 1, 1000.000000, NOW());
