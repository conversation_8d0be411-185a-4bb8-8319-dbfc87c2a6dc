<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.difference.InventoryDifferenceMapper">

    <!-- 结果映射 -->
    <resultMap id="InventoryDifferenceDTOMap" type="com.hvisions.rawmaterial.dto.difference.InventoryDifferenceDTO">
        <id column="id" property="id"/>
        <result column="material_type" property="materialType"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="erp_warehouse_code" property="erpWarehouseCode"/>
        <result column="erp_warehouse_name" property="erpWarehouseName"/>
        <result column="department" property="department"/>
        <result column="mes_current_stock" property="mesCurrentStock"/>
        <result column="mes_initial_stock" property="mesInitialStock"/>
        <result column="weighbridge_receipt_quantity" property="weighbridgeReceiptQuantity"/>
        <result column="solid_waste_quantity" property="solidWasteQuantity"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="unit" property="unit"/>
        <result column="sap_stock" property="sapStock"/>
        <result column="difference_quantity" property="differenceQuantity"/>
        <result column="statistics_start_date" property="statisticsStartDate"/>
        <result column="statistics_end_date" property="statisticsEndDate"/>
        <result column="status" property="status"/>
        <result column="processor_id" property="processorId"/>
        <result column="processor_name" property="processorName"/>
        <result column="process_time" property="processTime"/>
        <result column="process_remark" property="processRemark"/>
        <result column="sap_sync_time" property="sapSyncTime"/>
        <result column="report_date" property="reportDate"/>
        <result column="report_quantity" property="reportQuantity"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        d.id,
        d.material_type,
        d.material_code,
        d.material_name,
        d.erp_warehouse_code,
        d.erp_warehouse_name,
        d.department,
        d.mes_current_stock,
        d.mes_initial_stock,
        d.weighbridge_receipt_quantity,
        d.solid_waste_quantity,
        d.issued_quantity,
        d.unit,
        d.sap_stock,
        d.difference_quantity,
        d.statistics_start_date,
        d.statistics_end_date,
        d.status,
        CASE 
            WHEN d.status = 1 THEN '待处理'
            WHEN d.status = 2 THEN '已处理'
            ELSE '未知'
        END as status_desc,
        d.processor_id,
        d.processor_name,
        d.process_time,
        d.process_remark,
        d.sap_sync_time,
        d.report_date,
        d.report_quantity,
        d.create_time,
        d.update_time
    </sql>

    <!-- 分页查询库存差异处理记录 -->
    <select id="queryInventoryDifferences" resultMap="InventoryDifferenceDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_inventory_difference d
        WHERE d.deleted = 0
        <if test="queryDTO.materialCode != null and queryDTO.materialCode != ''">
            AND d.material_code LIKE CONCAT('%', #{queryDTO.materialCode}, '%')
        </if>
        <if test="queryDTO.materialName != null and queryDTO.materialName != ''">
            AND d.material_name LIKE CONCAT('%', #{queryDTO.materialName}, '%')
        </if>
        <if test="queryDTO.erpWarehouseCode != null and queryDTO.erpWarehouseCode != ''">
            AND d.erp_warehouse_code LIKE CONCAT('%', #{queryDTO.erpWarehouseCode}, '%')
        </if>
        <if test="queryDTO.erpWarehouseName != null and queryDTO.erpWarehouseName != ''">
            AND d.erp_warehouse_name LIKE CONCAT('%', #{queryDTO.erpWarehouseName}, '%')
        </if>
        <if test="queryDTO.department != null and queryDTO.department != ''">
            AND d.department LIKE CONCAT('%', #{queryDTO.department}, '%')
        </if>
        <if test="queryDTO.status != null">
            AND d.status = #{queryDTO.status}
        </if>
        <if test="queryDTO.statisticsStartDateBegin != null">
            AND d.statistics_start_date >= #{queryDTO.statisticsStartDateBegin}
        </if>
        <if test="queryDTO.statisticsStartDateEnd != null">
            AND d.statistics_start_date &lt;= #{queryDTO.statisticsStartDateEnd}
        </if>
        <if test="queryDTO.statisticsEndDateBegin != null">
            AND d.statistics_end_date >= #{queryDTO.statisticsEndDateBegin}
        </if>
        <if test="queryDTO.statisticsEndDateEnd != null">
            AND d.statistics_end_date &lt;= #{queryDTO.statisticsEndDateEnd}
        </if>
        <if test="queryDTO.processorName != null and queryDTO.processorName != ''">
            AND d.processor_name LIKE CONCAT('%', #{queryDTO.processorName}, '%')
        </if>
        <if test="queryDTO.processTimeBegin != null">
            AND d.process_time >= #{queryDTO.processTimeBegin}
        </if>
        <if test="queryDTO.processTimeEnd != null">
            AND d.process_time &lt;= #{queryDTO.processTimeEnd}
        </if>
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据ID查询差异处理记录详情 -->
    <select id="getInventoryDifferenceById" resultMap="InventoryDifferenceDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_inventory_difference d
        WHERE d.deleted = 0 AND d.id = #{id}
    </select>

    <!-- 查询待处理的差异记录 -->
    <select id="getPendingDifferences" resultMap="InventoryDifferenceDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_inventory_difference d
        WHERE d.deleted = 0 AND d.status = 1
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据物料和仓库查询最新的差异处理记录 -->
    <select id="getLatestDifferenceByMaterialAndWarehouse" resultMap="InventoryDifferenceDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_inventory_difference d
        WHERE d.deleted = 0 
        AND d.material_code = #{materialCode}
        AND d.erp_warehouse_code = #{erpWarehouseCode}
        ORDER BY d.statistics_end_date DESC
        LIMIT 1
    </select>

    <!-- 统计MES当前库存 -->
    <select id="getMesCurrentStock" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(inventory_weight), 0)
        FROM t_mpd_silo_realtime
        WHERE material_code = #{materialCode}
        AND deleted = 0
        group by silo_no
    </select>

    <!-- 统计地磅收货数量 -->
    <select id="getWeighbridgeReceiptQuantity" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(deo.demand_quantity), 0)
        FROM brewage.t_mp_daily_delivery dd
        LEFT JOIN brewage.t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN brewage.t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN brewage.t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = dd.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        LEFT JOIN (
        SELECT di.delivery_id,SUM(di.arrival_weight) arrival_weight
        FROM brewage.t_mp_delivery_item di
        WHERE di.deleted = 0
        GROUP BY di.delivery_id
        ) di ON di.delivery_id = dd.id
        WHERE dd.deleted = 0 and dd.state =2
        AND m.material_code = #{materialCode}
        AND deo.demand_date >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND deo.demand_date <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
    </select>

    <!-- 统计固废提报数量 -->
    <select id="getSolidWasteQuantity" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(loss_quantity), 0)
        FROM t_mpd_sorghum_loss
        WHERE material_code = #{materialCode}
        AND deleted = 0 and state = 1
        AND posting_time >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND posting_time <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
    </select>

    <!-- 统计发料数量 -->
    <select id="getIssuedQuantity" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(issued_quantity), 0)
        FROM t_mpd_material_issue
        WHERE material_code = #{materialCode}
        AND issue_time >= #{startDate}
        AND issue_time &lt;= #{endDate}
        AND deleted = 0
    </select>

    <!-- 获取物料和仓库的基本信息 -->
    <select id="getMaterialWarehouseInfo" resultType="com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO">
        SELECT DISTINCT
            m.id,
            m.material_code,
            t.material_type_name AS materialType,
            m.material_name
        FROM
            materials.hv_bm_material m
        LEFT JOIN materials.hv_bm_material_type t ON m.root_material_type = t.id
        WHERE
            t.id IN (27, 28)
    </select>

    <!-- 获取待处理差异统计信息 -->
    <select id="getPendingStatistics" resultType="java.util.HashMap">
        SELECT 
        COUNT(*) as total_count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity,
        COALESCE(SUM(CASE WHEN difference_quantity > 0 THEN 1 ELSE 0 END), 0) as surplus_count,
        COALESCE(SUM(CASE WHEN difference_quantity &lt; 0 THEN 1 ELSE 0 END), 0) as deficit_count,
        COALESCE(SUM(CASE WHEN difference_quantity > 0 THEN difference_quantity ELSE 0 END), 0) as total_surplus_quantity,
        COALESCE(SUM(CASE WHEN difference_quantity &lt; 0 THEN ABS(difference_quantity) ELSE 0 END), 0) as total_deficit_quantity,
        COALESCE(AVG(difference_quantity), 0) as average_difference_quantity
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 1
    </select>

    <!-- 获取已处理差异统计信息 -->
    <select id="getProcessedStatistics" resultType="java.util.HashMap">
        SELECT 
        COUNT(*) as total_count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity,
        COALESCE(SUM(CASE WHEN difference_quantity > 0 THEN 1 ELSE 0 END), 0) as surplus_count,
        COALESCE(SUM(CASE WHEN difference_quantity &lt; 0 THEN 1 ELSE 0 END), 0) as deficit_count,
        COALESCE(SUM(CASE WHEN difference_quantity > 0 THEN difference_quantity ELSE 0 END), 0) as total_surplus_quantity,
        COALESCE(SUM(CASE WHEN difference_quantity &lt; 0 THEN ABS(difference_quantity) ELSE 0 END), 0) as total_deficit_quantity,
        COALESCE(AVG(difference_quantity), 0) as average_difference_quantity,
        COALESCE(AVG(TIMESTAMPDIFF(HOUR, create_time, process_time)), 0) as average_processing_time_hours
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 2
        AND process_time >= #{startDate}
        AND process_time &lt;= #{endDate}
    </select>

    <!-- 按部门统计待处理差异 -->
    <select id="getPendingStatisticsByDepartment" resultType="java.util.HashMap">
        SELECT 
        department,
        COUNT(*) as count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 1
        GROUP BY department
        ORDER BY count DESC
    </select>

    <!-- 按物料类型统计待处理差异 -->
    <select id="getPendingStatisticsByMaterialType" resultType="java.util.HashMap">
        SELECT 
        CASE 
            WHEN LEFT(material_code, 2) = 'RM' THEN '原料'
            WHEN LEFT(material_code, 2) = 'PM' THEN '包装材料'
            WHEN LEFT(material_code, 2) = 'AM' THEN '辅助材料'
            WHEN LEFT(material_code, 2) = 'SM' THEN '备件材料'
            ELSE '其他'
        END as material_type,
        COUNT(*) as count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 1
        GROUP BY 
        CASE 
            WHEN LEFT(material_code, 2) = 'RM' THEN '原料'
            WHEN LEFT(material_code, 2) = 'PM' THEN '包装材料'
            WHEN LEFT(material_code, 2) = 'AM' THEN '辅助材料'
            WHEN LEFT(material_code, 2) = 'SM' THEN '备件材料'
            ELSE '其他'
        END
        ORDER BY count DESC
    </select>

    <!-- 按处理人统计已处理差异 -->
    <select id="getProcessedStatisticsByProcessor" resultType="java.util.HashMap">
        SELECT 
        processor_name,
        COUNT(*) as count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity,
        COALESCE(AVG(TIMESTAMPDIFF(HOUR, create_time, process_time)), 0) as average_processing_time_hours
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 2
        AND process_time >= #{startDate}
        AND process_time &lt;= #{endDate}
        AND processor_name IS NOT NULL
        GROUP BY processor_name
        ORDER BY count DESC
    </select>

    <!-- 按日期统计已处理差异 -->
    <select id="getProcessedStatisticsByDate" resultType="java.util.HashMap">
        SELECT 
        DATE(process_time) as process_date,
        COUNT(*) as count,
        COALESCE(SUM(difference_quantity), 0) as total_difference_quantity
        FROM t_mpd_inventory_difference
        WHERE deleted = 0 AND status = 2
        AND process_time >= #{startDate}
        AND process_time &lt;= #{endDate}
        GROUP BY DATE(process_time)
        ORDER BY process_date DESC
    </select>

    <!-- 批量查询库存差异数据 -->
    <select id="batchQueryByMaterialAndWarehouse" resultMap="InventoryDifferenceDTOMap">
        SELECT 
        <include refid="baseColumns"/>
        d.statistics_end_date,
        d.status,
        CASE 
            WHEN d.status = 1 THEN '待处理'
            WHEN d.status = 2 THEN '已处理'
            ELSE '未知'
        END as status_desc,
        d.processor_id,
        d.processor_name,
        d.process_time,
        d.process_remark,
        d.sap_sync_time,
        d.create_time,
        d.update_time
        FROM t_mpd_inventory_difference d
        WHERE 1=1
        <if test="materialCodes != null and materialCodes.size() > 0">
            AND d.material_code IN
            <foreach collection="materialCodes" item="materialCode" open="(" separator="," close=")">
                #{materialCode}
            </foreach>
        </if>
        <if test="warehouseCodes != null and warehouseCodes.size() > 0">
            AND d.erp_warehouse_code IN
            <foreach collection="warehouseCodes" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        ORDER BY d.create_time DESC
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE t_mpd_inventory_difference 
        SET status = #{status}, 
            update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入库存差异记录 -->
    <insert id="batchInsertDifferences" parameterType="java.util.List">
        INSERT INTO t_mpd_inventory_difference (
            material_code, material_name, erp_warehouse_code, erp_warehouse_name,
            department, mes_current_stock, mes_initial_stock, weighbridge_receipt_quantity,
            solid_waste_quantity, issued_quantity, unit, sap_stock, difference_quantity,
            statistics_start_date, statistics_end_date, status, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.materialCode}, #{item.materialName}, #{item.erpWarehouseCode}, #{item.erpWarehouseName},
                #{item.department}, #{item.mesCurrentStock}, #{item.mesInitialStock}, #{item.weighbridgeReceiptQuantity},
                #{item.solidWasteQuantity}, #{item.issuedQuantity}, #{item.unit}, #{item.sapStock}, #{item.differenceQuantity},
                #{item.statisticsStartDate}, #{item.statisticsEndDate}, #{item.status}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 获取统计信息 -->
    <select id="getStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as processed_count,
            SUM(ABS(difference_quantity)) as total_difference_quantity,
            AVG(ABS(difference_quantity)) as avg_difference_quantity
        FROM t_mpd_inventory_difference
        WHERE delete_flag = 0
    </select>

    <!-- 获取热点物料统计 -->
    <select id="getHotMaterials" resultType="java.util.Map">
        SELECT 
            material_code,
            material_name,
            COUNT(*) as difference_count,
            SUM(ABS(difference_quantity)) as total_difference
        FROM t_mpd_inventory_difference
        WHERE delete_flag = 0
        GROUP BY material_code, material_name
        ORDER BY difference_count DESC, total_difference DESC
        LIMIT 10
    </select>

    <select id="getRiceIssuedQuantity" resultType="java.math.BigDecimal">
        SELECT SUM(actual_quantity)
        FROM t_mpd_bran_transfer_order dd
        WHERE
        dd.deleted = 0
        AND dd.material_code = #{materialCode}
        AND dd.order_date >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND dd.order_date <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
    </select>

    <select id="getSorghumIssuedQuantity" resultType="java.math.BigDecimal">
        SELECT SUM(actual_quantity)
        FROM t_mpd_sorghum_dispense_order dd
        WHERE
        dd.deleted = 0
        AND dd.material_code = #{materialCode}
        AND dd.order_date >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND dd.order_date <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
    </select>

    <select id="getRiceIssuedInventoryDifferenceCenters"
            resultType="com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail">
        SELECT SUM(actual_quantity) as actualQuantity,material_code as materialCode,center,unit,material_name as materialName
        FROM t_mpd_bran_transfer_order dd
        WHERE
        dd.deleted = 0
        AND dd.material_code = #{materialCode}
        AND dd.order_date >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND dd.order_date <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
        GROUP BY dd.center
    </select>

    <select id="getSorghumIssuedInventoryDifferenceCenters"
            resultType="com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail">
        SELECT SUM(actual_quantity) as actualQuantity,material_code as materialCode,center,unit,material_name as materialName
        FROM t_mpd_sorghum_dispense_order dd
        WHERE
        dd.deleted = 0
        AND dd.material_code = #{materialCode}
        AND dd.order_date >= DATE_FORMAT(#{startDate},'%Y-%m-%d 00:00:00')
        AND dd.order_date <![CDATA[<=]]> DATE_FORMAT(#{endDate},'%Y-%m-%d 23:59:59')
        GROUP BY dd.center
    </select>
</mapper>