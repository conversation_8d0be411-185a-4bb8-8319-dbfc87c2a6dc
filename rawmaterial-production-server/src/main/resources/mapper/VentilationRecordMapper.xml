<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.mapper.VentilationRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="VentilationRecordDTOMap" type="com.hvisions.rawmaterial.dto.VentilationRecordDTO">
        <id column="id" property="id"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="work_order_date" property="workOrderDate"/>
        <result column="silo_name" property="siloName"/>
        <result column="silo_code" property="siloCode"/>
        <result column="material_name" property="materialName"/>
        <result column="material_code" property="materialCode"/>
        <result column="ventilation_duration" property="ventilationDuration"/>
        <result column="temperature_before" property="temperatureBefore"/>
        <result column="temperature_after" property="temperatureAfter"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="temperature_maintainer_id" property="temperatureMaintainerId"/>
        <result column="temperature_maintainer_name" property="temperatureMaintainerName"/>
        <result column="temperature_maintain_time" property="temperatureMaintainTime"/>
        <result column="remark" property="remark"/>
        <result column="central_control_task_id" property="centralControlTaskId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        v.id,
        v.serial_number,
        v.work_order_number,
        v.work_order_date,
        v.silo_name,
        v.silo_code,
        v.material_name,
        v.material_code,
        v.ventilation_duration,
        v.temperature_before,
        v.temperature_after,
        v.start_time,
        v.end_time,
        v.status,
        CASE 
            WHEN v.status = 1 THEN '执行中'
            WHEN v.status = 2 THEN '已完成'
            ELSE '未知'
        END as status_desc,
        v.temperature_maintainer_id,
        v.temperature_maintainer_name,
        v.temperature_maintain_time,
        v.remark,
        v.central_control_task_id,
        v.create_time,
        v.update_time
    </sql>

    <!-- 分页查询通风记录 -->
    <select id="queryVentilationRecords" resultMap="VentilationRecordDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_ventilation_record v
        WHERE v.deleted = 0
        <if test="queryDTO.workOrderNumber != null and queryDTO.workOrderNumber != ''">
            AND v.work_order_number LIKE CONCAT('%', #{queryDTO.workOrderNumber}, '%')
        </if>
        <if test="queryDTO.siloCode != null and queryDTO.siloCode != ''">
            AND v.silo_code LIKE CONCAT('%', #{queryDTO.siloCode}, '%')
        </if>
        <if test="queryDTO.materialName != null and queryDTO.materialName != ''">
            AND v.material_name LIKE CONCAT('%', #{queryDTO.materialName}, '%')
        </if>
        <if test="queryDTO.status != null">
            AND v.status = #{queryDTO.status}
        </if>
        <if test="queryDTO.startTimeBegin != null">
            AND v.start_time >= #{queryDTO.startTimeBegin}
        </if>
        <if test="queryDTO.startTimeEnd != null">
            AND v.start_time &lt;= #{queryDTO.startTimeEnd}
        </if>
        <if test="queryDTO.workOrderDateBegin != null">
            AND v.work_order_date >= #{queryDTO.workOrderDateBegin}
        </if>
        <if test="queryDTO.workOrderDateEnd != null">
            AND v.work_order_date &lt;= #{queryDTO.workOrderDateEnd}
        </if>
        <if test="queryDTO.centralControlTaskId != null and queryDTO.centralControlTaskId != ''">
            AND v.central_control_task_id = #{queryDTO.centralControlTaskId}
        </if>
        <if test="queryDTO.temperatureBefore != null">
            AND v.temperature_before = #{queryDTO.temperatureBefore}
        </if>
        <if test="queryDTO.temperatureAfter != null">
            AND v.temperature_after = #{queryDTO.temperatureAfter}
        </if>
        <if test="queryDTO.ventilationDuration != null">
            AND v.ventilation_duration = #{queryDTO.ventilationDuration}
        </if>
        ORDER BY v.create_time DESC
    </select>

    <!-- 根据工单号查询通风记录 -->
    <select id="getVentilationRecordByWorkOrderNumber" resultMap="VentilationRecordDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_ventilation_record v
        WHERE v.deleted = 0 AND v.work_order_number = #{workOrderNumber}
    </select>

    <!-- 根据中控任务ID查询通风记录 -->
    <select id="getVentilationRecordByTaskId" resultMap="VentilationRecordDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_ventilation_record v
        WHERE v.deleted = 0 AND v.central_control_task_id = #{centralControlTaskId}
    </select>

    <!-- 查询执行中的通风记录 -->
    <select id="getExecutingRecords" resultMap="VentilationRecordDTOMap">
        SELECT
        <include refid="baseColumns"/>
        FROM t_mpd_ventilation_record v
        WHERE v.deleted = 0 AND v.status = 1
        ORDER BY v.start_time DESC
    </select>

    <!-- 根据日期查询当天的工单数量 -->
    <select id="countByWorkOrderDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_ventilation_record v
        WHERE v.deleted = 0 AND DATE(v.work_order_date) = #{workOrderDate}
    </select>

</mapper>
