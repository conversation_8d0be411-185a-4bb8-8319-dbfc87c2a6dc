<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumDispenseOrderMapper">

    <select id="getSorghumDispenseOrderPageList" resultMap="orderMap">
        SELECT sso.*,pt.`name` production_type_name,p.plan_no,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.send_warehouse_id)  send_warehouse,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.accept_warehouse_id)  accept_warehouse,
        ( SELECT SUM(actual_quantity) FROM t_mpd_sd_order_detail d WHERE d.deleted = 0 AND d.order_id = sso.id AND d.sap_state = "1" ) postQuantity
        FROM `t_mpd_sorghum_dispense_order` sso
        LEFT JOIN t_mpd_sorghum_dispense_plan p ON p.`id` = sso.plan_id AND p.deleted = 0
        LEFT JOIN t_mpd_production_type pt ON pt.`code` = sso.production_type_code AND pt.deleted = 0

        WHERE sso.deleted = 0
        AND sso.center IN ('709','713','718')
        <if test="materialCode != null and materialCode != ''">
            AND sso.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND sso.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="centerId != null">
            AND sso.`center_id` = #{centerId}
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(sso.order_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY sso.order_date DESC,sso.id DESC
    </select>
    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageDTO">
        <id column="id" property="id"/>
        <result property="planId" column="plan_id"/>
        <result property="planNo" column="plan_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="centerCode" column="center"/>
        <result property="orderDate" column="order_date"/>
        <result property="productionTypeCode" column="production_type_code"/>
        <result property="productionTypeName" column="production_type_name"/>
        <result property="sendWarehouseId" column="send_warehouse_id"/>
        <result property="acceptWarehouseId" column="accept_warehouse_id"/>
        <result property="sendWarehouse" column="send_warehouse"/>
        <result property="acceptWarehouse" column="accept_warehouse"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="requirementQuantity" column="requirement_quantity"/>
        <result property="inventoryQuantity" column="inventory_quantity"/>
        <result property="planQuantity" column="plan_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="createTime" column="create_time"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>
    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.SorghumDispenseDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_sd_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0 AND ssod.order_id = #{id}
    </select>

    <select id="selectPostQuantityByOrderId" resultType="java.math.BigDecimal">
        SELECT SUM(actual_quantity)
        FROM t_mpd_sd_order_detail
        WHERE deleted = 0 AND order_id = #{id} AND sap_state = "1"
    </select>
    <select id="getSorghumDispenseOrderDetailPageList"
            resultType="com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.SorghumDispenseDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
        (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
        (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_sd_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0
          and ssod.flow_batch = #{batch}
        order by ssod.id desc
    </select>
</mapper>
