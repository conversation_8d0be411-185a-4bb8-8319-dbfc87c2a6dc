<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumLossMapper">

    <resultMap id="orderMap_old" type="com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossPageDTO">
        <id column="id" property="id"/>
        <result property="centerId" column="center_id"/>
        <result property="center" column="center"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="postingTime" column="posting_time"/>
        <result property="issueQuantity" column="issue_quantity"/>
        <result property="lossQuantity" column="loss_quantity"/>
        <result property="state" column="state"/>
        <result property="matDoc" column="mat_doc"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>

    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossPageDTO">
        <id column="id" property="id"/>
        <result property="centerId" column="center_id"/>
        <result property="center" column="center"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="postingTime" column="posting_time"/>
        <result property="issueQuantity" column="issue_quantity"/>
        <result property="lossQuantity" column="loss_quantity"/>
        <result property="state" column="state"/>
        <result property="matDoc" column="mat_doc"/>
        <result property="orderNo" column="order_no"/>
    </resultMap>

    <select id="getSorghumLossPageList_old" resultMap="orderMap_old">
        SELECT l.*
        FROM `t_mpd_sorghum_loss` l
        WHERE l.deleted = 0
        <if test="centerCode != null and centerCode != ''">
            AND l.`center` LIKE concat('%',#{centerCode},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(l.start_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
            AND DATE_FORMAT(l.end_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY l.id DESC
    </select>

    <select id="selectDetail_old" resultType="com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDetailListDTO">
        SELECT ld.*,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ld.send_silo_id AND rl.deleted = 0) sendSilo,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ld.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_sorghum_loss_detail ld
        WHERE ld.deleted = 0 AND ld.order_id = #{id}
    </select>

    <select id="getSorghumLossPageList" resultMap="orderMap">
        SELECT l.id,
        l.center_id,
        l.center,
        l.material_id,
        l.material_code,
        l.material_name,
        l.unit,
        l.start_date,
        l.end_date,
        l.posting_time,
        l.issue_quantity,
        l.loss_quantity,
        l.state,
        l.mat_doc,
        l.order_no
        FROM `t_mpd_sorghum_loss` l
        WHERE l.deleted = 0
        <if test="centerCode != null and centerCode != ''">
            AND l.`center` LIKE concat('%',#{centerCode},'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND l.`order_no` LIKE concat('%',#{orderNo},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(l.start_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
            AND DATE_FORMAT(l.end_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY l.id DESC
    </select>

    <select id="selectDetailByCertificateNumber"
            resultType="com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDetailDTO">
        SELECT *
        FROM t_mpd_sorghum_loss_detail
        WHERE deleted = 0 AND certificate_number = #{certificateNumber}
    </select>

    <select id="getCenterIdByWarehouseCode" resultType="java.lang.Integer">
        SELECT center_id
        FROM brewage.t_mp_inventory_location i
        WHERE i.`code` = #{code}
    </select>

    <select id="getCenterCodeById" resultType="java.lang.String">
        SELECT l.`code` as center
        FROM equipment.hv_bm_location l
        WHERE l.id = #{centerId}
    </select>

    <select id="countByOrderNoPrefix" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_mpd_sorghum_loss
        WHERE deleted = 0 AND order_no LIKE CONCAT(#{orderNoPrefix}, '%')
    </select>

</mapper>
