<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.VendorMapper">

    <!-- 分页查询sap库存地点维护列表 -->
    <select id="getVendorPageList"
            resultType="com.hvisions.purchase.dto.purchase.vendor.VendorPageDTO">
        SELECT  v.*,o.`name` organization_name
        FROM t_mp_vendor v
        LEFT JOIN t_mp_purchasing_organization o ON o.id = v.organization_id AND o.deleted = 0
        where v.deleted = 0
        <if test="name != null and name != ''">
            AND v.`name` LIKE concat('%',#{name},'%')
        </if>
        <if test="code != null and code != ''">
            AND v.`code` LIKE concat('%',#{code},'%')
        </if>
        ORDER BY v.id DESC
    </select>

</mapper>
