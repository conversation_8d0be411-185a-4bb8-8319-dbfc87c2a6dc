<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.purchase.purchase.dao.TMpVendorManageMapper">


    <select id="getVendorManagePage"
            resultType="com.hvisions.purchase.dto.purchase.demand.order.VendorManagePageDTO">
        select vm.*, ddi.inspect_result, ddd.license_plate_number, ddd.delivery_number, ddd.material_code, ddd.material_name, it.inspection_order,
        ddd.srm_batch, ddd.srm_variety, ddd.srm_place_production, ddd.estimated_number estimatedNumber, ddd.admission_time
        from t_mp_vendor_manage vm
        left join t_mp_daily_delivery_detail ddd on ddd.id = vm.delivery_detail_id and ddd.deleted = 0
        left join t_mp_daily_delivery_inspect ddi on ddi.delivery_detail_id = vm.delivery_detail_id and ddi.deleted = 0
        LEFT JOIN t_qa_inspection_task it on it.associated_document = ddd.delivery_number and it.id = (
            select max(id) from t_qa_inspection_task t where t.associated_document = ddd.delivery_number)
        where vm.deleted = 0
        <if test="licensePlateNumber != null and licensePlateNumber != '' ">
            AND ddd.license_plate_number LIKE '%${licensePlateNumber}%'
        </if>
        <if test="vendorName != null and vendorName != ''">
            AND vm.vendor_name = #{vendorName}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != '' ">
            AND ddd.license_plate_number LIKE '%${licensePlateNumber}%'
        </if>
        <if test="state != null and state != ''">
            AND vm.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            AND vm.delivery_time BETWEEN #{startTime} and #{endTime}
        </if>
        order by vm.create_time desc
    </select>
</mapper>