package com.hvisions.purchase.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 11:39
 */
@Getter
@Setter
@NoArgsConstructor
public class SapBaseMessageDto {

    @JsonProperty("HEADER_KEY")
    private String headerKey;

    @JsonProperty("MSGTY")
    private String msgty;

    @JsonProperty("MSGTX")
    private String msgtx;

    @JsonProperty("EBELN")
    private String ebeln;

    @JsonProperty("EXT01")
    private String extOne = "";

    @JsonProperty("EXT02")
    private String extTwo = "";

    @JsonProperty("EXT03")
    private String extThree = "";

    @JsonProperty("EXT04")
    private String extFour = "";

    @JsonProperty("EXT05")
    private String extFive = "";
}
