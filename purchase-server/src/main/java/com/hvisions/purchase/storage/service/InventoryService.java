//package com.hvisions.purchase.storage.service;
//
//import com.hvisions.purchase.dto.storage.inventory.InventoryFinishDTO;
//import com.hvisions.purchase.dto.storage.inventory.InventoryGenerateDTO;
//import com.hvisions.purchase.dto.storage.inventory.InventoryPageDTO;
//import com.hvisions.purchase.dto.storage.inventory.InventoryPageQueryDTO;
//import com.hvisions.common.dto.ExcelExportDto;
//import org.springframework.data.domain.Page;
//
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Description:
// * @author: Jcao
// * @time: 2022/4/24 16:35
// */
//public interface InventoryService {
//
//    Page<InventoryPageDTO> getInventoryPageList(InventoryPageQueryDTO inventoryPageQueryDTO);
//
//    Map<Integer, BigDecimal> getSapInventory(Integer id);
//
//    Integer generateInventory(InventoryGenerateDTO inventoryGenerateDTO);
//
//    Integer startInventory(Integer id);
//
//    Integer finishInventory(List<InventoryFinishDTO> inventoryFinishs, Integer id, String checkPeople);
//
//    Integer deleteInventory(Integer id);
//
//    ExcelExportDto exportData(Integer id);
//}
