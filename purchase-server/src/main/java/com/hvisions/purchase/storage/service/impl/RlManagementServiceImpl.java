//package com.hvisions.purchase.storage.service.impl;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.hvisions.common.exception.BaseKnownException;
//import com.hvisions.common.utils.DtoMapper;
//import com.hvisions.common.utils.PageHelperUtil;
//import com.hvisions.common.vo.ResultVO;
//import com.hvisions.materialsmsd.materials.client.MaterialClient;
//import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
//import com.hvisions.purchase.dto.storage.Rl.*;
//import com.hvisions.purchase.purchase.consts.MaterialConst;
//import com.hvisions.purchase.purchase.dao.ProcurementPlanMapper;
//import com.hvisions.purchase.sap.dto.SapBaseOutputDto;
//import com.hvisions.purchase.sap.service.SapService;
//import com.hvisions.purchase.storage.dao.RlManagementMapper;
//import com.hvisions.purchase.storage.dao.WarehouseDataMapper;
//import com.hvisions.purchase.storage.entity.TMpdWarehouseData;
//import com.hvisions.purchase.storage.service.RlManagementService;
//import com.hvisions.purchase.utils.StringUtil;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.data.domain.Page;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.*;
//
///**
// * @Description: 库区库位管理
// * @author: Jcao
// * @time: 2022/4/24 13:26
// */
//@Service
//public class RlManagementServiceImpl implements RlManagementService {
//
//    @Resource
//    private RlManagementMapper rlManagementMapper;
//
//    @Resource
//    private MaterialClient materialClient;
//
//    @Resource
//    private WarehouseDataMapper warehouseDataMapper;
//
//    @Resource
//    private SapService sapService;
//
//    @Resource
//    public ProcurementPlanMapper procurementPlanMapper;
//
//
//    /*
//     * @Description: 分页查询库区列表
//     *
//     * <AUTHOR>
//     * @param rlManagementPageQueryDTO:
//     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.storage.Rl.RlManagementPageDTO>
//     */
//    @Override
//    public Page<RlManagementPageDTO> getRlAreaPageList(RlManagementPageQueryDTO rlManagementPageQueryDTO) {
//        return PageHelperUtil.getPage(rlManagementMapper::getRlAreaPageList, rlManagementPageQueryDTO, RlManagementPageDTO.class);
//    }
//
//    /*
//     * @Description: 分页查询库位列表
//     *
//     * <AUTHOR>
//     * @param rlManagementPageQueryDTO:
//     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.storage.Rl.RlManagementPageDTO>
//     */
//    @Override
//    public Page<LocationPageDTO> getRlLocationPageList(RlManagementPageQueryDTO rlManagementPageQueryDTO) {
//        return PageHelperUtil.getPage(rlManagementMapper::getRlLocationPageList, rlManagementPageQueryDTO, LocationPageDTO.class);
//    }
//
//    /**
//     * @param queryDTO
//     * @return java.util.List<com.hvisions.purchase.dto.storage.Rl.LocationDetailDTO>
//     * @Description 条件查询库位列表
//     * <AUTHOR>
//     * @Date 2022-8-24 14:11
//     **/
//    @Override
//    public List<LocationDetailDTO> getLocationList(LocationDetailQueryDTO queryDTO) {
//        return rlManagementMapper.getLocationList(queryDTO);
//    }
//
//    /*
//     * @Description: 获取库内数据列表
//     *
//     * <AUTHOR>
//     * @param rlManagementPageQueryDTO:
//     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.storage.Rl.RlManagementPageDTO>
//     */
//    @Override
//    public Page<StorageDataPageDTO> getStorageDataPageList(StorageDataPageQueryDTO warehouseDataPageQueryDTO) {
//        return PageHelperUtil.getPage(rlManagementMapper::getStorageDataPageList, warehouseDataPageQueryDTO, StorageDataPageDTO.class);
//    }
//
//    @Override
//    public Integer addRlManagement(RlManagementDTO rlManagementDTO) {
//        TMpdRlManagement rlManagement = DtoMapper.convert(rlManagementDTO, TMpdRlManagement.class);
//        rlManagement.setUpdateTime(new Date());
//        rlManagement.setCreateTime(new Date());
//        return rlManagementMapper.insert(rlManagement);
//    }
//
//    @Override
//    public Integer updateRlManagement(RlManagementDTO rlManagementDTO) {
//        TMpdRlManagement rlManagement = DtoMapper.convert(rlManagementDTO, TMpdRlManagement.class);
//        rlManagement.setUpdateTime(new Date());
//        rlManagement.setCreatorId(null);
//        return rlManagementMapper.updateById(rlManagement);
//    }
//
//    @Override
//    public Integer deleteRlManagement(Integer id) {
//        return rlManagementMapper.deleteById(id);
//    }
//
//    /*
//     * @description: 根据编码获取库位库区数据
//     * <AUTHOR>
//     * @date 2022/6/28 9:59
//     * @param centralData
//     * @return com.hvisions.purchase.dto.storage.Rl.RlManagementDetailDTO
//     */
//
//    @Override
//    public RlManagementDetailDTO getRlManagementDetailByCentralData(String code) {
//        return rlManagementMapper.getRlManagementDetailByCentralData(code);
//    }
//
//    /*
//     * @description: 仓库数量转移
//     * <AUTHOR>
//     * @date 2022/6/28 16:58
//     * @param outId 出仓id
//     * @param inId 入仓id
//     * @param quantity 转移数量
//     * @return void
//     */
//    @Override
//    @Transactional
//    public String stockOutAndIn(Integer outId, Integer inId, BigDecimal quantity) {
//
//        /**
//         * 出入库操作:
//         *      1、获取出库库位下的库位数据，将最早的物料先出
//         *      2、获取入库库位，判断是否存在库位数据，相同批次数据合并，不同批次数据新增
//         */
//
//        try {
//
//            Set<String> batches = new HashSet<>();
//            TMpdRlManagement outStorage = rlManagementMapper.selectById(outId);
//
//            TMpdRlManagement inStorage = rlManagementMapper.selectById(inId);
//
//            if (outStorage == null) {
//                throw new BaseKnownException(10000, "出仓筒仓不存在，无法出仓！");
//            }
//
//            List<TMpdWarehouseData> outStorageDataList = warehouseDataMapper.selectList(Wrappers.<TMpdWarehouseData>lambdaQuery()
//                    .eq(TMpdWarehouseData::getStorageId, outId)
//                    .eq(TMpdWarehouseData::getDeleted, 0)
//                    .orderByAsc(TMpdWarehouseData::getCreateTime)
//            );
//
//            BigDecimal remainQuantity = quantity; // 记录剩余入库数量
//
////            if (outStorageDataList.size() == 0) {
////                // 出库筒仓没有数据
////                throw new BaseKnownException(10000, "出仓筒仓没有数据，无法出仓！");
////            }
//
//
//            if (inId == null || inStorage == null) { // 没有入仓，则出仓单独发出
//                for (TMpdWarehouseData outStorageData : outStorageDataList) {
//
//                    if (outStorageData.getStockQuantity().compareTo(remainQuantity) == 1) { // 剩余入库数量小于出库库存数量，出库库存减少，入库库存增加
//
//                        outStorageData.setStockQuantity(outStorageData.getStockQuantity().subtract(remainQuantity));
//                        batches.add(outStorageData.getBatch());
//                        warehouseDataMapper.updateById(outStorageData);
//                        return StringUtils.join(batches, ",");
//                    } else {
//                        // 剩余入库数量大于出库库存数量，全部出库（删除出库），入库增加，剩余入库数量减少
//                        remainQuantity = remainQuantity.subtract(outStorageData.getStockQuantity());
//                        outStorageData.setStockQuantity(outStorageData.getStockQuantity().subtract(remainQuantity));
//                        batches.add(outStorageData.getBatch());
//                        warehouseDataMapper.deleteById(outStorageData.getId());
//                    }
//                }
//            }
//
//
//            if (outStorageDataList.size() > 0 && inStorage != null) { // 存在出库数据，且入库库位存在
//                for (TMpdWarehouseData outStorageData : outStorageDataList) {
//
//                    if (remainQuantity.compareTo(new BigDecimal(0)) == 0) {
//                        return StringUtils.join(batches, ",");
//                    }
//
//                    if (outStorageData.getStockQuantity().compareTo(remainQuantity) == 1) { // 剩余入库数量小于出库库存数量，出库库存减少，入库库存增加
//                        TMpdWarehouseData inStorageData = DtoMapper.convert(outStorageData, TMpdWarehouseData.class);
//                        inStorageData.setStorageId(inId);
//                        inStorageData.setStockQuantity(remainQuantity);
//
//                        outStorageData.setStockQuantity(outStorageData.getStockQuantity().subtract(remainQuantity));
//                        batches.add(outStorageData.getBatch());
//                        warehouseDataMapper.updateById(outStorageData);
//                        this.stockIn(inStorageData);
//
//                        return StringUtils.join(batches, ",");
//                    } else {
//                        // 剩余入库数量大于出库库存数量，全部出库（删除出库），入库增加，剩余入库数量减少
//                        TMpdWarehouseData inStorageData = DtoMapper.convert(outStorageData, TMpdWarehouseData.class);
//                        inStorageData.setStorageId(inId);
//                        remainQuantity = remainQuantity.subtract(outStorageData.getStockQuantity());
//
//                        outStorageData.setStockQuantity(outStorageData.getStockQuantity().subtract(remainQuantity));
//                        batches.add(outStorageData.getBatch());
//                        warehouseDataMapper.deleteById(outStorageData.getId());
//                        this.stockIn(inStorageData);
//                    }
//                }
//            }
////            if (remainQuantity.compareTo(new BigDecimal(0)) == 1) { // 剩余入库数量大于0
////                throw new BaseKnownException(10000, outStorage.getName() + "库位库存不足以出库");
////            }
//            return StringUtils.join(batches, ",");
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new BaseKnownException(10000, e.getMessage());
//        }
//    }
//
//
//    /**
//     * @param warehouseData
//     * @return void
//     * @description: 根据批次入库
//     * <AUTHOR>
//     * @date 2022-7-28 15:55
//     */
//    @Override
//    public void stockIn(TMpdWarehouseData warehouseData) {
//        List<TMpdWarehouseData> warehouseDataList = warehouseDataMapper.selectList(Wrappers.<TMpdWarehouseData>lambdaQuery()
//                .eq(TMpdWarehouseData::getStorageId, warehouseData.getStorageId())
//                .eq(TMpdWarehouseData::getDeleted, "0")
//        );
//
//        for (TMpdWarehouseData oldWarehouseData : warehouseDataList) {
//            if (warehouseData.getBatch().equals(oldWarehouseData.getBatch())) {
//                oldWarehouseData.setStockQuantity(oldWarehouseData.getStockQuantity().add(warehouseData.getStockQuantity()));
//                oldWarehouseData.setUpdateTime(new Date());
//                warehouseDataMapper.updateById(oldWarehouseData);
//                return;
//            }
//
//        }
//        warehouseData.setCreateTime(new Date());
//        warehouseDataMapper.insert(warehouseData);
//    }
//
//    @Override
//    public LocationSafetyStockDTO getLocationSafetyStock(LocationDetailQueryDTO queryDTO) {
//        return rlManagementMapper.getLocationSafetyStock(queryDTO);
//    }
//
//    @Override
//    public List<StockUsageDetailDTO> getStockUsageDetail() {
//
//        // 获取高粱，稻壳sap库存
//        List<String> GL = rlManagementMapper.getMaterialCodesByTypeCode("GL");
//        List<String> DK = rlManagementMapper.getMaterialCodesByTypeCode("DK");
//        BigDecimal glTotal = new BigDecimal(0);
//        BigDecimal dkTotal = new BigDecimal(0);
//        try {
//            // 总数累加
//            List<SapBaseOutputDto> outputs = sapService.inventoryQuery(GL).getEvOutput().getOutput();
//            List<SapBaseOutputDto> outputs1 = sapService.inventoryQuery(DK).getEvOutput().getOutput();
//            for (SapBaseOutputDto output : outputs) {
//                glTotal = glTotal.add(new BigDecimal(output.getQty()));
//            }
//            for (SapBaseOutputDto output : outputs1) {
//                dkTotal = dkTotal.add(new BigDecimal(output.getQty()));
//            }
//        } catch (Exception e) {
//            throw new BaseKnownException(10000, "SAP接口调用异常，" + e.getMessage());
//        }
//
//        List<StockUsageDetailDTO> stockUsageDetail = rlManagementMapper.getStockUsageDetail();
//
//        if (stockUsageDetail.size() > 0) {
//            for (StockUsageDetailDTO stockUsageDetailDTO : stockUsageDetail) {
//                if (stockUsageDetailDTO.getMaterialTypeCode().equals("GL")) {
//                    stockUsageDetailDTO.setSapWeight(glTotal);
//                }
//                if (stockUsageDetailDTO.getMaterialTypeCode().equals("DK")) {
//                    stockUsageDetailDTO.setSapWeight(dkTotal);
//                }
//            }
//        }
//
//        return stockUsageDetail;
//    }
//
//    /***
//     * @Description 根据物料id获取物料库存详情
//     *
//     * <AUTHOR>
//     * @Date 2023-2-23 16:53
//     * @param materialId
//     * @return com.hvisions.purchase.dto.storage.Rl.MaterialStockDetailDTO
//     **/
//    @Override
//    public MaterialStockDetailDTO getMaterialStockDetailByMaterialId(Integer materialId) {
//        MaterialStockDetailDTO materialStockDetailDTO = new MaterialStockDetailDTO();
//        ResultVO<MaterialDTO> resultVO = materialClient.getMaterialById(materialId);
//        if (StringUtil.isNotEmpty(resultVO.getData())) {
//            try {
//                materialStockDetailDTO.setMaterialId(materialId);
//                materialStockDetailDTO.setMaterialName(resultVO.getData().getMaterialName());
//                materialStockDetailDTO.setMaterialCode(resultVO.getData().getMaterialCode());
//                materialStockDetailDTO.setStockQuantity(new BigDecimal(rlManagementMapper.getMaterialStockQuantityByMaterialCode(resultVO.getData().getMaterialCode())));
//                materialStockDetailDTO.setWaitReceiveWeight(new BigDecimal(rlManagementMapper.getMaterialWaitReceiveQuantityByMaterialCode(resultVO.getData().getMaterialCode())));
//                materialStockDetailDTO.setWaitPostingWeight(new BigDecimal(rlManagementMapper.getMaterialWaitPostingQuantityByMaterialCode(resultVO.getData().getMaterialCode())));
//                List<String> materialCodes = new ArrayList<>();
//                materialCodes.add(resultVO.getData().getMaterialCode());
//                List<SapBaseOutputDto> outputs = sapService.inventoryQuery(materialCodes).getEvOutput().getOutput();
//                if (StringUtil.isNotEmpty(outputs) && outputs.size() > 0) {
//                    materialStockDetailDTO.setSapWeight(StringUtil.isNotEmpty(outputs.get(0)) ? new BigDecimal(outputs.get(0).getQty()) : BigDecimal.ZERO);
//                }
//            } catch (Exception e) {
//                throw new BaseKnownException(10000, "获取SAP库存接口调用异常，" + e.getMessage());
//            }
//        }
//        return materialStockDetailDTO;
//    }
//
//    @Override
//    public PurchaseMaterialStockDetailDTO getPurchaseStockDetailByMaterialId(Integer materialId) {
//        ResultVO<MaterialDTO> resultVO = materialClient.getMaterialById(materialId);
//        if (StringUtil.isNotEmpty(resultVO.getData())) {
//            PurchaseMaterialStockDetailDTO purchaseStockDetail = rlManagementMapper.getPurchaseStockDetail(resultVO.getData().getMaterialCode(), resultVO.getData().getMaterialTypeName());
//            purchaseStockDetail.setMaterialId(resultVO.getData().getId());
//            purchaseStockDetail.setMaterialCode(resultVO.getData().getMaterialCode());
//            purchaseStockDetail.setMaterialName(resultVO.getData().getMaterialName());
//            return purchaseStockDetail;
//        }
//        return null;
//    }
//
//    /**
//     * @Description 获取原辅料 库存数量、待收货数量、待过账数量 物料列表
//     *
//     * <AUTHOR>
//     * @Date 2024-6-3 15:38
//     * @param
//     * @return java.util.List<com.hvisions.purchase.dto.storage.Rl.MaterialStockDetailDTO>
//     **/
//    @Override
//    public List<MaterialStockDetailDTO> getMaterialStockDetailList() {
//        List<Integer> materialIds = procurementPlanMapper.getMaterialIds();
//        List<MaterialStockDetailDTO> list = new ArrayList<>();
//        for (Integer materialId : materialIds) {
//            MaterialStockDetailDTO materialStockDetailDTO = rlManagementMapper.getMaterialStockDetail(materialId);
//            List<String> materialCodes = new ArrayList<>();
//            materialCodes.add(materialStockDetailDTO.getMaterialCode());
//            List<SapBaseOutputDto> outputs;
//            if (MaterialConst.LQM1.equals(materialStockDetailDTO.getMaterialCode()) || MaterialConst.LQM2.equals(materialStockDetailDTO.getMaterialCode()) || MaterialConst.LQM3.equals(materialStockDetailDTO.getMaterialCode())) {
//                continue;
//            } else {
//                outputs = sapService.inventoryQuery(materialCodes).getEvOutput().getOutput();
//            }
//            if (StringUtil.isNotEmpty(outputs) && outputs.size() > 0) {
//                materialStockDetailDTO.setSapWeight(StringUtil.isNotEmpty(outputs.get(0)) ? new BigDecimal(outputs.get(0).getQty()) : BigDecimal.ZERO);
//            }
//            list.add(materialStockDetailDTO);
//        }
//        return list;
//    }
//}
