package com.hvisions.purchase.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MonthActualDataExcel {
    /**
     * 车间编码
     */
    @ExcelProperty(value = "车间编码")
    private String locationCode;
    /**
     * 年
     */
    @ExcelProperty(value = "年")
    private Integer year;
    /**
     * 月
     */
    @ExcelProperty(value = "月")
    private String month;

    /**
     * 实际投粮
     */
    @ExcelProperty(value = "实际投粮")
    private BigDecimal actualFeeding;

    /**
     * 实际耗粮
     */
    @ExcelProperty(value = "实际耗粮")
    private BigDecimal actualConsumption;

    /**
     * 实际基酒产出
     */
    @ExcelProperty(value = "实际基酒产出")
    private BigDecimal actualWinOutput;
}









