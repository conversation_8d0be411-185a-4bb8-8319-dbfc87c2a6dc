package com.hvisions.purchase.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import org.springframework.stereotype.Component;

@Component
public class Print709ClientFallBack extends BaseFallbackFactory<Print709Client> {
    public Print709ClientFallBack() {
    }

    public Print709Client getFallBack(ResultVO vo) {
        return new Print709Client() {
            public ResultVO print(PrintDTO printDTO) {
                return vo;
            }
        };

    }
}
