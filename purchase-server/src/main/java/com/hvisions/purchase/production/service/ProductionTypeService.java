//package com.hvisions.purchase.production.service;
//
//
//import com.hvisions.purchase.dto.production.production.type.ProductionTypeDTO;
//import com.hvisions.purchase.dto.production.production.type.ProductionTypePageDTO;
//import com.hvisions.purchase.dto.production.production.type.ProductionTypePageQueryDTO;
//import org.springframework.data.domain.Page;
//
///**
// * <AUTHOR>
// * @description:生产类型
// * @date 2022/4/22 10:18
// */
//public interface ProductionTypeService {
//
//    Page<ProductionTypePageDTO> getProductionTypePageList(ProductionTypePageQueryDTO productionTypePageQueryDTO);
//
//    Integer addProductionType(ProductionTypeDTO productionTypeDTO);
//
//    Integer updateProductionType(ProductionTypeDTO productionTypeDTO);
//
//    Integer deleteProductionType(Integer id);
//
//}
