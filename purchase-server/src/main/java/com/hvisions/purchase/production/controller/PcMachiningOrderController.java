//package com.hvisions.purchase.production.controller;
//
//import com.hvisions.purchase.dto.production.pc.machining.order.PcMachiningOrderDTO;
//import com.hvisions.purchase.dto.production.pc.machining.order.PcMachiningOrderPageDTO;
//import com.hvisions.purchase.dto.production.pc.machining.order.PcMachiningOrderPageQueryDTO;
//import com.hvisions.purchase.production.service.PcMachiningOrderService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.data.domain.Page;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//
///**
// * <AUTHOR>
// * @description:品创预处理加工工单
// * @date 2022/4/22 10:18
// */
//@RestController
//@RequestMapping(value = "/pc/machining/order")
//@Api(tags = "品创预处理加工工单")
//public class PcMachiningOrderController {
//
//    @Resource
//    private PcMachiningOrderService machiningOrderService;
//
//
//    @ApiOperation(value = "分页查询预处理加工工单")
//    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
//    public Page<PcMachiningOrderPageDTO> getPcMachiningOrderPageList(@RequestBody PcMachiningOrderPageQueryDTO machiningOrderPageQueryDTO) {
//        return machiningOrderService.getPcMachiningOrderPageList(machiningOrderPageQueryDTO);
//    }
//
//    @ApiOperation(value = "新增预处理加工工单")
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    public Integer addPcMachiningOrder(@Valid @RequestBody PcMachiningOrderDTO machiningOrderDTO) {
//        return machiningOrderService.addPcMachiningOrder(machiningOrderDTO);
//    }
//
//    @ApiOperation(value = "修改预处理加工工单")
//    @RequestMapping(value = "/update", method = RequestMethod.PUT)
//    public Integer updatePcMachiningOrder(@Valid @RequestBody PcMachiningOrderDTO machiningOrderDTO) {
//        return machiningOrderService.updatePcMachiningOrder(machiningOrderDTO);
//    }
//
//    @ApiOperation(value = "删除预处理加工工单")
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    public Integer deletePcMachiningOrder(@PathVariable Integer id) {
//        return machiningOrderService.deletePcMachiningOrder(id);
//    }
//
//}
