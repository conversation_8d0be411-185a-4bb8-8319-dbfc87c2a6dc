//package com.hvisions.purchase.production.service.impl;
//
//
//import com.hvisions.purchase.dto.production.bran.issue.plan.BranIssuePlanPageDTO;
//import com.hvisions.purchase.dto.production.bran.issue.plan.BranIssuePlanPageQueryDTO;
//import com.hvisions.purchase.production.dao.BranIssuePlanMapper;
//import com.hvisions.purchase.production.service.BranIssuePlanService;
//import com.hvisions.common.utils.PageHelperUtil;
//import org.springframework.data.domain.Page;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @description:高粱发放计划
// * @date 2022/4/26 10:18
// */
//@Service
//public class BranIssuePlanServiceImpl implements BranIssuePlanService {
//
//    @Resource
//    private BranIssuePlanMapper branIssuePlanMapper;
//
//
//    @Override
//    public Page<BranIssuePlanPageDTO> getBranIssuePlanPageList(BranIssuePlanPageQueryDTO queryDTO) {
//        return PageHelperUtil.getPage(branIssuePlanMapper::getBranIssuePlanPageList, queryDTO, BranIssuePlanPageDTO.class);
//    }
//}
