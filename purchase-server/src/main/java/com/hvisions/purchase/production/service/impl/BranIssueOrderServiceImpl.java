//package com.hvisions.purchase.production.service.impl;
//
//
//import cn.hutool.core.date.DatePattern;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.hvisions.powder.client.SapClient;
//import com.hvisions.powder.dto.qudou.SapPostVO;
//import com.hvisions.purchase.dto.production.bran.balance.BranBalanceValueDTO;
//import com.hvisions.purchase.dto.production.bran.issue.order.BranIssueOrderPageDTO;
//import com.hvisions.purchase.dto.production.bran.issue.order.BranIssueOrderPageQueryDTO;
//import com.hvisions.purchase.dto.production.loss.rice.RiceLossDTO;
//import com.hvisions.purchase.dto.production.sorghum.DetailInsertDTO;
//import com.hvisions.purchase.dto.production.sorghum.SorghumAndBranBatchPostDTO;
//import com.hvisions.purchase.dto.production.sorghum.SorghumAndBranPostDetailDTO;
//import com.hvisions.purchase.dto.storage.warehouse.WarehousePageDTO;
//import com.hvisions.purchase.production.dao.BiOrderDetailMapper;
//import com.hvisions.purchase.production.dao.BranIssueOrderMapper;
//import com.hvisions.purchase.production.entity.TMpdBiOrderDetail;
//import com.hvisions.purchase.production.entity.TMpdBranIssueOrder;
//import com.hvisions.purchase.production.entity.TMpdSdOrderDetail;
//import com.hvisions.purchase.production.service.BranBalanceService;
//import com.hvisions.purchase.production.service.BranIssueOrderService;
//import com.hvisions.purchase.production.service.RiceLossService;
//import com.hvisions.purchase.purchase.consts.SapConst;
//import com.hvisions.purchase.sap.dto.SapBaseResponseDto;
//import com.hvisions.purchase.sap.dto.inventory.InventoryAllocationDto;
//import com.hvisions.purchase.sap.dto.purchase.OrderWriteOffHeaderDto;
//import com.hvisions.purchase.sap.service.SapService;
//import com.hvisions.purchase.storage.dao.WarehouseDataMapper;
//import com.hvisions.purchase.storage.entity.TMpdWarehouseData;
//import com.hvisions.purchase.storage.service.RlManagementService;
//import com.hvisions.purchase.storage.service.WarehouseService;
//import com.hvisions.purchase.utils.DateUtil;
//import com.hvisions.purchase.utils.GenerateCodeUtil;
//import com.hvisions.purchase.utils.StringUtil;
//import com.hvisions.common.exception.BaseKnownException;
//import com.hvisions.common.utils.DtoMapper;
//import com.hvisions.common.utils.PageHelperUtil;
//import lombok.extern.slf4j.Slf4j;
//import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.data.domain.Page;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description:熟糠发放工单
// * @date 2022/4/26 10:18
// */
//@Slf4j
//@Service
//public class BranIssueOrderServiceImpl implements BranIssueOrderService {
//
//    @Resource
//    private BranIssueOrderMapper branIssueOrderMapper;
//
//    @Resource
//    private RlManagementService rlManagementService;
//
//    @Resource
//    private BiOrderDetailMapper orderDetailMapper;
//
//    @Resource
//    private RiceLossService riceLossService;
//
//    @Resource
//    private BiOrderDetailMapper biOrderDetailMapper;
//
//    @Resource
//    private SapService sapService;
//
//    @Resource
//    private WarehouseService warehouseService;
//
//    @Resource
//    private GenerateCodeUtil generateCodeUtil;
//
//    @Resource
//    private BranBalanceService branBalanceService;
//
//    @Resource
//    private WarehouseDataMapper warehouseDataMapper;
//
//    @Resource
//    private SapClient sapClient;
//
//    @Override
//    public Page<BranIssueOrderPageDTO> getBranIssueOrderPageList(BranIssueOrderPageQueryDTO queryDTO) {
//        Page<BranIssueOrderPageDTO> dtos = PageHelperUtil.getPage(branIssueOrderMapper::getBranIssueOrderPageList, queryDTO, BranIssueOrderPageDTO.class);
//        for (BranIssueOrderPageDTO dto : dtos.getContent()) {
//            dto.setPostQuantity(branIssueOrderMapper.selectPostQuantityByOrderId(dto.getId()));
//        }
//        return dtos;
//    }
//
//    /**
//     * @Description 批量新增熟糠发放详情
//     *
//     * <AUTHOR>
//     * @Date 2023-11-23 13:53
//     * @param detailInsertDTOS
//     * @return java.lang.Integer
//     **/
//    @Override
//    @Transactional
//    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
//        int res = 0;
//        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
//            // 获取熟糠发放工单
//            TMpdBranIssueOrder order = branIssueOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
//            BigDecimal count = new BigDecimal(0);
//
//            // 获取指定日期中心得平衡值
//            BranBalanceValueDTO centerDateBalance = branBalanceService.getCenterDateBalance(order.getOrderDate(), order.getCenterId());
//
//            List<TMpdBiOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdBiOrderDetail.class);
//            // 遍历工单详情列表
//            for (TMpdBiOrderDetail orderDetail : orderDetails) {
//
//                if (StringUtil.isNotEmpty(centerDateBalance) && StringUtil.isNotEmpty(centerDateBalance.getBalanceValue())) {
//                    // 平衡值 = 实际数量*平衡比例
//                    orderDetail.setBalanceQuantity(orderDetail.getActualQuantity().multiply(centerDateBalance.getBalanceValue()));
//                }
//                orderDetail.setCreateTime(new Date());
//                orderDetail.setActualBeginTime(new Date());
//                orderDetail.setIsManual("1");
//
//                // 执行出入仓操作，并返回批次
//                String batch = rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
//                orderDetail.setBatch(batch);
//
//                // 累计实际数量
//                count = count.add(orderDetail.getActualQuantity());
//
//                // 新增损耗记录
//                RiceLossDTO riceLossDTO = DtoMapper.convert(orderDetail, RiceLossDTO.class);
//                riceLossDTO.setCenterId(order.getCenterId());
//                riceLossDTO.setCenter(order.getCenter());
//                riceLossDTO.setIssueQuantity(orderDetail.getActualQuantity());
//                riceLossDTO.setActualBeginTime(orderDetail.getPostingTime());
//                res += riceLossService.insertOrUpdateRiceLoss(riceLossDTO);
//
//                res += orderDetailMapper.insert(orderDetail);
//
//            }
//
//            // 更新工单实际数量
//            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
//            res += branIssueOrderMapper.updateById(order);
//        }
//        return res;
//    }
//
//
//    /***
//     * @Description 熟糠发放冲销
//     *
//     * <AUTHOR>
//     * @Date 2022-9-5 18:55
//     * @param matDocs
//     * @return java.lang.Integer
//     **/
//    @Override
//    @Transactional
//    public Integer writeOff(List<String> matDocs) {
//        int res = 0;
//        for (String matDoc : matDocs) {
//            // 获取已经过账的且过账凭证号为选择的
//            List<TMpdBiOrderDetail> biOrderDetails = biOrderDetailMapper.selectList(Wrappers.<TMpdBiOrderDetail>lambdaQuery()
//                    .eq(TMpdBiOrderDetail::getMatDoc, matDoc)
//                    .eq(TMpdBiOrderDetail::getDeleted, 0)
//                    .eq(TMpdBiOrderDetail::getSapState, "1")
//            );
//            // sap冲销
//            OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
//            headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
//            headerDto.setPstingDate(cn.hutool.core.date.DateUtil.format(biOrderDetails.get(0).getPostingTime(), DatePattern.PURE_DATETIME_PATTERN));
//            headerDto.setMjahr(biOrderDetails.get(0).getDocYear());
//            headerDto.setMblnr(matDoc);
//            SapBaseResponseDto response = sapService.writeOff(headerDto);
//            BigDecimal writeOffWeight = BigDecimal.ZERO;
//
//            if (!"S".equals(response.getEsMessage().getMsgty())) {
//                // 冲销失败
//                throw new BaseKnownException(10000, "sap冲销失败" + response.getEsMessage().getMsgtx());
//            } else if ("S".equals(response.getEsMessage().getMsgty())) {
//                // 冲销成功，将工单详情制空
//                for (TMpdBiOrderDetail orderDetail : biOrderDetails) {
//                    orderDetail.setSapState("0");
//                    orderDetail.setMatDoc("");
//                    orderDetail.setIsWriteOff("1");
//                    writeOffWeight = writeOffWeight.add(orderDetail.getActualQuantity());
//
//                    res += biOrderDetailMapper.updateById(orderDetail);
//                }
//                // 新增sap操作记录
//                SapPostVO sapPostVO = new SapPostVO();
//                sapPostVO.setMaterialCode(biOrderDetails.get(0).getMaterialCode());
//                sapPostVO.setMaterialName(biOrderDetails.get(0).getMaterialName());
//                sapPostVO.setUnit(biOrderDetails.get(0).getUnit());
//                sapPostVO.setWeight(writeOffWeight.negate());
//                sapPostVO.setType(0);
//                sapPostVO.setMovementTypeId(311);
//                sapPostVO.setOperatorType("熟糠发放冲销");
//                sapPostVO.setCertificateDate(LocalDate.now());
//                sapPostVO.setValueJson(JSONObject.toJSONString(headerDto));
//                sapPostVO.setSapCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO);
//                sapPostVO.setState("1");
//                sapClient.insertPurchaseSapPostRecord(sapPostVO);
//            }
//        }
//        return res;
//    }
//
//    /***
//     * @Description 熟糠发放详情，库存回退
//     *
//     * <AUTHOR>
//     * @Date 2023-1-10 9:56
//     * @param biOrderDetail
//     * @return Integer
//     **/
//    private Integer backStock(TMpdBiOrderDetail biOrderDetail) {
//
//        /**
//         * 库存回退
//         *      1、根据发出筒仓和批次获取库存记录（删除的 || 没有删除的）
//         *      2、将库存加回来
//         */
//
//        int res = 0;
//        TMpdWarehouseData warehouseData = warehouseDataMapper.getWarehouseDataDetailByStorageIdAndBatch(biOrderDetail.getSendSiloId(), biOrderDetail.getBatch().split(",")[0]);
//        if (warehouseData.getDeleted()) { // 批次全部删除了
//            res += warehouseDataMapper.updateDeletedStock(warehouseData.getId(), biOrderDetail.getActualQuantity());
//        } else { // 批次存在
//            warehouseData.setStockQuantity(warehouseData.getStockQuantity().add(biOrderDetail.getActualQuantity()));
//            res += warehouseDataMapper.updateById(warehouseData);
//        }
//        return res;
//    }
//
//    /**
//     * @Description 熟糠发放批量过账
//     *
//     * <AUTHOR>
//     * @Date 2023-11-23 14:02
//     * @param postDTO
//     * @return java.lang.Integer
//     **/
//    @Override
//    public Integer posting(SorghumAndBranBatchPostDTO postDTO) {
//        int res = 0;
//        if (StringUtil.isEmpty(postDTO.getSorghumAndBranPostDetailDTOList()) && postDTO.getSorghumAndBranPostDetailDTOList().size() == 0) {
//            throw new BaseKnownException(10000, "无过账记录！");
//        }
//
//        Date postDate = getPostDate(postDTO.getPostingDate());
//        boolean isPost = sapService.judgeSapPostTime(postDate);
//        //如果日期在当月24号之后则返回为false, 不进行同步并将状态改为同步中,同步日期更改为次月1号, 由定时任务同步
//        if (!isPost) {
//            // 遍历工单详情，修改工单详情
//            for (SorghumAndBranPostDetailDTO sorghumAndBranPostDetailDTO : postDTO.getSorghumAndBranPostDetailDTOList()) {
//                TMpdBiOrderDetail orderDetail = orderDetailMapper.selectById(sorghumAndBranPostDetailDTO.getId());
//                orderDetail.setPostingTime(postDate);
//                orderDetail.setSapState("2");
//                res += biOrderDetailMapper.updateById(orderDetail);
//            }
//            return res;
//        }
//
//        // 获取首个工单详情
//        TMpdBiOrderDetail firstOrderDetail = biOrderDetailMapper.selectById(postDTO.getSorghumAndBranPostDetailDTOList().get(0).getId());
//        // 获取工单
//        TMpdBranIssueOrder order = branIssueOrderMapper.selectById(firstOrderDetail.getOrderId());
//
//        // 同步到sap
//        WarehousePageDTO warehouse = warehouseService.getWarehouseByDepartmentCode(order.getCenter());
//        InventoryAllocationDto inventoryAllocationDto = new InventoryAllocationDto();
//        inventoryAllocationDto.setOrderNo(generateCodeUtil.generatePlanCode(SapConst.INVENTORY_ALLOCATION_NO));
//        inventoryAllocationDto.setMaterial(postDTO.getMaterialCode());
//        inventoryAllocationDto.setSlogrt("1106");
//        inventoryAllocationDto.setDlogrt(warehouse.getErpCode());
//        inventoryAllocationDto.setEntryQnt(postDTO.getPostingQuantity());
//        inventoryAllocationDto.setCertificateDate(postDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
//        SapBaseResponseDto responseDto = sapService.inventoryAllocation(inventoryAllocationDto);
//        if (!"S".equals(responseDto.getEsMessage().getMsgty())) {
//            // 过账失败
//            throw new BaseKnownException(10000, "熟糠发放SAP过账失败，原因：" + responseDto.getEsMessage().getMsgtx());
//        } else if ("S".equals(responseDto.getEsMessage().getMsgty())) {
//            // 过账成功
//            // 遍历工单子项，修改工单子项
//            List<TMpdBiOrderDetail> updateList = new ArrayList<>();
//            for (SorghumAndBranPostDetailDTO sorghumAndBranPostDetailDTO : postDTO.getSorghumAndBranPostDetailDTOList()) {
//                TMpdBiOrderDetail orderDetail = orderDetailMapper.selectById(sorghumAndBranPostDetailDTO.getId());
//                orderDetail.setMatDoc(responseDto.getEvOutput().getOutput().get(0).getMblnr());
//                orderDetail.setDocYear(DateUtil.yearFormat(postDate));
//                orderDetail.setActualQuantity(sorghumAndBranPostDetailDTO.getPostingQuantity());
//                orderDetail.setMaterialId(postDTO.getMaterialId());
//                orderDetail.setMaterialCode(postDTO.getMaterialCode());
//                orderDetail.setMaterialName(postDTO.getMaterialName());
//                orderDetail.setSapState("1");
//                orderDetail.setPostingTime(postDate);
//                //res += biOrderDetailMapper.updateById(orderDetail);
//                updateList.add(orderDetail);
//            }
//            if (CollectionUtils.isNotEmpty(updateList)) {
//                res += biOrderDetailMapper.updatePostByBatch(updateList);
//            }
//            // 生成sap过账记录
//            SapPostVO sapPostVO = new SapPostVO();
//            sapPostVO.setMaterialCode(postDTO.getMaterialCode());
//            sapPostVO.setMaterialName(postDTO.getMaterialName());
//            sapPostVO.setWeight(postDTO.getPostingQuantity());
//            sapPostVO.setUnit(firstOrderDetail.getUnit());
//            sapPostVO.setMovementTypeId(311);
//            sapPostVO.setType(0);
//            sapPostVO.setInitialWarehouseCode("1106");
//            sapPostVO.setInitialWarehouseName("黄舣粮库");
//            sapPostVO.setTargetWarehouseCode(warehouse.getErpCode());
//            sapPostVO.setTargetWarehouseName(warehouse.getName());
//            sapPostVO.setOperatorType("熟糠发放手动过账");
//            sapPostVO.setSapCode(SapConst.INVENTORY_ALLOCATION_NO);
//            sapPostVO.setOperatingTime(LocalDateTime.now());
//            sapPostVO.setCertificateDate(postDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
//            sapPostVO.setValueJson(JSONObject.toJSONString(inventoryAllocationDto));
//            sapPostVO.setOrderDate(order.getOrderDate());
//            sapPostVO.setState("1");
//            sapPostVO.setCertificateNumber(responseDto.getEvOutput().getOutput().get(0).getMblnr());
//            sapPostVO.setCertificateYear(responseDto.getEvOutput().getOutput().get(0).getMjahr());
//            sapClient.insertPurchaseSapPostRecord(sapPostVO);
//        }
//        return res;
//    }
//
//    /**
//     * 获取过账日期
//     * @param from
//     * @return
//     */
//    private Date getPostDate(Date from) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(from);
//        int postDay = calendar.get(Calendar.DAY_OF_MONTH); // 当前过账时间日期
//        // 如果过账日期 >= 24, 则将过账日期改成次月1号
//        if (postDay >= 24){
//            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            Date postTime = calendar.getTime();
//            log.info("过账日期 >= 24,更改过账日期为次月1号:" + DateUtil.dateFormat(postTime));
//            return postTime;
//        }
//        return from;
//    }
//
//    /***
//     * @Description 每月1号12点调用，将上月未过账的数据的发放数据同步给sap
//     *
//     * <AUTHOR>
//     * @Date 2022-12-15 14:32
//     * @param
//     * @return void
//     **/
//    @Scheduled(cron = "0 0 12 1 * ?")
//    @SchedulerLock(name = "scheduledBranSyncSapLock")
//    public void scheduledBranSyncSap() {
//        log.info("熟糠发放1号定时同步sap开始");
//
//        Date yearTime = sapService.isYearPost();
//
//        List<TMpdBiOrderDetail> biOrderDetails;
//
//        if (yearTime != null) {
//            biOrderDetails = biOrderDetailMapper.selectList(Wrappers.<TMpdBiOrderDetail>lambdaQuery()
//                    .eq(TMpdBiOrderDetail::getSapState, "2")
//                    .between(TMpdBiOrderDetail::getPostingTime, yearTime, DateUtil.getAppointDay(new Date(), 1))
//            );
//        } else {
//            biOrderDetails = biOrderDetailMapper.selectList(Wrappers.<TMpdBiOrderDetail>lambdaQuery()
//                    .eq(TMpdBiOrderDetail::getSapState, "2")
//                    .between(TMpdBiOrderDetail::getPostingTime, DateUtil.getUpMonthAppointDay(24), DateUtil.getAppointDay(new Date(), 1))
//
//            );
//        }
//        log.info("熟糠发放1号定时同步sap数据为==>{}", biOrderDetails.toString());
//        int res = 0;
//
//        if (biOrderDetails.size() > 0) {
//            for (TMpdBiOrderDetail orderDetail : biOrderDetails) {
//                TMpdBranIssueOrder order = branIssueOrderMapper.selectById(orderDetail.getOrderId());
//                // 同步到sap
//                WarehousePageDTO warehouse = warehouseService.getWarehouseByDepartmentCode(order.getCenter());
//                if (StringUtil.isEmpty(warehouse)) {
//                    log.info("该中心不存在sap仓库，无法过账" + order.getCenter());
//                    continue;
//                }
//                InventoryAllocationDto inventoryAllocationDto = new InventoryAllocationDto();
//                inventoryAllocationDto.setOrderNo(generateCodeUtil.generatePlanCode(SapConst.INVENTORY_ALLOCATION_NO));
//                inventoryAllocationDto.setMaterial(orderDetail.getMaterialCode());
//                inventoryAllocationDto.setSlogrt("1106");
//                inventoryAllocationDto.setDlogrt(warehouse.getErpCode());
//                inventoryAllocationDto.setEntryQnt(orderDetail.getActualQuantity());
//                inventoryAllocationDto.setCertificateDate(orderDetail.getPostingTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
//                SapBaseResponseDto responseDto = sapService.inventoryAllocation(inventoryAllocationDto);
//
//                // sap操作记录
//                SapPostVO sapPostVO = new SapPostVO();
//                sapPostVO.setMaterialCode(orderDetail.getMaterialCode());
//                sapPostVO.setMaterialName(orderDetail.getMaterialName());
//                sapPostVO.setUnit(orderDetail.getUnit());
//                sapPostVO.setWeight(orderDetail.getActualQuantity());
//                sapPostVO.setMovementTypeId(311);
//                sapPostVO.setType(0);
//                sapPostVO.setInitialWarehouseCode("1106");
//                sapPostVO.setInitialWarehouseName("黄舣粮库");
//                sapPostVO.setTargetWarehouseCode(warehouse.getErpCode());
//                sapPostVO.setTargetWarehouseName(warehouse.getName());
//                sapPostVO.setOperatorType("熟糠发放同步");
//                sapPostVO.setCertificateDate(LocalDate.now());
//                sapPostVO.setSapCode(SapConst.INVENTORY_ALLOCATION_NO);
//                sapPostVO.setValueJson(JSONObject.toJSONString(inventoryAllocationDto));
//                sapPostVO.setOrderDate(order.getOrderDate());
//                if ("S".equals(responseDto.getEsMessage().getMsgty())) {
//                    sapPostVO.setState("1");
//                    sapPostVO.setCertificateNumber(responseDto.getEvOutput().getOutput().get(0).getMblnr());
//                    sapPostVO.setCertificateYear(responseDto.getEvOutput().getOutput().get(0).getMjahr());
//                    orderDetail.setMatDoc(responseDto.getEvOutput().getOutput().get(0).getMblnr());
//                    orderDetail.setDocYear(DateUtil.yearFormat(orderDetail.getPostingTime()));
//                    orderDetail.setSapState("1");
//                } else {
//                    sapService.sendPostFailNotice("熟糠发放", order.getOrderNo(), responseDto.getEsMessage().getMsgtx());
//                    sapPostVO.setState("2");
//                    sapPostVO.setFailReason(responseDto.getEsMessage().getMsgtx());
//                }
//                sapClient.insertPurchaseSapPostRecord(sapPostVO);
//
//                res += biOrderDetailMapper.updateById(orderDetail);
//            }
//        }
//        log.info("熟糠发放1号定时同步sap数据完成==》" + res);
//
//    }
//
//
//}
