//package com.hvisions.purchase.production.service;
//
//
//import com.hvisions.purchase.dto.production.sorghum.shipment.plan.SorghumShipmentPlanPageDTO;
//import com.hvisions.purchase.dto.production.sorghum.shipment.plan.SorghumShipmentPlanPageQueryDTO;
//import org.springframework.data.domain.Page;
//
///**
// * <AUTHOR>
// * @description:高粱转粮计划
// * @date 2022/4/22 10:18
// */
//public interface SorghumShipmentPlanService {
//
//    Page<SorghumShipmentPlanPageDTO> getSorghumShipmentPlanPageList(SorghumShipmentPlanPageQueryDTO queryDTO);
//
//
//}
