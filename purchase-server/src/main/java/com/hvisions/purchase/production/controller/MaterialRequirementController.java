//package com.hvisions.purchase.production.controller;
//
//import com.hvisions.purchase.dto.production.material.requirement.MaterialRequirementDTO;
//import com.hvisions.purchase.dto.production.material.requirement.MaterialRequirementPageDTO;
//import com.hvisions.purchase.dto.production.material.requirement.MaterialRequirementPageQueryDTO;
//import com.hvisions.purchase.production.service.MaterialRequirementService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.data.domain.Page;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @description:酿酒生产物料需求
// * @date 2022/4/22 10:18
// */
//@RestController
//@RequestMapping(value = "/material/requirement")
//@Api(tags = "酿酒生产物料需求")
//public class MaterialRequirementController {
//
//    @Resource
//    private MaterialRequirementService materialRequirementService;
//
//
//    @ApiOperation(value = "分页查询酿酒生产物料需求")
//    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
//    public Page<MaterialRequirementPageDTO> getMaterialRequirementPageList(@RequestBody MaterialRequirementPageQueryDTO queryDTO) {
//        return materialRequirementService.getMaterialRequirementPageList(queryDTO);
//    }
//
//
//    @ApiOperation(value = "新增酿酒生产物料需求")
//    @RequestMapping(value = "/insert", method = RequestMethod.POST)
//    public Integer insertMaterialRequirement(@RequestBody MaterialRequirementDTO materialRequirementDTO) {
//        return materialRequirementService.insertMaterialRequirement(materialRequirementDTO);
//    }
//
//
//}
