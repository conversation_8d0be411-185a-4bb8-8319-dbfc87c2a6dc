//package com.hvisions.purchase.production.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.hvisions.purchase.dto.production.bran.inspection.*;
//import com.hvisions.purchase.production.entity.TMpdBranInspection;
//import org.springframework.data.domain.Page;
//
///**
// * <p>
// * 蒸糠质量巡检 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-05-12
// */
//public interface BranInspectionService extends IService<TMpdBranInspection> {
//
//    Page<BranInspectionPageDTO> getBranInspectionPageList(BranInspectionPageQueryDTO pageQueryDTO);
//
//    BranInspectionPageDTO getBranInspectionByOrderNo(String orderNo);
//
//    Boolean addBranInspection(BranInspectionDTO branInspectionDTO);
//
//    Boolean updateBranInspection(BranInspectionDTO branInspectionDTO);
//
//    Boolean reportBranInspection(Integer id);
//
//    Boolean deleteBranInspection(Integer id);
//
//    BranInspectionDetailDTO getBranInspectionDetail(Integer id);
//
//    Boolean workReport(WorkReportDTO workReportDTO);
//
//}
