//package com.hvisions.purchase.production.service;
//
//
//import com.hvisions.purchase.dto.production.sorghum.transfer.plan.SorghumTransferPlanPageDTO;
//import com.hvisions.purchase.dto.production.sorghum.transfer.plan.SorghumTransferPlanPageQueryDTO;
//import org.springframework.data.domain.Page;
//
///**
// * <AUTHOR>
// * @description:高粱转运计划
// * @date 2022/4/22 10:18
// */
//public interface SorghumTransferPlanService {
//
//    Page<SorghumTransferPlanPageDTO> getSorghumTransferPlanPageList(SorghumTransferPlanPageQueryDTO queryDTO);
//
//
//}
