//package com.hvisions.purchase.production.service.impl;
//
//
//import com.hvisions.common.utils.DtoMapper;
//import com.hvisions.common.utils.PageHelperUtil;
//import com.hvisions.purchase.dto.production.machining.plan.MachiningPlanDTO;
//import com.hvisions.purchase.dto.production.machining.plan.MachiningPlanPageDTO;
//import com.hvisions.purchase.dto.production.machining.plan.MachiningPlanPageQueryDTO;
//import com.hvisions.purchase.production.dao.MachiningPlanMapper;
//import com.hvisions.purchase.production.entity.TMpdMachiningPlan;
//import com.hvisions.purchase.production.service.MachiningPlanService;
//import com.hvisions.purchase.utils.DateUtil;
//import com.hvisions.purchase.utils.SerialNumberUtil;
//import org.springframework.data.domain.Page;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.Date;
//
///**
// * <AUTHOR>
// * @description:预处理加工计划
// * @date 2022/4/22 10:18
// */
//@Service
//public class MachiningPlanServiceImpl implements MachiningPlanService {
//
//    @Resource
//    private MachiningPlanMapper machiningPlanMapper;
//
//    @Resource
//    private SerialNumberUtil serialNumberUtil;
//
//    @Override
//    public Page<MachiningPlanPageDTO> getMachiningPlanPageList(MachiningPlanPageQueryDTO machiningPlanPageQueryDTO) {
//        return PageHelperUtil.getPage(machiningPlanMapper::getMachiningPlanPageList, machiningPlanPageQueryDTO, MachiningPlanPageDTO.class);
//    }
//
//    @Override
//    public Integer addMachiningPlan(MachiningPlanDTO machiningPlanDTO) {
//        TMpdMachiningPlan machiningPlan = DtoMapper.convert(machiningPlanDTO, TMpdMachiningPlan.class);
//        String serialNumber = serialNumberUtil.getSerialNumber("t_mpd_machining_plan", 3, true);
//        machiningPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
//        machiningPlan.setCreateTime(new Date());
//        machiningPlan.setUpdateTime(new Date());
//        return machiningPlanMapper.insert(machiningPlan);
//    }
//
//    @Override
//    public Integer updateMachiningPlan(MachiningPlanDTO machiningPlanDTO) {
//        TMpdMachiningPlan machiningPlan = DtoMapper.convert(machiningPlanDTO, TMpdMachiningPlan.class);
//        machiningPlan.setUpdateTime(new Date());
//        machiningPlan.setCreatorId(null);
//        return machiningPlanMapper.updateById(machiningPlan);
//    }
//
//    @Override
//    public Integer deleteMachiningPlan(Integer id) {
//        return machiningPlanMapper.deleteById(id);
//    }
//
//}
