package com.hvisions.purchase.purchase.dao;

import com.hvisions.purchase.dto.purchase.demand.order.VendorManagePageDTO;
import com.hvisions.purchase.dto.purchase.demand.order.VendorManageQueryDTO;
import com.hvisions.purchase.purchase.entity.TMpVendorManage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 供应商场内管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface TMpVendorManageMapper extends BaseMapper<TMpVendorManage> {

    List<VendorManagePageDTO> getVendorManagePage(VendorManageQueryDTO vendorManageQueryDTO);
}
