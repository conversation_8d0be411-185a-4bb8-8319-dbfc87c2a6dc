package com.hvisions.purchase.purchase.service.impl;

import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.brewage.common.utils.SnowflakeIdWorker;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.purchase.advice.UserAuditorAware;
import com.hvisions.purchase.dto.purchase.demand.order.DeliveryInspectPageDTO;
import com.hvisions.purchase.dto.purchase.demand.order.VendorManagePageDTO;
import com.hvisions.purchase.dto.purchase.demand.order.VendorManageQueryDTO;
import com.hvisions.purchase.purchase.dao.TMpVendorManageMapper;
import com.hvisions.purchase.purchase.entity.TMpVendorManage;
import com.hvisions.purchase.purchase.service.TMpVendorManageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.Date;

import static com.hvisions.powder.consts.CommonConsts.LOGIN_HINT;

/**
 * <p>
 * 供应商场内管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class TMpVendorManageServiceImpl extends ServiceImpl<TMpVendorManageMapper, TMpVendorManage> implements TMpVendorManageService {

    @Autowired
    UserAuditorAware userAuditorAware;

    @Override
    public  IPage<TMpVendorManage> findListByPage(Integer page, Integer pageCount){
        IPage<TMpVendorManage> wherePage = new Page<>(page, pageCount);
        TMpVendorManage where = new TMpVendorManage();

        return   baseMapper.selectPage(wherePage, Wrappers.query(where));
    }

    @Override
    public int add(TMpVendorManage tMpVendorManage){
        tMpVendorManage.setSubmitTime(new Date());
        //单据号
        tMpVendorManage.setOrderNo("" +SnowflakeIdWorker.getNextId());
        //提交人
        UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
        tMpVendorManage.setSubmitUser(userBaseDTO.getUserName());
        tMpVendorManage.setState("1");
        int insert = baseMapper.insert(tMpVendorManage);
        //下发srm
        return insert;
    }

    @Override
    public int delete(Long id){
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateData(TMpVendorManage tMpVendorManage){
        TMpVendorManage query = baseMapper.selectById(tMpVendorManage.getId());
        if ("2".equals(query.getState())) {
            throw new BaseKnownException(10011, "已处理的数据不允许修改");
        }
        //下发数据到srm
        return baseMapper.updateById(tMpVendorManage);
    }

    @Override
    public TMpVendorManage findById(Long id){
        return  baseMapper.selectById(id);
    }

    @Override
    public org.springframework.data.domain.Page<VendorManagePageDTO> getVendorManagePage(VendorManageQueryDTO queryDto) {
        return PageHelperUtil.getPage(baseMapper::getVendorManagePage, queryDto, VendorManagePageDTO.class);
    }
}
