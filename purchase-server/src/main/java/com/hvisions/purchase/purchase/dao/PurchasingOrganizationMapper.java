package com.hvisions.purchase.purchase.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.purchase.dto.purchase.purchasing.organization.PurchasingOrganizationPageDTO;
import com.hvisions.purchase.dto.purchase.purchasing.organization.PurchasingOrganizationPageQueryDTO;
import com.hvisions.purchase.purchase.entity.TMpPurchasingOrganization;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 采购组织
 * @author: Jcao
 * @time: 2022/4/2 10:37
 */
@Mapper
public interface PurchasingOrganizationMapper extends BaseMapper<TMpPurchasingOrganization> {

    /*
     * @Description: 分页查询采购组织
     * <AUTHOR>
     */
    List<PurchasingOrganizationPageDTO> getPurchasingOrganizationPageList(PurchasingOrganizationPageQueryDTO purchasingOrganizationPageQueryDTO);

}
