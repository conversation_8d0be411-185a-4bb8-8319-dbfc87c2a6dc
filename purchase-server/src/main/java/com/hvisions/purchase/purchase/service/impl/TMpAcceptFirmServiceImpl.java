package com.hvisions.purchase.purchase.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.purchase.purchase.dao.TMpAcceptFirmMapper;
import com.hvisions.purchase.purchase.entity.TMpAcceptFirm;
import com.hvisions.purchase.purchase.service.TMpAcceptFirmService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class TMpAcceptFirmServiceImpl extends ServiceImpl<TMpAcceptFirmMapper, TMpAcceptFirm> implements TMpAcceptFirmService {

    @Override
    public  IPage<TMpAcceptFirm> findListByPage(Integer page, Integer pageCount){
        IPage<TMpAcceptFirm> wherePage = new Page<>(page, pageCount);
        TMpAcceptFirm where = new TMpAcceptFirm();

        return   baseMapper.selectPage(wherePage, Wrappers.query(where));
    }

    @Override
    public int add(TMpAcceptFirm tMpAcceptFirm){
        return baseMapper.insert(tMpAcceptFirm);
    }

    @Override
    public int delete(Long id){
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateData(TMpAcceptFirm tMpAcceptFirm){
        return baseMapper.updateById(tMpAcceptFirm);
    }

    @Override
    public TMpAcceptFirm findById(Long id){
        return  baseMapper.selectById(id);
    }
}
