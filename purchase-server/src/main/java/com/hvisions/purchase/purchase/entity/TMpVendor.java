package com.hvisions.purchase.purchase.entity;

import com.hvisions.purchase.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @description 供应商
 * @date 2022/4/6 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_vendor")
public class TMpVendor extends SysBase {

    /**
     * 供应商编码
     */
    private String code;
    /**
     * 供应商名称
     */
    private String name;
    /**
     * 采购组织id
     */
    private Integer organizationId;
    /**
     * 供应商组
     */
    private String vendorGroup;
}
