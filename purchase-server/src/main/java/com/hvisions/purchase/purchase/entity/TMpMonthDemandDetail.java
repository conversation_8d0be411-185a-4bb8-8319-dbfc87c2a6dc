package com.hvisions.purchase.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.purchase.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_month_demand_detail")
@ApiModel(value="TMpMonthDemandDetail对象", description="")
public class TMpMonthDemandDetail extends SysBase {

    @ApiModelProperty(value = "月需求单id")
    private Integer mouthDemandId;

    @ApiModelProperty(value = "采购订单id")
    private Integer purchaseOrderId;

    @ApiModelProperty(value = "采购订单编号")
    private String sapOrder;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "要货数量kg")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "品种")
    private String variety;

    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "供货分解单号(SRM)")
    private String supplyNo;
}
