package com.hvisions.purchase.purchase.entity;

import com.hvisions.purchase.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 品创日送货计划详情
 * @date 2022/4/6 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_pc_daily_delivery_detail")
public class TMpPcDailyDeliveryDetail extends SysBase {

    /**
     * 日送货计划id
     */
    private Integer deliveryId;
    /**
     * 物料id
     */
    private Integer materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 车牌号
     */
    private String licensePlateNumber;
    /**
     * 送货单号
     */
    private String deliveryNumber;
    /**
     * 预送数量
     */
    private BigDecimal estimatedNumber;
    /**
     * 入场时间
     */
    private Date admissionTime;
    /**
     * 毛重（入场重量）
     */
    private BigDecimal grossWeight;

    /**
     * 出场重量（皮重）
     */
    private BigDecimal appearanceWeight;

    /**
     * 离厂净重
     */
    private BigDecimal leaveNetWeight;
    /**
     * 扣重重量
     */
    private BigDecimal buckleWeight;
    /**
     * 杂质重量
     */
    private BigDecimal impurityWeight;
    /**
     * 最终净重
     */
    private BigDecimal netWeight;

    /**
     * 出场时间
     */
    private Date appearanceTime;

    /**
     * 批次
     */
    private String batch;
    /**
     * 异常数据id
     */
    private Integer exceptionId;
    /**
     * 异常描述
     */
    private String exceptionDescription;
    /**
     * 车辆状态：
     * 0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、 5-待处理、6-拒收、7-复检,8-特殊退货,9已收货
     */
    private String state;
    /**
     * 下发同步状态：
     * 0-待自动同步、1-已同步、2-同步失败
     */
    private String syncState;
    /**
     * 延时同步时使用：
     * 上传者的部门Id
     */
    private Integer departmentId;
    /**
     * 质检状态:0- 待检验、1-质检中、2-合格、3-不合格
     */
    private String inspectState;
    /**
     * 类型： 0-送货、1-杂质
     */
    private String type;
    /**
     * 过账状态 0-未过账、1-已过账
     */
    private String postingState;

    /**
     * 过账时间
     */
    private Date postingTime;
    /**
     * 过账人
     */
    private String postingPeople;

    /**
     * 备注
     */
    private String remark;
    /**
     * 物料凭证编号
     */
    private String matDoc;
    /**
     * 物料凭证年度
     */
    private String docYear;
    /**
     * 是否打印；0-否，1-是
     */
    private String print;

    /**
     * 扣重是否处理；0-否，1-是
     */
    private String buckleIsHandle;

    /**
     * 入库库位id
     */
    private Integer warehouseId;

    // 特殊退货备注
    private String returnRemark;

    /**
     *卸货状态 0 卸货中，1卸货完成
     */
    private String unloadState;

    //收货单位
    private String receiptPlace;

    /**
     * 制曲生产入仓任务id；
     */
    private Integer warehouseTaskId;
    // 麦号
    private String wheatNumber;


}
