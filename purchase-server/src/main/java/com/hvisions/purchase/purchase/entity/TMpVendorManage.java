package com.hvisions.purchase.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 供应商场内管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_vendor_manage")
@ApiModel(value="TMpVendorManage对象", description="供应商场内管理")
public class TMpVendorManage extends SysBase {

    @ApiModelProperty(value = "单据号")
    private String orderNo;

    @ApiModelProperty(value = "送货车辆id")
    private Integer deliveryDetailId;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "问题描述")
    private String questionDescription;

    @ApiModelProperty(value = "图片")
    private String picUrls;

    @ApiModelProperty(value = "提交用户")
    private String submitUser;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty(value = "状态 1-待处理：2-已处理；")
    private String state;

    @ApiModelProperty(value = "处理详情")
    private String disposeDescription;

    @ApiModelProperty(value = "处理图片")
    private String disposePicUrls;

    @ApiModelProperty(value = "处理用户")
    private String disposeUser;
}
