package com.hvisions.powder.exceptionType.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.dto.qudou.exceptionType.ExceptionTypeDTO;
import com.hvisions.powder.dto.qudou.exceptionType.ExceptionTypePageDTO;
import com.hvisions.powder.dto.qudou.exceptionType.ExceptionTypePageQueryDTO;
import com.hvisions.powder.exceptionType.entity.TWpExceptionType;
import com.hvisions.powder.exceptionType.service.ExceptionTypeService;
import com.hvisions.powder.exceptionType.mapper.ExceptionTypeMapper;
import com.hvisions.powder.utils.CopyUtil;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_wp_exception_type(曲粉异常类型)】的数据库操作Service实现
* @createDate 2024-02-28 16:40:17
*/
@Service
public class ExceptionTypeServiceImpl extends ServiceImpl<ExceptionTypeMapper, TWpExceptionType> implements ExceptionTypeService {

    @Override
    public Page getExceptionTypePageList(ExceptionTypePageQueryDTO exceptionTypePageQueryDTO) {
        return PageHelperUtil.getPage(baseMapper::getExceptionTypeList,exceptionTypePageQueryDTO, ExceptionTypePageDTO.class);
    }

    @Override
    public Boolean insertExceptionType(ExceptionTypeDTO exceptionTypeDTO) {
        TWpExceptionType tWpExceptionType = CopyUtil.simpleCopy(exceptionTypeDTO, TWpExceptionType.class);
        return baseMapper.insert(tWpExceptionType) > 0;
    }

    @Override
    public Boolean updateExceptionType(ExceptionTypeDTO exceptionTypeDTO) {
        TWpExceptionType tWpExceptionType = CopyUtil.simpleCopy(exceptionTypeDTO, TWpExceptionType.class);
        return baseMapper.updateById(tWpExceptionType) > 0;
    }

    @Override
    public Boolean deleteExceptionType(Integer id) {
        return baseMapper.deleteById(id) > 0;
    }
}




