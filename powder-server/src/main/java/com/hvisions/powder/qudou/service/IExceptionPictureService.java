package com.hvisions.powder.qudou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.powder.qudou.entity.ExceptionPicture;
import com.hvisions.powder.qudou.vo.UserMapperDepartment;

import java.util.List;

/**
 * <p>
 * 异常图片 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface IExceptionPictureService extends IService<ExceptionPicture> {
    void handleDetailImage(Integer detailId, List<Integer> imageIds);

    List<Integer> listPicByDetailId(Integer detailId);


}
