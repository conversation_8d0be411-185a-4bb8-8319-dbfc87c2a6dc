/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.powder.qudou.dto;

import com.hvisions.powder.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname SceneDataReq
 * @description 曲粉现场数据数据传输对象实体类
 * @date 2022-04-22
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("SceneData数据传输对象实体类")
public class SceneDataReq extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("中心id")
    private Integer centerId;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("车间id")
    private Integer locationId;

    @ApiModelProperty("车间名称")
    private String locationName;

    @ApiModelProperty("曲粉id")
    private Integer materialId;

    @ApiModelProperty("曲粉编码")
    private String materialCode;

    @ApiModelProperty("曲粉名称")
    private String materialName;

    @ApiModelProperty("当前数量KG")
    private BigDecimal currentWeight;

}
