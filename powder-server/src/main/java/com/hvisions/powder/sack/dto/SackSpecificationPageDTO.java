package com.hvisions.powder.sack.dto;

import com.hvisions.powder.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @描述: 袋装基础数据DTO
 * @作者: wy liu
 * @日期: 2023/12/26 10:15
 * @版本 1.0
 */
@Data
@NoArgsConstructor
@ApiModel("袋装配置查询DTO")
public class SackSpecificationPageDTO extends BaseDTO {

    @ApiModelProperty("袋装规格")
    private String specification;

    @ApiModelProperty("每袋公斤数")
    private Integer weight;

    @ApiModelProperty("基地id")
    private Integer locationId;

    @ApiModelProperty("基地")
    private String location;

    @ApiModelProperty("状态 1--生效  2--归档")
    private Integer state;

    @ApiModelProperty("创建人名称")
    private String creatorName;

}
