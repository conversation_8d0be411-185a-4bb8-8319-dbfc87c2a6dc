package com.hvisions.powder.vehicleTransport.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hvisions.powder.dto.VehicleTransportBatchDTO;
import com.hvisions.powder.dto.VehicleTransportDTO;
import com.hvisions.powder.dto.VehicleTransportQueryDTO;
import com.hvisions.powder.vehicleTransport.service.VehicleTransportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/BatchSearch")
@Api(description = "车辆运输管理配置")
@Slf4j
public class VehicleTransportController {

    @Resource
    VehicleTransportService vehicleTransportService;


    /**
     * 车辆运输管理配置新增修改
     * @param dto
     */
    @ApiOperation(value = "车辆运输管理配置新增修改")
    @RequestMapping(value = "/addOrUpdateVehicleTransport" , method = RequestMethod.POST)
    public void addOrUpdateVehicleTransport(@RequestBody VehicleTransportDTO dto){
        vehicleTransportService.addOrUpdateVehicleTransport(dto);
    }

    /**
     * 分页查询车辆运输管理配置信息
     */
    @ApiOperation(value = "分页查询车辆云南书管理配置信息")
    @RequestMapping(value = "/getVehicleTransportsByPage" , method = RequestMethod.POST)
    public IPage<VehicleTransportDTO> getVehicleTransportsByPage(@RequestBody VehicleTransportQueryDTO queryDTO){
        return vehicleTransportService.getVehicleTransportsByPage(queryDTO);
    }

    /**
     * 车辆运输管理配置删除
     * @param id
     */
    @ApiOperation(value = "车辆运输管理配置删除")
    @RequestMapping(value = "/deleteVehicleTransportById/{id}" , method = RequestMethod.DELETE)
    public void deleteVehicleTransportById(@PathVariable Integer id){
        vehicleTransportService.deleteVehicleTransportById(id);
    }

    @ApiOperation(value = "批量更改状态")
    @RequestMapping(value = "/update/batch" , method = RequestMethod.DELETE)
    public Boolean updateBatch(@RequestBody VehicleTransportBatchDTO ids){
        return vehicleTransportService.updateBatch(ids);
    }
}
