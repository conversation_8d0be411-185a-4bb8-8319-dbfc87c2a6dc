package com.hvisions.powder.sap.dto.art;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class ArtBaseDataDto {

    // 车牌号
    @JsonProperty("plateNo")
    private String plateNo;

    // 事由
    @JsonProperty("purpose")
    private String purpose;

    // 货物名称
    @JsonProperty("cargoName")
    private String cargoName;

    // 毛重
    @JsonProperty("grossWeight")
    private String grossWeight;

    // 皮重
    @JsonProperty("tare")
    private String tare;

    // 净重
    @JsonProperty("netWeight")
    private String netWeight;

    // 预计出园时间
    @JsonProperty("planOutTime")
    private String planOutTime;

    // 1-过夜，2-不过夜，如果过夜则planOutTime必填，且为第二天预计出园时间，出门条有效期截止到planOutTime，如果不过夜，则出门条有效期则保持现状，如30分钟后过期
    @JsonProperty("stayNightFlag")
    private String stayNightFlag;

    @JsonProperty("parkCode")
    private String parkCode;

}
