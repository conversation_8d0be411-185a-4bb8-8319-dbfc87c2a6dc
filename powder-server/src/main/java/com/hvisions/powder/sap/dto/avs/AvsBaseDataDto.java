package com.hvisions.powder.sap.dto.avs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class AvsBaseDataDto {

    @JsonProperty("deliveryNumber")
    private String deliveryNumber;

    @JsonProperty("vehicleNo")
    private String vehicleNo;

    @JsonProperty("cargoName")
    private String cargoName;

    // 货物规格
    @JsonProperty("cargoSpec")
    private String cargoSpec;

    // 发货单位
    @JsonProperty("srcName")
    private String srcName;

    // 收货单位
    @JsonProperty("dstName")
    private String dstName;

    @JsonProperty("remarks")
    private String remarks;

    // 业务类型：1-送货、2-提货
    @JsonProperty("businessType")
    @ApiModelProperty(value = "车辆类型：1-送货车、2-曲粉车、3-杂质车、4-外销曲车、5-曲渣车")
    private String businessType;

    // 类型 1-登记/更新、2-作废车辆
    @JsonProperty("type")
    private Integer type;

    @ApiModelProperty(value = "车辆定义：1.长期车；2.临时车")
    @JsonProperty("timeType")
    private String timeType;

}
