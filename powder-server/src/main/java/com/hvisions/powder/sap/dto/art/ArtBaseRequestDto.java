package com.hvisions.powder.sap.dto.art;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class ArtBaseRequestDto {
    public ArtBaseRequestDto(Request request) {
        this.request = request;
    }

    @JsonProperty("REQUEST")
    private Request request;

    @Data
    public static class Request {
        public Request(Head head, ArtBaseDataDto list) {
            this.head = head;
            this.list = list;
        }

        @JsonProperty("HEAD")
        private Head head;

        @JsonProperty("LIST")
        private ArtBaseDataDto list;

        @Data
        public static class Head {
            public Head() {
                this.account = "ZQMES";
                this.password = "";
                this.consumer = "ZQMES";
                this.count = "1";
                final String transaction = "LZLJ_ZQMES_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                this.biTransactionId = transaction;
                this.sevLevel = "NJ8MES";
                final String now = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
                this.requestDate = now;
            }

            @JsonProperty("ACCOUNT")
            private String account;

            @JsonProperty("PASSWORD")
            private String password;

            @JsonProperty("CONSUMER")
            private String consumer;

            @JsonProperty("COUNT")
            private String count;

            @JsonProperty("BIZTRANSACTIONID")
            private String biTransactionId;

            @JsonProperty("SRVLEVEL")
            private String sevLevel;

            @JsonProperty("REQUESTDATE")
            private String requestDate;

            @JsonProperty("ACCESSTOKEN")
            private String accessToken;
        }

    }
}
