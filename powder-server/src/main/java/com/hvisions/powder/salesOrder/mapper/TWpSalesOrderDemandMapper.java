package com.hvisions.powder.salesOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.powder.salesOrder.dto.TMpSalesOrderDemandDetailDTO;
import com.hvisions.powder.salesOrder.dto.TWpSalesOrderDemandPageDTO;
import com.hvisions.powder.salesOrder.dto.TWpSalesOrderDemandQueryDTO;
import com.hvisions.powder.salesOrder.entity.TWpDemand;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TWpSalesOrderDemandMapper extends BaseMapper<TWpDemand> {
    List<TWpSalesOrderDemandPageDTO> getDemandPageList(TWpSalesOrderDemandQueryDTO tWpSalesOrderDemandQueryDTO);

    int unbindSalesOrderDemandState(Integer orderId);

    int completeSalesOrderDemand(Integer orderId);

    List<TMpSalesOrderDemandDetailDTO> getSalesOrderDemandDetailList(Integer id);
}




