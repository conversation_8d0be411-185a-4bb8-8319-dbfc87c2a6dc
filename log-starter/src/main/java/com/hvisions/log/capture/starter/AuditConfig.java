package com.hvisions.log.capture.starter;

/**
 * <p>Title: AuditConfig</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/22</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AuditConfig {
    /**
     * 实例名称
     */
    private String entity;

    /**
     * 审计的操作
     */
    private String[] option;

    /**
     * 是否所有操作都记录
     */
    private Boolean allOption = true;
}









