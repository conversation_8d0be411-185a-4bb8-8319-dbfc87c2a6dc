package com.hvisions.equipmentmsd.dto.equipment.equipmentclass;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: EquipmentClassQuery</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EquipmentClassQuery extends PageInfo {
    public EquipmentClassQuery() {
        this.codeOrName = "";
    }

    /**
     * 类型编码或者名称
     */
    @ApiModelProperty(value = "类型编码或者名称")
    private String codeOrName;
}









