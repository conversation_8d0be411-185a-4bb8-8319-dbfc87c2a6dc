package com.hvisions.equipmentmsd.dto.equipment;

import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.equipmentmsd.dto.EquipmentMasterDataObjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: CellEquipmentDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/30</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class CellEquipmentDTO implements IObjectType {

    public CellEquipmentDTO() {
        this.equipmentIds = new ArrayList<>();
    }

    /**
     * cellId
     */
    @ApiModelProperty(value = "cellId")
    private Integer cellId;

    /**
     * 设备id列表
     */
    @ApiModelProperty(value = "设备Id与排序字段信息")
    private List<EquipmentOrderNumDTO> equipmentIds;

    @Override
    public Integer getObjectType() {
        return EquipmentMasterDataObjectTypeEnum.CELL_EQUIPMENT_DTO.getCode();
    }
}