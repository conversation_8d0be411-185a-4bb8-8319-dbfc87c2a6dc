package com.hvisions.equipmentmsd.dto.equipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: EquipmentTypeImportDTO</p>
 * <p>Description: 设备类型导入对象 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class EquipmentTypeImportDTO {
    /**
     * 设备类型编码
     */
    @ApiModelProperty(value = "设备类型编码")
    private String equipmentTypeCode;
    /**
     * 设备类型名称
     */
    @ApiModelProperty(value = "设备类型名称")
    private String equipmentTypeName;
    /**
     * 父级设备类型编码
     */
    @ApiModelProperty(value = "父级设备类型编码")
    private String parentTypeCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}









