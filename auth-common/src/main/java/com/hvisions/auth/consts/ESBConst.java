package com.hvisions.auth.consts;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 9:57
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "esb")
public class ESBConst implements InitializingBean {
    // iam 查询用户信息 esb url
    public static String IAM_URL;

    public static HttpHeaders getBaseAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content_Type", "application/json");
        return headers;
    }

    private String iamUrl;


    @Override
    public void afterPropertiesSet() {
        IAM_URL = iamUrl;
    }
}
