package com.hvisions.auth.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.auth.SysBaseDTO;
import com.hvisions.auth.dto.group.GroupDTO;
import com.hvisions.auth.enums.AuthObjectTypeEnum;
import com.hvisions.common.interfaces.IExtendObject;
import com.hvisions.common.interfaces.IObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * <p>Title: UserDTO</p>
 * <p>Description: 用户信息DTO </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserBaseDTO extends SysBaseDTO implements IObjectType, IExtendObject {
    private static final long serialVersionUID = 6257421472739782301L;

    public UserBaseDTO() {

        userName = "";
        description = "";
        userAccount = "";
        email = "";
        gender = 1;
        birthday = new Date();
        roleIdList = new ArrayList<>();
        departmentId = 0;
        extend = new HashMap<>();

    }

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "用户姓名不能为空")
    private String userName;
    /**
     * 备注
     */
    @ApiModelProperty(name = "描述", value = "描述")
    private String description;
    /**
     * 登录账号
     */
    @ApiModelProperty(name = "登录账号", value = "登录账号", required = true)
    @NotBlank(message = "用户账户不能为空")
    private String userAccount;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "邮箱地址", value = "邮箱地址")
    private String email;
    /**
     * 性别1:male 2:female
     */
    @ApiModelProperty(name = "性别：1：男，2：女", value = "性别：1：男，2：女", allowableValues = "1,2")
    private Integer gender;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "手机号", value = "手机号")
    private String mobilePhone;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "生日", value = "生日(yyyy-MM-dd)")
    private Date birthday;
    /**
     * 角色列表
     */
    @ApiModelProperty(value = " 角色列表")
    private List<Integer> roleIdList;
    /**
     * 部门Id
     */
    @ApiModelProperty(name = "部门Id", value = "部门Id")
    private Integer departmentId;
    /**
     * 扩展属性
     */
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend;

    /**
     * 首页模块Id
     */
    @ApiModelProperty(value = "首页模块ID")
    private Integer moduleId;
    /**
     * 首页模块Id
     */
    @ApiModelProperty(value = "APP首页模块ID")
    private Integer appModuleId;

    /**
     * 首页模块Id
     */
    @ApiModelProperty(value = "客户端首页模块ID")
    private Integer clientModuleId;
    /**
     * 账号是否锁定
     */
    @ApiModelProperty(value = "账号是否锁定",readOnly = true)
    private Boolean isLock;

    /**
     * 账号过期时间
     */
    @ApiModelProperty(value = "账号过期时间")
    private Date expireTime;
    
    /**
     * 用户组
     */
    @ApiModelProperty(value = "用户组",readOnly = true)
    private GroupDTO group;
    
    /**
     * 对象类型标识
     */
    @Override
    public Integer getObjectType() {
        return AuthObjectTypeEnum.USER_DTO.getCode();
    }
}
