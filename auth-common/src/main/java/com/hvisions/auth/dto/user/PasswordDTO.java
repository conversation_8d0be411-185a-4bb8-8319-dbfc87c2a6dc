package com.hvisions.auth.dto.user;

import com.hvisions.auth.enums.AuthObjectTypeEnum;
import com.hvisions.common.interfaces.IObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: PasswordDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/9</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class PasswordDTO implements IObjectType {
    /**
     * 登录账号
     */
    @ApiModelProperty(value = "用户账号")
    private String userAccount;
    /**
     * 登录密码
     */
    @ApiModelProperty(value = "新用户密码")
    private String userPassword;
    /**
     * 登录密码
     */
    @ApiModelProperty(value = "旧用户密码")
    private String oldPassword;

    @Override
    public Integer getObjectType() {
        return AuthObjectTypeEnum.PASSWORD_DTO.getCode();
    }
}
















