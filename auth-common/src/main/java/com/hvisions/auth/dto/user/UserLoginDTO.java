package com.hvisions.auth.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hvisions.auth.enums.AuthObjectTypeEnum;
import com.hvisions.common.interfaces.IObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: UserLoginDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/12</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class UserLoginDTO implements IObjectType {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;
    /**
     * 登录次数
     */
    @ApiModelProperty(value = "登录次数")
    private Integer loginCount = 1;
    /**
     * 登录账号
     */
    @ApiModelProperty(value = "用户登录账户")
    private String userAccount = "";
    /**
     * 登录密码
     */
    @JsonIgnore
    @ApiModelProperty(value = "用户密码")
    private String userPassword = "";
    /**
     * 登录密钥
     */
    @ApiModelProperty(value = "用户密钥")
    private String userSecretKey = "";
    /**
     * 登录提示
     */
    @ApiModelProperty(value = "密码提示")
    private String question = "";
    /**
     * 登录提示答案
     */
    @ApiModelProperty(value = "密码提示答案")
    private String answerQuestion = "";
    /**
     * 登录语言
     */
    @ApiModelProperty(value = "语言")
    private String language = "";
    /**
     * 登录主题
     */
    @ApiModelProperty(value = "主题")
    private String theme = "";
    /**
     * 更改密码时间
     */
    @ApiModelProperty(value = "修改密码日期")
    private Date changePasswordDate = new Date();
    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id")
    private Integer siteId = 1;
    /**
     * 关联的用户信息
     */
    @ApiModelProperty(value = "用户id")
    private Integer userId = 0;

    /**
     * 账号是否锁定
     */
    private Boolean isLock;
    /**
     * 是否是初始密码
     */
    private Boolean initialPassword;
    /**
     * 账号过期时间
     */
    private Date expireTime;

    @Override
    public Integer getObjectType() {
        return AuthObjectTypeEnum.USER_LOGIN_DTO.getCode();
    }
}
















