package com.hvisions.auth.dto.dictionary;

/**
 * <p>Title: InUseDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/2/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
public class InUseDTO {
    /**
     * 实体id
     */
    @ApiModelProperty(value = "实体id")
    @NotNull(message = "实体id不能为空")
    private Integer id;
    /**
     * 是否使用
     */
    @ApiModelProperty(value = "是否使用")
    @NotNull(message = "是否使用不能为空")
    private Boolean inUse;
}









