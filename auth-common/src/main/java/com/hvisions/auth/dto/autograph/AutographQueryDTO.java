package com.hvisions.auth.dto.autograph;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>Title: AutographQueryDTO</p >
 * <p>Description: 电子签名验证传参</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/2/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class AutographQueryDTO {

    /**
     * 登录账号
     */
    @ApiModelProperty(value = "用户账号",required = true)
    @NotBlank(message = "用户账号不能为空")
    private String userAccount;
    /**
     * 登录密码
     */
    @ApiModelProperty(value = "登录密码",required = true)
    @NotBlank(message = "用户密码不能为空")
    private String userPassword;
    /**
     * 菜单界面编码
     */
    @ApiModelProperty(value = "请求操作界面编码",required = true)
    @NotBlank(message = "请求操作界面编码不能为空")
    private String menuCode;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String operationContent;
}