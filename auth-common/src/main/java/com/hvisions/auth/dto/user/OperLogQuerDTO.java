package com.hvisions.auth.dto.user;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("用户登录日志查询DTO")
public class OperLogQuerDTO extends PageInfo implements Serializable {

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 操作内容
     */
    @ApiModelProperty("title")
    private String title;

    /**
     * 状态 1 成功; 2 失败
     */
    @ApiModelProperty("状态 0 成功; 1 失败")
    private String status;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 部门id
     */
    @ApiModelProperty("部门id")
    private Integer deptId;

    /** 模块  酿酒生产；制曲生产；原辅料采供；质检管理；工艺管理*/
    @ApiModelProperty("模块")
    private String model;
}
