package com.hvisions.auth.dto.user;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: UserQueryDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/6/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@ApiModel(description = "用户查询对象")
@Data
public class UserQueryDTO extends PageInfo {
    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "账号")
    private String account;
}









