package com.hvisions.quality.dto.quality.disqualified;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: 不合格评审处理
 * @author: Jcao
 * @time: 2022/3/11 15:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "组织不合格评审处理")
public class ReviewHandleDTO extends SysBaseDTO {

    @ApiModelProperty(value = "不合格评审id")
    private Integer id;

    @ApiModelProperty(value = "校验任务id")
    private Integer taskId;

    @ApiModelProperty(value = "类型：0、组长评审；1、原辅料评审；")
    @NotNull(message = "评审类型不能为空！")
    private Integer type;

    @ApiModelProperty(value = "评审结果;0-通过、1-驳回、2-退货、3-复检、4-报废、5-重蒸")
    @NotNull(message = "评审结果不能为空！")
    private String result;

    @ApiModelProperty(value = "意见")
    private String remark;

    @ApiModelProperty(value = "评审人")
    private String reviewPeople;

    @ApiModelProperty(value = "是否补货 1-是；0-否")
    private String isReplenishment;

    @ApiModelProperty(value = "补货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date replenishmentTime;

}
