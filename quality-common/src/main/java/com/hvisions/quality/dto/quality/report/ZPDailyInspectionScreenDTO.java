package com.hvisions.quality.dto.quality.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 糟培7天检验数据统计dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "糟培7天检验数据统计dto")
public class ZPDailyInspectionScreenDTO {

    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日期", example = "2022-08-09")
    private Date date;

    @ApiModelProperty(value = "酸度")
    private BigDecimal acidity;

    @ApiModelProperty(value = "淀粉")
    private BigDecimal starch;

    @ApiModelProperty(value = "水分")
    private BigDecimal water;

    @ApiModelProperty(value = "酒精度")
    private BigDecimal alcohol;

}
