package com.hvisions.quality.dto.quality.inspection;

import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 交酒报检参数dto
 * @author: yyy
 * @time: 2022/3/8 15:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "交酒报检参数dto")
public class LaunchInspectionWineDTO {

    @ApiModelProperty(value = "基酒流水号")
    private String qmOrderCode;

    @ApiModelProperty(value = "暂存罐编号")
    private String workshopTmpTankCode;

    @ApiModelProperty(value = "糟源代码")
    private String vinasseCode;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

}
