package com.hvisions.quality.dto.quality.disqualified;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 不合格检验评审列表
 * @author: Jcao
 * @time: 2022/5/20 10:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "不合格检验评审列表")
public class DisqualifiedListDTO {

    @ApiModelProperty(value = "不合格评审id")
    private Integer id;

    @ApiModelProperty(value = "检验任务id")
    private Integer taskId;

    @ApiModelProperty(value = "检验任务单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "质检结果")
    private String inspectionResult;

    @ApiModelProperty(value = "单位")
    private String unit;

}
