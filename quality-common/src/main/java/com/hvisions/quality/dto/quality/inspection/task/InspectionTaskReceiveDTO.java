package com.hvisions.quality.dto.quality.inspection.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 检验任务列表分页查询
 * @author: Jcao
 * @time: 2022/3/7 15:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "检验任务收样展示信息")
public class InspectionTaskReceiveDTO {

    @ApiModelProperty(value = "检验任务id")
    private Integer id;

    @ApiModelProperty(value = "取样任务id")
    private Integer samplingId;

    @ApiModelProperty(value = "检验任务单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "状态：0-新建、1-已取样、2-已关闭、3-已确认酒度、4-已经定级、5-已拒收、6-已收样、7-检验中、8-待审核、9-检验完成")
    private String state;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "样品名称")
    private String samplingName;

    @ApiModelProperty(value = "取样任务号")
    private String samplingOrder;

    @ApiModelProperty(value = "取样人")
    private String samplingPeople;

    @ApiModelProperty(value = "报检部门")
    private String departmentName;

    @ApiModelProperty(value = "取样地点")
    private String samplingLocation;

    @ApiModelProperty(value = "取样时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDate;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "样品数量")
    private BigDecimal standardCount;

    @ApiModelProperty(value = "样品重量")
    private BigDecimal standardWeight;

    @ApiModelProperty(value = "标签粘贴位置")
    private String labelPosition;

    @ApiModelProperty(value = "包装方式名称")
    private String packingTypeName;

    @ApiModelProperty(value = "收样时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveData;

    @ApiModelProperty(value = "收样状态;0-可收样，1-已收样，2-异常")
    private String receiveState;

    @ApiModelProperty(value = "糟源类型")
    private String leesType;

    @ApiModelProperty(value = "交酒段次")
    private String stageNum;

    @ApiModelProperty(value = "取样采集点数据集合")
    private List<SamplingCollectListDTO> samplingCollects;


}
