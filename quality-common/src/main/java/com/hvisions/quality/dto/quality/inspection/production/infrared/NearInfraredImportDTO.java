package com.hvisions.quality.dto.quality.inspection.production.infrared;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 近红外导入DTO
 * @author: yyy
 * @time: 2022/3/15 9:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "近红外导入DTO")
public class NearInfraredImportDTO {

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "酸度mmol/10g")
    private String acidity;

    @ApiModelProperty(value = "淀粉%")
    private String starch;

    @ApiModelProperty(value = "水分%")
    private String water;

    @ApiModelProperty(value = "酒精度%vol")
    private String alcohol;

    @ApiModelProperty(value = "检验结果（合格|| 不合格）")
    private String inspectionResult;

    @ApiModelProperty(value = "审核评语")
    private String reviewComments;

}
