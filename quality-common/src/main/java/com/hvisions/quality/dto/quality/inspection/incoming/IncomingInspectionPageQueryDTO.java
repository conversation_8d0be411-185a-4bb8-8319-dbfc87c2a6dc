package com.hvisions.quality.dto.quality.inspection.incoming;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 检验任务列表分页查询条件
 *
 * @author: Jcao
 * @time: 2022/3/7 15:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "检验任务列表分页查询条件")
public class IncomingInspectionPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "检验任务单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "检验场景id")
    private Integer scenesId;

    @ApiModelProperty(value = "检验场景编码")
    private String sceneCode;

    @ApiModelProperty(value = "检验状态")
    private String state;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "检验人")
    private String inspectionName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;
}
