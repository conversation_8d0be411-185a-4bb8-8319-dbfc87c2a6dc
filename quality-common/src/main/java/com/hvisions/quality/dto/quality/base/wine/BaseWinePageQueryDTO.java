package com.hvisions.quality.dto.quality.base.wine;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description 评语类型分页查询条件
 *
 * <AUTHOR>
 * @date 2022/2/28 17:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "评语类型分页查询条件")
public class BaseWinePageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "评语类型")
    private String name;


}
