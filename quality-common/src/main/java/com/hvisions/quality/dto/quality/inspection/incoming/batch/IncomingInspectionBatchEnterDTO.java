package com.hvisions.quality.dto.quality.inspection.incoming.batch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 来料检验结果批量录入dto
 * @author: yyy
 * @time: 2022/3/11 15:10
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "来料检验结果批量录入dto")
public class IncomingInspectionBatchEnterDTO {

    @ApiModelProperty(value = "保存：false，同步检验单：true")
    @NotNull(message = "保存提交标志不能为空")
    private Boolean flag;

    @ApiModelProperty(value = "检验人")
    private String inspectionName;

    @ApiModelProperty(value = "检验结果")
    private String inspectionResult;

    @ApiModelProperty(value = "收样组合id")
    private Integer groupId;

    @ApiModelProperty(value = "检验任务集合")
    @NotNull(message = "检验任务不能为空")
    List<InspectionTaskBatchDTO> inspectionTaskBatchDTOS;

}
