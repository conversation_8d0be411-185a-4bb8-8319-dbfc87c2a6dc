package com.hvisions.quality.dto.quality.sampling.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 样品报表分页查询条件
 * @author: Jcao
 * @time: 2022/3/8 16:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "样品报表分页查询条件")
public class SamplingReportPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "检验任务号")
    private String inspectionOrder;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "取样开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date samplingStartDate;

    @ApiModelProperty(value = "取样结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date samplingEndDate;

    @ApiModelProperty(value = "检验开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date inspectionStartDate;

    @ApiModelProperty(value = "检验结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date inspectionEndDate;

}
