package com.hvisions.quality.dto.quality.specimen.location;

import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/1 17:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "送样地点查询返回dto")
public class SpecimenLocationPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "送样地点")
    private String specimenLocation;

    @ApiModelProperty(value = "备注")
    private String remark;

}
