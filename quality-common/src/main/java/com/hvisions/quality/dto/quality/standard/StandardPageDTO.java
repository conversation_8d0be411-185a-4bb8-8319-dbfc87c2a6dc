package com.hvisions.quality.dto.quality.standard;

import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/3 17:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "检验标准分页查询dto")
public class StandardPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "检验标准编码")
    private String standardCode;

    @ApiModelProperty(value = "检验标准名称")
    private String standardName;

    @ApiModelProperty(value = "检验标准描述")
    private String standardDesc;

    @ApiModelProperty(value = "检验方法")
    private String standardMethods;

    @ApiModelProperty(value = "附件id")
    private Integer fileId;

    @ApiModelProperty(value = "关联物料")
    private String standardMaterials;

    @ApiModelProperty(value = "关联物料ids")
    private String standardMaterialIds;

    @ApiModelProperty(value = "检验场景")
    private String standardScenes;

    @ApiModelProperty(value = "检验场景ids")
    private String standardSceneIds;

    @ApiModelProperty(value = "版本号")
    private String standardVersion;

    @ApiModelProperty(value = "状态:新建、生效、归档")
    private String standardStatus;

}
