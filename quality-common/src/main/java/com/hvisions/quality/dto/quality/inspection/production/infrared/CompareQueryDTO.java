package com.hvisions.quality.dto.quality.inspection.production.infrared;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 对比查询条件dto
 * @author: yyy
 * @time: 2022/3/15 9:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "对比查询条件dto")
public class CompareQueryDTO {

    @ApiModelProperty(value = "检验类型:0-粮食检验、1-糟醅检验、2-基酒检验")
    private String inspectionType;

    @ApiModelProperty(value = "检验数据类型:0-检验单、1-预处理、2-中红外、3-近红外")
    private String type;

    @ApiModelProperty(value = "指标是否完整录入")
    private Boolean indicatorIsFull;

    @ApiModelProperty(value = "指标是录入情况 1.色谱已录完；2.色谱未录入；3.理化已录完；4.理化未录入")
    private String indicatorInput;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "检验任务号")
    private String inspectionOrder;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "状态：0-新建、1-已取样、2-已关闭、3-已确认酒度、4-已经定级、5-已拒收、6-已收样、7-检验中、8-待审核、9-检验完成")
    private String state;

    @ApiModelProperty(value = "检验开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "检验结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "交酒类别")
    private String tempTankCategoryName;

    @ApiModelProperty(value = "指标录入情况 1. 已完整录入;2. 未完整录入;3. 色谱未录入;4. 理化未录入")
    private String entryStatus;
}
