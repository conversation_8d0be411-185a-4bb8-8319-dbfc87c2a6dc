package com.hvisions.quality.dto.quality.specimen.abnormal;

import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022/2/28 16:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "样品异常类型新增修改dto")
public class SpecimenAbnormalDTO extends SysBaseDTO {

    @ApiModelProperty(value = "样品异常类型名称")
    @NotNull(message = "样品异常类型名称不能为空")
    @Size(min = 0, max = 10, message = "样品异常类型名称长度不能大于10")
    private String specimenAbnormalName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
