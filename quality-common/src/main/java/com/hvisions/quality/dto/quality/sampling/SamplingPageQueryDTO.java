package com.hvisions.quality.dto.quality.sampling;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 取样任务分页查询条件
 * @author: Jcao
 * @time: 2022/3/8 16:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "取样任务分页查询条件")
public class SamplingPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "取样任务号")
    private String samplingOrder;

    @ApiModelProperty(value = "场景编码集合，逗号隔开")
    private String sceneCodeList;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "检验任务号")
    private String inspectionOrder;

    @ApiModelProperty(value = "检验类型：0粮食检验，1糟培，2基酒")
    private String inspectionType;

    @ApiModelProperty(value = "一级部门id")
    private Integer topDepartmentId;

    @ApiModelProperty(value = "报检部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "样品名称")
    private String materialName;

    @ApiModelProperty(value = "检验场景id")
    private Integer sceneId;

    @ApiModelProperty(value = "取样状态;0-待取样、1-已取样")
    private String state;

    @ApiModelProperty(value = "取样地点")
    private String samplingSite;

    @ApiModelProperty(value = "计划取样开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDateStart;

    @ApiModelProperty(value = "计划取样结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDateEnd;

    @ApiModelProperty(value = "实际取样开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDateStart;

    @ApiModelProperty(value = "实际取样结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDateEnd;
}