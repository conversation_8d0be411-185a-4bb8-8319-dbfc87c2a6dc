package com.hvisions.quality.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "车辆运输管理配置查询信息")
public class VehicleTransportQueryDTO extends PageInfo {
    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "车辆状态(正常 1     检修 2)")
    private Integer state;

}
