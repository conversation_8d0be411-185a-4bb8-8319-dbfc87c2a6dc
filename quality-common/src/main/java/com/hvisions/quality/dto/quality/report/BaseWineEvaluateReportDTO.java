package com.hvisions.quality.dto.quality.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 基酒评语报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "基酒评语报表dto")
public class BaseWineEvaluateReportDTO {

    @ApiModelProperty(value = "中心名称")
    private String centerName;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "评语")
    private String evaluate;

    @ApiModelProperty(value = "数量")
    private Integer count;

}
