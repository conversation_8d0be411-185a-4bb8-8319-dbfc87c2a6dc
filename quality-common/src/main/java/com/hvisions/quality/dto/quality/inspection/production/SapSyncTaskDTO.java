package com.hvisions.quality.dto.quality.inspection.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;

/**
 * @Description: sap同步交酒任务dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "sap同步交酒任务dto")
public class SapSyncTaskDTO {

    @ApiModelProperty(value = "交酒任务id")
    private Integer id;

    @ApiModelProperty(value = "交酒任务号")
    private String handinTaskCode;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "sap同步状态(0未同步1已同步2冲销)，默认0")
    private int sapSyncStatus;

    @ApiModelProperty(value = "理化同步状态（0:未同步，1:已同步，默认为0")
    private Integer chemicalSynStatus;


    @ApiModelProperty(value = "中心")
    private String centerName;

    @ApiModelProperty(value = "车间")
    private String locationName;

    @ApiModelProperty(value = "坛号")
    private String pitCode;

    @ApiModelProperty(value = "交酒类别")
    private String tempTankCategoryName;

    @ApiModelProperty(value = "等级")
    private String finalLevel;

    @ApiModelProperty(value = "质检状态")
    private String inspectState;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "交酒完成日期")
    private LocalDate endTime;

    @ApiModelProperty(value = "定等时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date tastingDate;

    @ApiModelProperty(value = "窖龄")
    private Integer pitAge;

    @ApiModelProperty(value = "发酵天数")
    private Integer fermentationDays;

    @ApiModelProperty(value = "酒度")
    private Double standardVol;

    @ApiModelProperty(value = "原度重量")
    private Double quantity;

    @ApiModelProperty(value = "折合重量")
    private Double to60Quantity;

    @ApiModelProperty(value = "评语")
    private String evaluate;

    @ApiModelProperty(value = "检验备注")
    private String inspectionRemark;

    @ApiModelProperty(value = "总酸含量")
    private String totalAcidContent;

    @ApiModelProperty(value = "总酯含量")
    private String totalEsterContent;

    @ApiModelProperty(value = "乙酸乙酯含量")
    private String ethylAcetateContent;

    @ApiModelProperty(value = "丁酸乙酯含量")
    private String ethylButyrateContent;

    @ApiModelProperty(value = "乳酸乙酯含量")
    private String ethylLactateContent;

    @ApiModelProperty(value = "己酸乙酯含量")
    private String ethylCaproateContent;

    @ApiModelProperty(value = "流水码")
    private String serialNo;

    @ApiModelProperty(value = "收酒罐号")
    private String tankName;

    @ApiModelProperty(value = "暂存罐号")
    private String workshopTmpTankCode;


}
