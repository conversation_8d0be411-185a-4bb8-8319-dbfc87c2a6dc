package com.hvisions.quality.dto.quality.report.incomingInspection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 检验指标各月平均值报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "检验指标各月平均值报表dto")
public class MonthIndicatorAvgDTO {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "物料名称")
    private String samplingName;

    @ApiModelProperty(value = "指标编码")
    private String indicatorCode;

    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "标准值")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "规格上限")
    private BigDecimal upperSpecificationLimit;

    @ApiModelProperty(value = "规格下限")
    private BigDecimal lowerSpecificationLimit;

    @ApiModelProperty(value = "月指标数据详情")
    private List<MonthIndicatorAvgDetailDTO> monthIndicatorAvgDetailDTOList;
}
