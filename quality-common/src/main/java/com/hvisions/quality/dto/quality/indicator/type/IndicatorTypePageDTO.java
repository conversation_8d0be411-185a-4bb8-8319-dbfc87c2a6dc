package com.hvisions.quality.dto.quality.indicator.type;

import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/28 15:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "指标类型分页查询")
public class IndicatorTypePageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "指标类型编码")
    private String indicatorTypeCode;

    @ApiModelProperty(value = "指标类型名称")
    private String indicatorTypeName;

}
