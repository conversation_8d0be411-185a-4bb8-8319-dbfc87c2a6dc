package com.hvisions.equipmentmsd.client.fallback;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.EquipmentFeignClient;
import com.hvisions.equipmentmsd.dto.equipment.CellWithEquipmentDTO;
import com.hvisions.equipmentmsd.dto.equipment.EquipmentBaseDTO;
import com.hvisions.equipmentmsd.dto.equipment.EquipmentDTO;
import com.hvisions.equipmentmsd.dto.equipment.EquipmentQueryDTO;
import com.hvisions.equipmentmsd.dto.location.LocationMsgDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: EquipmentFeignClientFallBack</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/8/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class EquipmentFeignClientFallBack extends BaseFallbackFactory<EquipmentFeignClient> {
    @Override
    public EquipmentFeignClient getFallBack(ResultVO vo) {
        return new EquipmentFeignClient() {
            @Override
            public ResultVO createEquipmentColumn(ExtendColumnInfo extendColumnInfo) {
                return vo;
            }

            /**
             * 更新设备扩展属性
             *
             * @param extendInfo 扩展属性信息
             */
            @Override
            public ResultVO updateEquipmentExtendInfo(ExtendInfo extendInfo) {
                return vo;
            }

            @Override
            public ResultVO deleteEquipmentColumnByColumnName(String columnName) {
                return vo;
            }

            @Override
            public ResultVO<EquipmentBaseDTO> createEquipment(EquipmentBaseDTO hvEquipmentDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ExtendColumnInfo>> getEquipmentExtendColumnInfo() {
                return vo;
            }

            @Override
            public ResultVO deleteEquipmentById(int id) {
                return vo;
            }

            @Override
            public ResultVO<EquipmentBaseDTO> updateEquipment(EquipmentBaseDTO hvEquipmentDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<EquipmentDTO>> getEquipmentPageByNameOrCodeAndEquipmentTypeId(EquipmentQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getAllEquipment() {
                return vo;
            }

            @Override
            public ResultVO<EquipmentDTO> getEquipmentByCode(String equipmentCode) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getEquipmentListByParentId(int parentId) {
                return vo;
            }

            @Override
            public ResultVO<EquipmentDTO> getEquipmentById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getEquipmentById(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getEquipmentListByEquipmentTypeId(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getEquipmentListByCellId(int id) {
                return vo;
            }

            @Override
            public ResultVO updateCellEquipmentRelation(CellWithEquipmentDTO cellEquipmentDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteCellEquipmentRelation(int equipmentId, int cellId) {
                return vo;
            }

            @Override
            public ResultVO<LocationMsgDTO> getLocationMsgDtoByEquipmentId(int id) {
                return vo;
            }

            @Override
            public ResultVO<Map<Integer, LocationMsgDTO>> getLocationMsgDtoListByEquipmentIds(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getAllEquipmentByCellId(int cellId) {
                return vo;
            }

            /**
             * 根据产线Id列表查询设备
             *
             * @param cellIdList 产线ID列表
             * @return 设备列表
             */
            @Override
            public ResultVO<List<EquipmentDTO>> getAllEquipmentByCellIdList(List<Integer> cellIdList) {
                return vo;
            }
        };
    }
}









