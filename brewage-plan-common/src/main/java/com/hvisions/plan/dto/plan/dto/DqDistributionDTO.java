/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.dto.plan.dto;

import com.hvisions.plan.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname DqDistribution
 * @description 大曲配送dto
 * @date 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("大曲配送dto")
public class DqDistributionDTO extends SysBaseDTO {

    @ApiModelProperty("配方编号")
    private String code;

    @ApiModelProperty("配方名称")
    private String name;

    @ApiModelProperty("大曲类型id")
    private Integer materialId;

    @ApiModelProperty("大曲类型编码")
    private String materialCode;

    @ApiModelProperty("大曲类型名称")
    private String materialName;

    @ApiModelProperty("曲斗类型")
    private String parameterType;

    @ApiModelProperty("配送数量KG")
    private BigDecimal shippingNumber;

    @ApiModelProperty("生效开始日期")
    private String beginTime;

    @ApiModelProperty("生效结束日期")
    private String endTime;

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

    @ApiModelProperty("版本")
    private Integer version;
}