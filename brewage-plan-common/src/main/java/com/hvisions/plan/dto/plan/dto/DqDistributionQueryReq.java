/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.dto.plan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @classname DqDistributionQueryReq
 * @description 大曲配送数据传输查询参数实体类
 * @date 2022-03-23
 */
@Data
@ApiModel("DqDistribution数据传输查询参数实体类")
public class DqDistributionQueryReq implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("生产基地ID")
    private Integer productionBaseId;

    @ApiModelProperty("配方名称")
    private String formulaName;

    @ApiModelProperty("大曲类型")
    private Integer materialId;

    @ApiModelProperty("曲斗类型")
    private String parameterType;

    @ApiModelProperty("日期 yyyy-MM-dd")
    private String date;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String dataState;

    @ApiModelProperty("曲粉发货单ID")
    private Integer issueOrderId;
}