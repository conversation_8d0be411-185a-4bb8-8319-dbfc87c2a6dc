/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.dto.plan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @classname RowQueryReq
 * @description 排次信息数据传输查询参数实体类
 * @date 2022-03-23
 */
@Data
@ApiModel("Row数据传输查询参数实体类")
public class RowQueryReq implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "生产基地")
    private Integer productionBaseId;
    @ApiModelProperty(value = "排次名称")
    private String rowName;
    @ApiModelProperty("是否热季检修")
    private Boolean checkState;
    @ApiModelProperty(value = "排次状态 0-新建、1-生效、2-存档")
    private String rowState;
    @ApiModelProperty(value = "创建开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @ApiModelProperty(value = "创建结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
}