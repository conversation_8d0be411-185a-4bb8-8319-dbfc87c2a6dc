package com.hvisions.plan.dto.plan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;


/**
 * 采购计划对接月计划请求类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel("采购计划对接月计划请求类")
public class PurchasePlanReq {
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private LocalDate beginDate;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private LocalDate endDate;
}