package com.hvisions.quality;

import com.hvisions.quality.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/28 16:25
 */
@Slf4j
@RunWith(SpringRunner.class)
//@SpringBootTest(classes = BrewageApplication.class)
public class YyyTest {


    @Test
    public void yyy() throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Calendar calendar = Calendar.getInstance();
        System.out.println(calendar.get(Calendar.MONTH));

        int month = calendar.get(Calendar.MONTH) + 1;

        System.out.println(month);

        Date appointDay = DateUtil.getAppointDay(new Date(), 1);
        String format = simpleDateFormat.format(appointDay);
        System.out.println(format);

        int i = Calendar.getInstance().get(Calendar.YEAR);
        System.out.println(i);

    }


}
