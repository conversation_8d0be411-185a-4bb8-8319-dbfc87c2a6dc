package com.hvisions.quality.service.imp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.quality.dao.VehicleTransportMapper;
import com.hvisions.quality.dto.VehicleTransportDTO;
import com.hvisions.quality.dto.VehicleTransportQueryDTO;
import com.hvisions.quality.entity.VehicleTransport;
import com.hvisions.quality.repository.VehicleTransportRepository;
import com.hvisions.quality.service.VehicleTransportService;
import com.hvisions.common.exception.BaseKnownException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
@Slf4j
public class VehicleTransportServiceImpl implements VehicleTransportService {
    @Resource
    VehicleTransportMapper vehicleTransportMapper;
    @Resource
    VehicleTransportRepository vehicleTransportRepository;


    /**
     * 车辆运输管理配置新增修改
     * @param dto
     */
    @Override
    public void addOrUpdateVehicleTransport(VehicleTransportDTO dto) {
        VehicleTransport vehicleTransport = new VehicleTransport();
        if (dto.getId() == null || dto.getId() == 0){
            vehicleTransport.setLicensePlateNumber(dto.getLicensePlateNumber());
            vehicleTransport.setDriverName(dto.getDriverName());
            vehicleTransport.setState(dto.getState());
        }else {
            Optional<VehicleTransport> vehicleTransportOptional = vehicleTransportRepository.findById(dto.getId());
            if (!vehicleTransportOptional.isPresent()){
                throw new BaseKnownException(110,"未找到车辆运输信息");
            }
            vehicleTransport = vehicleTransportOptional.get();
            vehicleTransport.setState(dto.getState());
            vehicleTransport.setDriverName(dto.getDriverName());
            vehicleTransport.setLicensePlateNumber(dto.getLicensePlateNumber());
        }
        vehicleTransportRepository.save(vehicleTransport);
    }

    /**
     * 分页查询车辆运输管理配置信息
     * @param queryDTO
     * @return
     */
    @Override
    public IPage<VehicleTransportDTO> getVehicleTransportsByPage(VehicleTransportQueryDTO queryDTO) {
        IPage<VehicleTransportDTO> page = new Page<>(queryDTO.getPage(),queryDTO.getPageSize());
        page = vehicleTransportMapper.getVehicleTransportsByPage(page,queryDTO);
        if (page.getRecords().size() > 0){
            page.getRecords().forEach(item -> {
                if (item.getState() == 1){
                    item.setStateName("正常");
                }else if (item.getState() == 2){
                    item.setStateName("检修");
                }
            });
        }
        return page;
    }

    /**
     * 车辆运输管理配置删除
     * @param id
     */
    @Override
    public void deleteVehicleTransportById(Integer id) {
        vehicleTransportRepository.deleteById(id);
    }

}
