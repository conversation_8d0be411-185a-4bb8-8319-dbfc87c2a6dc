package com.hvisions.quality.common.service;

import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import io.vavr.Tuple2;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/8 13:53
 */
public interface SystemCommonService {
	/**
	 * 获取当前车间
	 *
	 * @param userId
	 * @return
	 */
	List<LocationDTO> getLocalLocation(Integer userId);

	/**
	 * 计划w酒比例跟新
	 *
	 * @param input      是否是投入比例
	 * @param proportion 比例
	 */
	void updateWProportion(Boolean input, Integer proportion);

	/**
	 * 计划w酒比例获取
	 *
	 * @param input 是否是投入比例
	 */
	BigDecimal getWProportion(Boolean input);

	/**
	 * 计划w酒比例（投入，产出）获取
	 *
	 * @return Tuple2<BigDecimal, BigDecimal>
	 * <AUTHOR>
	 * @date 2023/3/7 16:42
	 */
	Tuple2<BigDecimal, BigDecimal> getWProportion();

	/**
	 * 跟新看板集合配置内容
	 *
	 * @param key    key
	 * @param values values
	 * <AUTHOR>
	 * @date 2023/3/7 17:01
	 */
	void updateRedisList(String key, List<String> values);

	/**
	 * 获取看板集合配置内容
	 *
	 * @param key key
	 * @return List<String>
	 * <AUTHOR>
	 * @date 2023/3/7 17:01
	 */
	List<String> getRedisList(String key);

	String getTransportManager();
}
