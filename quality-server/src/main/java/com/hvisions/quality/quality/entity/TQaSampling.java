package com.hvisions.quality.quality.entity;

import com.hvisions.quality.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Description: 取样
 * @author: Jcao
 * @time: 2022/3/7 16:11
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_qa_sampling")
public class TQaSampling extends SysBase {

    /*
     * 取样标准id
     */
    private Integer standardId;

    /*
     * 取样任务单号
     */
    private String samplingOrder;

    /*
     * 物料id
     */
    private Integer materialId;

    /*
     * 物料编码
     */
    private String materialCode;

    /*
     * 物料名称
     */
    private String materialName;

    /*
     * 取样人
     */
    private String samplingPeople;

    /*
     *取样人id
     */
    private Integer samplingPeopleId;

    /*
     * 取样状态;0-待取样、1-已取样
     */
    private String state;

    /*
     * 取样计划时间
     */
    private Date planDate;

    /*
     * 实际开始时间
     */
    private Date actualDate;

    /*
     * 是否是复检：0否，1是
     */
    private Boolean reinspection;

    /*
     * 取样地点
     */
    private String samplingLocation;

}
