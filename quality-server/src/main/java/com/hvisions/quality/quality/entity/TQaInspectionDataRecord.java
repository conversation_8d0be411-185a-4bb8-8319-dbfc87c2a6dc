package com.hvisions.quality.quality.entity;

import com.hvisions.quality.entity.SysBase;
import com.hvisions.quality.quality.consts.QualityPrecisionEnum;
import com.hvisions.quality.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 检验数据记录
 * @date 2024/4/11 14:21
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_qa_inspection_data_record")
public class TQaInspectionDataRecord extends SysBase {

    /**
     * 检验任务id
     */
    private Integer inspectionId;

    /**
     * 类型（1:快检设备数据  2：手工录入数据）
     */
    private String type;

    @ApiModelProperty(value = "标准酒度(%vol)")
    private BigDecimal modifyNormStandardVol;

    @ApiModelProperty(value = "己酸乙酯含量g/L")
    private String ethylCaproateContent;

    @ApiModelProperty(value = "乳酸乙酯含量g/L")
    private String ethylLactateContent;

    @ApiModelProperty(value = "乙酸乙酯含量g/L")
    private String ethylAcetateContent;

    @ApiModelProperty(value = "丁酸乙酯含量g/L")
    private String ethylButyrateContent;

    @ApiModelProperty(value = "总酸含量g/L")
    private String totalAcidContent;

    @ApiModelProperty(value = "总酯含量g/L")
    private String totalEsterContent;

    /**
     * 样品编码
     */
    private String sampleCode;

    /**
     * 备注
     */
    private String remark;

}
