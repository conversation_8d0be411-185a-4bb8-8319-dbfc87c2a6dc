package com.hvisions.quality.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.quality.dto.quality.review.ReviewPageDTO;
import com.hvisions.quality.dto.quality.review.ReviewPageQueryDTO;
import com.hvisions.quality.quality.entity.TQaReview;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 曲粉异常评审
 * @author: yyy
 * @time: 2022/3/11 9:35
 */
@Mapper
public interface ReviewMapper extends BaseMapper<TQaReview> {

    /**
     * @Description 获取曲粉异常评审列表
     *
     * <AUTHOR>
     * @Date 2023-12-26 17:44
     * @param queryDTO
     * @return java.util.List<com.hvisions.quality.dto.quality.review.ReviewPageDTO>
     **/
    List<ReviewPageDTO> getReviewPageList(ReviewPageQueryDTO queryDTO);


}
