package com.hvisions.quality.quality.entity;

import com.hvisions.quality.entity.SysBase;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 尝评
 * @author: Jcao
 * @time: 2022/3/8 10:46
 */

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_qa_tasting")
public class TQaTasting extends SysBase {

    /*
     * 检验任务id
     */
    private Integer inspectionId;

    /*
     * 尝评任务单号
     */
    private String tastingOrder;

    /*
     * 任务状态;0-新建、1-已确认酒度、2-尝评完成、3-复评完成、4-关闭,5已选择场景(先选择场景在确定酒度)
     */
    private String tastingState;

    /*
     * 确认结果
     */
    private String confirmResult;

    /*
     * 确认备注
     */
    private String confirmRemark;

    /*
     * 酒精度确认人
     */
    private String confirmPeople;

    /*
     * 酒精度确认时间
     */
    private Date confirmDate;


    // 定级糟源类别
    private String gradingLeesType;

    /*
     * 定级等级
     */
    private String level;

    /*
     * 定级评语ids
     */
    private String gradingEvaluateIds;
    /*
      尝评系统特定评语
     */
    private String cpEvaluate;
    /*
      尝评系统特定复评评语
     */
    private String cpReviewEvaluate;
    /**
     * 定级评语类型id
     */
    private String baseWineIds;

    // 复评糟源类别
    private String reviewLeesType;

    /*
     * 复评等级
     */
    private String reviewLevel;

    /*
     * 复评评语ids
     */
    private String reviewEvaluateIds;

    /**
     * 定级评语类型id
     */
    private String reviewBaseWineIds;

    /*
     * 尝评人
     */
    private String tastingPeople;

    /*
     * 尝评日期
     */
    private Date tastingDate;

    /*
     * 最终定级结果
     */
    private String finalLevel;


    /*
    酿酒中心id
     */
    private Integer centerId;

    /*
     * 暂存罐号
     */
    private String tankNo;

    /**
     * 定级场景：0-无 1-现场定级，2-集中定级
     */
    private String tastingScene;

    /**
     * 定级场景选择日期
     */
    private Date sceneDate;

    /**
     * 定级场景选择人
     */
    private String scenePeople;
    /**
     * 定级场景选择备注
     */
    private String sceneRemark;

    /**
     * 对接状态：0-待推送，1-执行中，2-已完成
     */
    private String dockState;

    /**
     * 推送时间
     */
    private Date pushDate;

    /**
     * 接收时间
     */
    private Date acceptDate;

    /**
     * 定级完成状态 0 未完成，1已完成
     */
    @Builder.Default
    private String completeState = "0";

}
