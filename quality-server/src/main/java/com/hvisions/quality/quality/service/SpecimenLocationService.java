package com.hvisions.quality.quality.service;


import com.hvisions.quality.dto.quality.specimen.location.SpecimenLocationDTO;
import com.hvisions.quality.dto.quality.specimen.location.SpecimenLocationPageDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 送样地点
 * @date 2022/3/1 14:20
 */
public interface SpecimenLocationService {

    List<SpecimenLocationPageDTO> getSpecimenLocationList();

    Integer addSpecimenLocation(SpecimenLocationDTO specimenLocationDTO);

    Integer updateSpecimenLocation(SpecimenLocationDTO specimenLocationDTO);

    Integer deleteSpecimenLocation(Integer id);
}
