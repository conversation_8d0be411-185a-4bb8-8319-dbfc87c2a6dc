package com.hvisions.quality.quality.service;

import com.hvisions.quality.dto.quality.process.point.ProcessPointDTO;
import com.hvisions.quality.dto.quality.process.point.ProcessPointPageDTO;
import com.hvisions.quality.dto.quality.process.point.ProcessPointPageQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 过程项点
 * @date 2022/2/28 16:26
 */
public interface ProcessPointService {

    /*
     * @description: 分页查询过程项点
     * <AUTHOR>
     * @date 2022/2/28 17:07
     * @param processPointPageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.process.point.ProcessPointPageDTO>
     */
    Page<ProcessPointPageDTO> getProcessPointPageList(ProcessPointPageQueryDTO processPointPageQueryDTO);

    /*
     * @description: 获取过程项点列表
     * <AUTHOR>
     * @date 2022/5/30 17:21
     * @param
     * @return java.util.List<com.hvisions.quality.dto.quality.process.point.ProcessPointPageDTO>
     */
    List<ProcessPointPageDTO> getNotInIndicatorProcessPointList();

    /*
     * @description: 新增过程项点
     * <AUTHOR>
     * @date 2022/2/28 17:37
     * @param processPointDTO
     * @return java.lang.Integer
     */
    Integer addProcessPoint(ProcessPointDTO processPointDTO);

    /*
     * @description: 修改过程项点
     * <AUTHOR>
     * @date 2022/2/28 17:38
     * @param processPointDTO
     * @return java.lang.Integer
     */
    Integer updateProcessPoint(ProcessPointDTO processPointDTO);

    /*
     * @description: 删除过程项点
     * <AUTHOR>
     * @date 2022/2/28 17:38
     * @param id
     * @return java.lang.Integer
     */
    Integer deleteProcessPoint(Integer id);


}
