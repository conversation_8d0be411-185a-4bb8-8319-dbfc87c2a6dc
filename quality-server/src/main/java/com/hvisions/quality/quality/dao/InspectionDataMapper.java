package com.hvisions.quality.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.quality.dto.quality.inspection.production.infrared.InspectionDataDTO;
import com.hvisions.quality.quality.entity.TQaInspectionData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 检验数据
 * @author: Jcao
 * @time: 2022/3/7 16:14
 */
@Mapper
public interface InspectionDataMapper extends BaseMapper<TQaInspectionData> {

    /*
     * @description: 获取检验单和预处理的检验数据
     * <AUTHOR>
     * @date 2022/7/21 11:45
     * @return java.util.List<com.hvisions.quality.dto.quality.inspection.production.infrared.InspectionDataDTO>
     */
    List<InspectionDataDTO> getInspectionData();


    /**
     * 查询标准酒度
     * @param samplingId
     * @return
     */
    String selectModifyNormStandardVol(Integer samplingId);
}
