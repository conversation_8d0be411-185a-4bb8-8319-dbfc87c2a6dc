<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.quality.quality.dao.SceneMapper">

    <!-- 根据id获取校验场景详情 -->
    <select id="getSceneDetail" resultType="com.hvisions.quality.dto.quality.scene.SceneDetailDTO">
        SELECT s.*,
        (SELECT GROUP_CONCAT( si.indicator_id ) FROM t_qa_scene_indicator si
            WHERE s.id = si.scene_id AND si.deleted = 0 GROUP BY si.scene_id) indicator_id
        FROM t_qa_scene s
        WHERE s.deleted = 0 AND s.id = #{id}
    </select>

    <!--    根据场景id获取检验指标列表-->
    <select id="getSceneIndicatorListById" resultMap="SceneIndicatorDTOMap">
        SELECT i.*,it.indicator_type_name indicator_type,ii.item_name
        FROM t_qa_scene_indicator si
        LEFT JOIN t_qa_indicator i ON i.id = si.indicator_id AND i.deleted = 0
        LEFT JOIN t_qa_indicator_type it ON i.indicator_type_id = it.id AND it.deleted = 0
        LEFT JOIN t_qa_indicator_item ii ON ii.indicator_id = i.id AND ii.deleted = 0
        WHERE si.deleted = 0 AND si.scene_id = #{id}
    </select>

    <resultMap id="SceneIndicatorDTOMap" type="com.hvisions.quality.dto.quality.scene.SceneIndicatorDTO">
        <result property="id" column="id"></result>
        <result property="indicatorCode" column="indicator_code"></result>
        <result property="indicatorName" column="indicator_name"></result>
        <result property="indicatorDesc" column="indicator_desc"></result>
        <result property="indicatorType" column="indicator_type"></result>
        <result property="entryMethod" column="entry_method"></result>
        <result property="standardValue" column="standard_value"></result>
        <result property="upperSpecificationLimit" column="upper_specification_limit"></result>
        <result property="lowerSpecificationLimit" column="lower_specification_limit"></result>
        <result property="unit" column="unit"></result>
        <result property="formula" column="formula"></result>
        <collection property="itemList" javaType="java.util.List" ofType="java.lang.String">
            <result column="item_name" />
        </collection>
    </resultMap>


</mapper>
