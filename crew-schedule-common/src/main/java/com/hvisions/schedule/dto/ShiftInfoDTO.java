package com.hvisions.schedule.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>Title: ShiftInfoDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/6/11</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class ShiftInfoDTO extends SysBaseDTO implements Comparable<ShiftInfoDTO> {

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id,0表示默认车间,此字段默认为0")
    private Integer areaId;

    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id,0表示班次为车间班次，此字段默认为0")
    private Integer cellId;

    /**
     * 班次id
     */
    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    /**
     * 班次名称
     */
    @ApiModelProperty(value = "班次名称,默认为空字符串,不能为空",required = true)
    private String shiftName;

    /**
     * 班次编码
     */
    @ApiModelProperty(value = "班次编码,默认为空字符串,不能为空",required = true)
    private String shiftCode;

    /**
     * 班组id
     */
    @ApiModelProperty(value = "班组id")
    private Integer crewId;

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称,默认为空字符串,不能为空",required = true)
    private String crewName;

    /**
     * 班组编码
     */
    @ApiModelProperty(value = "班组编码,默认为空字符串,不能为空",required = true)
    private String crewCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 排班日期
     */
    @ApiModelProperty(value = "排班日期")
    private LocalDate scheduleDay;




    @Override
    public int compareTo(ShiftInfoDTO o) {
        return this.getStartTime().compareTo(o.getStartTime());
    }
}










