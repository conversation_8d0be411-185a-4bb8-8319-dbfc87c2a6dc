package com.hvisions.schedule.dto;

import com.hvisions.schedule.enums.ScheduleObjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: ScheduleWithInfoDTO</p>
 * <p>Description: 带班组名称与班次名称的排班计划</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "班次信息，附带班组名称和班次名称，用于界面展示")
public class ScheduleWithInfoDTO extends ScheduleDTO {
    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;
    /**
     * 班次名称
     */
    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    @Override
    public Integer getObjectType() {
        return ScheduleObjectTypeEnum.SCHEDULE_WITH_INFO_DTO.getCode();
    }
}









