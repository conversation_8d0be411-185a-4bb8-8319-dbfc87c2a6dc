package com.hvisions.schedule.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: ShiftCrewQueryResult</p>
 * <p>Description: 根据车间，产线统一的查询对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/21</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@ApiModel(description = "根据车间，产线获取对应的班组班次信息")
public class ShiftCrewQueryResult {
    /**
     * 是否结果是通用班组产线信息
     */
    @ApiModelProperty(value = "是否结果是通用班组产线信息,0：原车间产线，1：产线没有配置，返回车间配置，2：返回通用配置，车间，产线都为0")
    private Integer defaultCell;
    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer areaId;
    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer cellId;
    /**
     * 班组列表
     */
    @ApiModelProperty(value = "班组列表")
    private List<CrewWithMemberDTO> crewDTOS;
    /**
     * 班次列表
     */
    @ApiModelProperty(value = "班次列表")
    private List<ShiftDTO> shiftDTOS;
}









