#默认公共配置
spring:
  # 关闭jmx
  jmx:
    enabled: false
  profiles:
    active: dev
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  #使用redis作为缓存
  cache:
    type: redis
  #redis 地址和端口号
  redis:
    host: ************
    port: 6379
  #序列化时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #是否打印Jpa生成的sql语句,生产环境可以关闭 默认 ture
    show-sql: false
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名
  application:
    name: timer
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  cloud:
    refresh:
      #为了解决springboot与spring cloud数据库初始化检查添加的配置项的循环依赖问题所添加
      refreshable: none
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
mybatis:
  typeAliasesPackage: com.hvisions.timer.entitys
  mapperLocations: classpath:mapper/*.xml
h-visions:
  # 此处配置为audit信息的创建方式。dto 为当请求控制器的时候如果入参为SysDTO可以自动赋值。jpa为使用jpa的audit方式进行实现。
  audit:
    mode: jpa
  #是否添加所有控制器请求记录到log服务
  log:
    enable: false
  #服务名称,可以使用中文，日志服务会使用
  service-name: 周期服务
  #可以使用swagger的接口，使用对应的测试方法，生成api文档，支持markdown和ascii
  swagger:
    api-url: http://localhost:${server.port}/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: @project.artifactId@
    version: @project.version@
    server-name: ${h-visions.service-name}
eureka:
  instance:
    prefer-ip-address: true
    #实例名
    instance-id: timer:${server.port}
  client:
    service-url:
      #euraka地址
      defaultZone: http://localhost:8763/eureka/