SUCCESS=SUCCESS
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=const_violate
PARAMS_ERROR=params error
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
CONSTRAINT_VIOLATION=CONSTRAINT_VIOLATION
EXTEND_TABLE_SHOULD_HAS_INT_ID=extend property should has int id
LOCATION_TYPE_NOT_SUPPORT=LOCATION_TYPE_NOT_SUPPORT
TYPE_MISMATCHING_PARENT_ID=TYPE_MISMATCHING_PARENT_ID
PARENT_ID_NOT_EXISTS=PARENT_ID_NOT_EXISTS
EQUIPMENT_TYPE_NOT_EXISTS=EQUIPMENT_TYPE_NOT_EXISTS
CELL_NOT_EXISTS=CELL_NOT_EXISTS
COLUMN_EXISTS=COLUMN_EXISTS
EQUIPMENT_TYPE_IN_USE=EQUIPMENT_TYPE_IN_USE
EQUIPMENT_TYPE_SUB_HAVE_DATA=EQUIPMENT_TYPE_SUB_HAVE_DATA
LOCATION_HAVE_DATA=LOCATION_HAVE_DATA
EQUIPMENT_HAS_BEEN_CELL=EQUIPMENT_HAS_BEEN_CELL
CELL_HAS_BEEN_EQUIPMENT=CELL_HAS_BEEN_EQUIPMENT
LOCATION_ID_IS_NOT_NULL=LOCATION_ID_IS_NOT_NULL
PLEASE_MAKE_SURE_TO_SELECT_CELL=PLEASE_MAKE_SURE_TO_SELECT_CELL
EQUIPMENT_EXISTS_DATA=EQUIPMENT_EXISTS_DATA
EXPORT_ERROR=EXPORT_ERROR
COLUMN_EXISTS_VALUE=COLUMN_EXISTS_VALUE
LOCATION_HAVE_EQUIPMENT=LOCATION_HAVE_EQUIPMENT
