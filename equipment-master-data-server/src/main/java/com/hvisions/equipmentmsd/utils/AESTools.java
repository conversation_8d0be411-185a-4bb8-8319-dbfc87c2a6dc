package com.hvisions.equipmentmsd.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <p>Title: AESTools</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>createParameter date: 2019/5/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public class AESTools {

    /**
     * 偏移量
     */
    private static final String OFFSET = "hvisions30151111";
    /**
     * 编码
     */
    private static final String ENCODING = "UTF-8";
    /**
     * 算法
     */
    private static final String ALGORITHM = "AES";
    /**
     * 默认的加密算法
     */
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    /**
     * 解密
     *
     * @param data     数据
     * @param password 密码
     * @return 解析结果
     */
    public static String decryptString(byte[] data, String password) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec key = new SecretKeySpec(password.getBytes(ENCODING), ALGORITHM);
        IvParameterSpec iv = new IvParameterSpec(OFFSET.getBytes());
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        byte[] encrypted = cipher.doFinal(data);
        return new String(encrypted, ENCODING);
    }

    /**
     * 加密字符串
     *
     * @param str 密钥
     * @return 加密数据
     */
    public static byte[] encryptString(String str, String password) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(ENCODING), ALGORITHM);
        //使用CBC模式，需要一个向量iv，可增加加密算法的强度
        IvParameterSpec iv = new IvParameterSpec(OFFSET.getBytes(ENCODING));
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
        return cipher.doFinal(str.getBytes(ENCODING));
    }


}
