package com.hvisions.equipmentmsd.location.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <p>Title: HvBmLocationArea</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
public class HvBmLocationWorkCenter {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 工厂建模ID
     */
    private Integer locationId;

}
