package com.hvisions.purchase.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailInfoDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.VehicleInspectStateUpdateDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.VehicleStateUpdateDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: ReceivingClient
 * @author: yyy
 * @time: 2023/12/12 10:11
 */
@FeignClient(name = "purchase-server", path = "/receiving", fallbackFactory = ReceivingClientFallBack.class)
public interface ReceivingClient {
    // 根据送货单号获取送货车信息
    @GetMapping({"/getDeliveryCarInfo/{deliveryNumber}"})
    ResultVO<DailyDeliveryDetailInfoDTO> getDeliveryCarInfo(@PathVariable String deliveryNumber);

    // 根据送货单号修改车辆状态
    @PostMapping({"/updateVehicleState"})
    ResultVO<Integer> updateVehicleState(@RequestBody VehicleStateUpdateDTO vehicleStateUpdateDTO);

    @PostMapping("/updateVehicleInspectState")
    @ApiOperation(value = "根据送货单号修改车辆检验状态")
    ResultVO<Integer> updateVehicleInspectState(@RequestBody VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO);


}
