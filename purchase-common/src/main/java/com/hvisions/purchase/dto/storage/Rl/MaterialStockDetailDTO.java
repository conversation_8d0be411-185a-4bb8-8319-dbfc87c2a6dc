package com.hvisions.purchase.dto.storage.Rl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料库存详情dto
 * @author: yyy
 * @time: 2022/9/8 13:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "原辅料库存情况dto")
public class MaterialStockDetailDTO {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "待收货数量")
    private BigDecimal waitReceiveWeight;

    @ApiModelProperty(value = "待过账数量")
    private BigDecimal waitPostingWeight;

    @ApiModelProperty(value = "sap库存数量")
    private BigDecimal sapWeight;


}
