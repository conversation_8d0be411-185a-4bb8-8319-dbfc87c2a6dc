package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 红高粱收货报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "红高粱收货报表dto")
public class RedSorghumReceiveSumDTO {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingWeight;

}
