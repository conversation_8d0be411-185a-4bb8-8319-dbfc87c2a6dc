package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 原辅料入库报表查询dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "原辅料入库报表查询dto")
public class InStorageReportQueryDTO extends PageInfo {

    @ApiModelProperty(value = "送货单类型：1-高粱、2-稻壳、3-小麦,逗号隔开")
    private String types;

    @ApiModelProperty(value = "物料编码集合，逗号隔开")
    private String materialCodes;

    @ApiModelProperty(value = "供应商编码集合，逗号隔开")
    private String vendorCodes;

    @ApiModelProperty(value = "送货计划号")
    private String planNumber;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;

    @ApiModelProperty(value = "过账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startPostDate;

    @ApiModelProperty(value = "过账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endPostDate;

    @ApiModelProperty(value = "送货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "送货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "入库仓号")
    private String warehouseCode;

    @ApiModelProperty(value = "是否扣重 0-否、1-是")
    private String isBuckle;


}
