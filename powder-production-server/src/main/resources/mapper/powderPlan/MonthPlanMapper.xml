<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.powder.productionPlan.mapper.MonthPlanMapper">

    <select id="getMonthPlanPageList" resultMap="monthPlanMap">
        SELECT p.id,CONCAT(p.`year`,"-",p.`month`) planMonth, p.begin_time,p.end_time,p.`status`,p.plan_no,
            MAX(CASE WHEN pd.product_type = 1 THEN pd.qf1_plan_quantity END) yjPlanCount,
            MAX(CASE WHEN pd.product_type = 1 THEN pd.qf2_plan_quantity END) fyjPlanCount
        FROM `t_wpp_month_plan` p
        LEFT JOIN t_wpp_month_plan_detail pd ON pd.month_plan_id = p.id AND pd.deleted = 0
        WHERE p.deleted = 0
        <if test="year != null and year != ''">
            AND p.`year`= #{year}
        </if>
        <if test="month != null and month != ''">
            AND p.`month`= #{month}
        </if>
        <if test="status != null and status != ''">
            AND p.`status`= #{status}
        </if>
        <if test="statusList != null and statusList != ''">
            AND FIND_IN_SET(p.`status`,#{statusList})
        </if>
        <if test="planDate != null">
            AND DATE_FORMAT(#{planDate}, '%Y-%m-%d') BETWEEN p.begin_time AND p.end_time
        </if>
        GROUP BY p.id
        <if test="startTime != null and endTime != null">
            HAVING planMonth BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY p.status ASC,p.id DESC
    </select>

    <resultMap id="monthPlanMap" type="com.hvisions.powder.dto.productionPlan.plan.month.MonthPlanPageDTO">
        <result property="id" column="id"></result>
        <result property="planMonth" column="planMonth"></result>
        <result property="beginTime" column="begin_time"></result>
        <result property="endTime" column="end_time"></result>
        <result property="yjPlanCount" column="yjPlanCount"></result>
        <result property="yjProductCount" column="yjProductCount"></result>
        <result property="fyjPlanCount" column="fyjPlanCount"></result>
        <result property="fyjProductCount" column="fyjProductCount"></result>
        <result property="status" column="status"></result>
        <collection property="weekPlanPageDTOList" column="id"
                    ofType="com.hvisions.powder.dto.productionPlan.plan.week.WeekPlanPageDTO"
                    javaType="java.util.ArrayList"
                    select="selectWeekList"
        >
        </collection>
    </resultMap>

    <select id="selectWeekList" resultType="com.hvisions.powder.dto.productionPlan.plan.week.WeekPlanPageDTO">
        SELECT p.id,p.begin_time,p.end_time,p.`status`,p.plan_no,
            MAX(CASE WHEN pd.material_type = 1 THEN pd.plan_count END) yjPlanCount,
            MAX(CASE WHEN pd.material_type = 2 THEN pd.plan_count END) fyjPlanCount,
            MAX(CASE WHEN ps.`week` = 1 THEN ps.schedule_type END) week1,
            MAX(CASE WHEN ps.`week` = 2 THEN ps.schedule_type END) week2,
            MAX(CASE WHEN ps.`week` = 3 THEN ps.schedule_type END) week3,
            MAX(CASE WHEN ps.`week` = 4 THEN ps.schedule_type END) week4,
            MAX(CASE WHEN ps.`week` = 5 THEN ps.schedule_type END) week5,
            MAX(CASE WHEN ps.`week` = 6 THEN ps.schedule_type END) week6,
            MAX(CASE WHEN ps.`week` = 0 THEN ps.schedule_type END) week7,
            p.rate,p.f_rate
        FROM `t_wpp_week_plan` p
        LEFT JOIN t_wpp_week_plan_detail pd ON p.id = pd.week_plan_id AND pd.deleted = 0
        LEFT JOIN t_wpp_week_plan_schedule ps ON p.id = ps.week_plan_id AND ps.deleted = 0 AND ps.floor_type = 2
        WHERE p.deleted = 0 and p.month_plan_id = #{id}
        AND ps.date BETWEEN DATE_FORMAT(p.begin_time,'%y-%m-%d') AND DATE_FORMAT(date_add(p.begin_time, interval 6-WEEKDAY(p.begin_time) day),'%y-%m-%d')
        GROUP BY p.id
    </select>

    <select id="getMaxMonthByYear" resultType="java.lang.String">
        SELECT p.`month`
        FROM t_wpp_month_plan p
        WHERE p.deleted = 0 AND p.`year` = 2024
        ORDER BY p.`month` DESC
        LIMIT 1
    </select>

    <select id="getMonthPlanValue" resultType="java.math.BigDecimal">
        SELECT
            MAX(CASE WHEN pd.product_type = 1 THEN
                <if test="materialType == 1 ">pd.qf1_plan_quantity * 1000</if>
                <if test="materialType == 2 ">pd.qf2_plan_quantity * 1000</if>
                END) PlanCount
        FROM `t_wpp_month_plan` p
        LEFT JOIN t_wpp_month_plan_detail pd ON pd.month_plan_id = p.id AND pd.deleted = 0
        WHERE p.deleted = 0 AND p.`status` = 3 AND p.`year` = #{year} AND p.`month` = #{month}
        GROUP BY p.id
    </select>

    <select id="getMonthPlanActualStartAndEndDate"
            resultType="com.hvisions.powder.dto.productionPlan.plan.month.MonthPlanActualStartAndEndDateDTO">
        SELECT MIN(wp.begin_time) begin_time, MAX(wp.end_time) end_time
        FROM t_wpp_month_plan mp
        LEFT JOIN t_wpp_week_plan wp ON wp.month_plan_id = mp.id AND wp.deleted = 0 AND wp.`status` = 3
        WHERE mp.deleted = 0 AND mp.`status` = 3 AND mp.`year`= #{year} AND mp.`month` = #{month}
    </select>
</mapper>
