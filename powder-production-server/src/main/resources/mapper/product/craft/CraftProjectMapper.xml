<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.powder.product.mapper.craft.CraftProjectMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.powder.product.entity.craft.TMkpCraftProject">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
            <result property="siteNum" column="site_num" jdbcType="VARCHAR"/>
            <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,creator_id,create_time,
        update_time,updater_id,deleted,
        site_num,project_code,project_name
    </sql>

    <resultMap id="getCraftProjectList" type="com.hvisions.powder.dto.product.craft.craftProject.CraftProjectPageDTO">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="BIT"/>
        <result property="siteNum" column="site_num" jdbcType="VARCHAR"/>
        <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <collection property="craftConfigurePageDTOList"
                    ofType="com.hvisions.powder.dto.product.craft.craftConfigure.CraftConfigurePageDTO"
                    javaType="java.util.List">
            <id property="id" column="c_id" jdbcType="INTEGER"/>
            <result property="projectId" column="projectId" jdbcType="INTEGER"/>
            <result property="configureCode" column="configure_code" jdbcType="VARCHAR"/>
            <result property="configureName" column="configure_name" jdbcType="VARCHAR"/>
            <result property="levelId" column="c_level_id" jdbcType="INTEGER"/>
            <result property="inputType" column="c_input_type" jdbcType="VARCHAR"/>
            <result property="upper" column="upper" />
            <result property="lower" column="lower" />
            <result property="disposeMeasure" column="c_dispose_measure" jdbcType="VARCHAR"/>
            <result property="remark" column="c_remark" jdbcType="VARCHAR"/>
            <result property="creatorId" column="c_creator_id" jdbcType="INTEGER"/>
            <result property="createTime" column="c_create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="c_update_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="c_updater_id" jdbcType="INTEGER"/>
            <result property="deleted" column="c_deleted" jdbcType="BIT"/>
            <result property="siteNum" column="c_site_num" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getCraftProjectList" resultMap="getCraftProjectList">
        SELECT cp.*,
               cc.id AS c_id,
               cc.project_id,
               cc.configure_code,
               cc.configure_name,
               cc.level_id AS c_level_id,
               cc.input_type AS c_input_type,
                cc.upper,cc.lower,
               cc.dispose_measure AS c_dispose_measure,
               cc.remark AS c_remark,
               cc.creator_id AS c_creator_id,
               cc.create_time AS c_create_time,
               cc.update_time AS c_update_time,
               cc.updater_id AS c_updater_id,
               cc.deleted AS c_deleted,
               cc.site_num AS c_site_num
        FROM t_mkp_craft_project cp
        LEFT JOIN t_mkp_craft_configure cc ON cc.project_id = cp.id AND cc.deleted = false
        WHERE cp.deleted = FALSE
        <if test="id != null">
            AND cp.id = #{id}
        </if>
        <if test="projectCode != null and projectCode != ''">
            AND cp.project_code = #{projectCode}
        </if>
        <if test="projectName != null and projectName != ''">
            AND cp.project_name = #{projectName}
        </if>
        ORDER BY cp.id DESC
    </select>
</mapper>
