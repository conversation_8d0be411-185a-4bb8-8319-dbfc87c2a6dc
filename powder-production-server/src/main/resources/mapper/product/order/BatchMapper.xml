<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.powder.product.mapper.order.BatchMapper">
<!--    根据筒仓批次获取压曲批次列表-->
    <select id="getExtrusionBatchByWarehouseBatch" resultType="com.hvisions.powder.dto.product.batch.BatchDTO">
        SELECT o.id,o.batch,3 type,o.actual_start_time batchDate
        FROM t_mkp_extrusion_order o
        WHERE o.deleted = 0 AND FIND_IN_SET(#{warehouseBatch},o.warehouse_batch)
    </select>
<!--    根据压曲批次找翻曲批次列表-->
    <select id="getFlipBatchByExtrusionBatch" resultType="com.hvisions.powder.dto.product.batch.BatchDTO">
        SELECT fo.id,fo.batch,4 type,fo.actual_start_time batchDate
        FROM t_mkp_flip_order fo
        WHERE fo.deleted = 0 AND FIND_IN_SET(#{extrusionBatch},fo.extrusion_batch)
    </select>
<!--    根据翻曲批次找粉碎批次列表-->
    <select id="getSmashBatchByFlipBatch" resultType="com.hvisions.powder.dto.product.batch.BatchDTO">
        SELECT o.id,o.batch,5 type,o.actual_start_time batchDate
        FROM t_mkp_smash_order o
        WHERE o.deleted = 0 AND FIND_IN_SET(#{flipBatch},o.flip_batch)
    </select>

    <select id="getWarehouseBatchByExtrusionBatch" resultType="com.hvisions.powder.dto.product.batch.BatchDTO">
        SELECT a.batch,wt.id,wt.start_time batchDate
        FROM (
            SELECT rtd.batch_no batch
            FROM t_mkp_runmai_task rt
            LEFT JOIN t_mkp_runmai_task_detail rtd ON rtd.task_id = rt.id AND rtd.deleted = 0
            WHERE rt.deleted = 0 AND  FIND_IN_SET(rt.batch,#{batch})
            GROUP BY rtd.batch_no
        )a
        LEFT JOIN t_mkp_warehouse_task wt ON wt.batch = a.batch AND wt.deleted = 0
        WHERE wt.id IS NOT NULL
    </select>

    <select id="getStoreroomAndFermentByExtrusionId" resultType="com.hvisions.powder.dto.product.batch.FermentBatchDTO">
        SELECT t.id,r.id storeroom_id,r.storeroom_no, t.task_no
        FROM t_mkp_extrusion_order o
        LEFT JOIN t_mkp_storeroom r ON FIND_IN_SET(r.id,o.ferment_ids) AND r.deleted = 0
        LEFT JOIN t_mkp_cultivate_ferment_task_order ft ON o.id = ft.order_id AND ft.type = 0 AND ft.deleted = 0
        LEFT JOIN t_mkp_cultivate_ferment_task t ON t.id = ft.task_id AND t.deleted = 0 AND t.storeroom_id = r.id
        WHERE o.deleted = 0 and o.id = #{extrusionId}
        order by t.id desc
    </select>

    <select id="getFermentStoreroomByFlipId" resultType="com.hvisions.powder.dto.product.batch.FermentBatchDTO">
        SELECT ft.task_id id, r.storeroom_no,r.id storeroom_id
        FROM t_mkp_flip_order fo
        LEFT JOIN t_mkp_storeroom r ON FIND_IN_SET(r.id,fo.ferment_ids) AND r.deleted = 0
        LEFT JOIN t_mkp_extrusion_order eo on FIND_IN_SET(eo.batch,fo.extrusion_batch)
        LEFT JOIN t_mkp_cultivate_ferment_task_order ft on ft.order_id = eo.id and ft.type = 0
        WHERE fo.deleted = 0 AND fo.id = #{flipId}
    </select>

    <select id="getStoreroomByFlipId" resultType="com.hvisions.powder.dto.product.batch.FermentBatchDTO">
        SELECT ft.task_id id,r.id storeroom_id,r.storeroom_no
        FROM t_mkp_flip_order o
        LEFT JOIN t_mkp_storeroom r ON FIND_IN_SET(r.id,o.storeroom_ids) AND r.deleted = 0
        LEFT JOIN t_mkp_cultivate_ferment_task_order ft ON o.id = ft.order_id AND ft.type = 1 AND ft.deleted = 0
        WHERE o.deleted = 0 AND o.id = #{flipId}
    </select>

    <select id="getStoreroomBySmashId" resultType="com.hvisions.powder.dto.product.batch.FermentBatchDTO">
        SELECT ft.task_id id,r.id storeroom_id,r.storeroom_no, t.task_no
        FROM t_mkp_smash_order o
        LEFT JOIN t_mkp_storeroom r ON FIND_IN_SET(r.id,o.storeroom_ids) AND r.deleted = 0
        LEFT JOIN t_mkp_flip_order fo on fo.batch = o.flip_batch
        LEFT JOIN t_mkp_cultivate_ferment_task_order ft ON fo.id = ft.order_id AND ft.type = 1 AND ft.deleted = 0
        LEFT JOIN t_mkp_invert_ferment_task t on t.id = ft.task_id and r.id = t.storeroom_id
        WHERE o.deleted = 0 AND o.id = #{flipId}
    </select>


    <select id="getWarehouseTaskBatchPageList" resultMap="warehouseTaskBatch">
        SELECT t.id,t.task_code,t.batch,t.warehouse_id,w.warehouse_code,t.material_id,t.material_name,t.material_code,t.storage_weight,t.wheat_number,t.start_time
        FROM t_mkp_warehouse_task t
        LEFT JOIN t_mkp_warehouse w ON w.id = t.warehouse_id AND w.deleted = 0
        WHERE t.deleted = 0 and t.state = 4
        <if test="batch != null and batch != ''">
            AND t.batch LIKE CONCAT('%',#{batch},'%')
        </if>
        <if test="warehouseId != null and warehouseId != ''">
            AND t.warehouse_id = #{warehouseId}
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(t.start_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
        <if test="batchList != null and batchList.size() > 0">
            AND t.batch IN
            <foreach collection="batchList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t.task_code
    </select>

    <resultMap id="warehouseTaskBatch" type="com.hvisions.powder.dto.product.batch.positive.WarehouseTaskBatchPageDTO">
        <id property="id" column="id"/>
        <result property="taskCode" column="task_code"/>
        <result property="batch" column="batch"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="materialId" column="material_id"/>
        <result property="materialName" column="material_name"/>
        <result property="materialCode" column="material_code"/>
        <result property="storageWeight" column="storage_weight"/>
        <result property="wheatNumber" column="wheat_number"/>
        <result property="startTime" column="start_time"/>
    </resultMap>

    <select id="getExtrusionRoomDetailBySmashBatch"
            resultType="com.hvisions.powder.dto.kanban.product.smashBatchCompart.SmashBatchCompareChildrenDTO">
        SELECT
             r.storeroom_no,eo.actual_start_time,eo.primer_input_rate,t.ferment_persistent_day
        FROM t_mkp_smash_order so
        LEFT JOIN t_mkp_flip_order fo ON FIND_IN_SET(fo.batch,so.flip_batch) AND fo.deleted = 0
        LEFT JOIN t_mkp_extrusion_order eo ON FIND_IN_SET(eo.batch,fo.extrusion_batch) AND eo.deleted = 0
        LEFT JOIN t_mkp_storeroom r ON FIND_IN_SET(r.id,eo.ferment_ids) AND r.deleted = 0
        LEFT JOIN t_mkp_cultivate_ferment_task_order ft ON eo.id = ft.order_id AND ft.type = 0 AND ft.deleted = 0
        LEFT JOIN t_mkp_cultivate_ferment_task t ON t.id = ft.task_id AND t.deleted = 0 AND t.storeroom_id = r.id
        WHERE eo.deleted = 0
        AND so.batch = #{smashBatch}
        ORDER BY r.storeroom_no DESC
    </select>

    <select id="getSmashBatchListByTime" resultType="java.lang.String">
        SELECT so.batch
        FROM t_mkp_smash_order so
        WHERE so.deleted = 0 AND so.smash_type = 0
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(so.actual_start_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>

        ORDER BY so.batch DESC
    </select>
</mapper>
