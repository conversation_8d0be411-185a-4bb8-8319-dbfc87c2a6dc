<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.powder.storage.mapper.StoreroomAlarmLogMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.powder.storage.entity.TMkpStoreroomAlarmLog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
            <result property="siteNum" column="site_num" jdbcType="VARCHAR"/>
            <result property="storeroomNo" column="storeroom_no" jdbcType="VARCHAR"/>
            <result property="equipType" column="equip_type" jdbcType="VARCHAR"/>
            <result property="equipCode" column="equip_code" jdbcType="VARCHAR"/>
            <result property="alarmName" column="alarm_name" jdbcType="VARCHAR"/>
            <result property="alarmContent" column="alarm_content" jdbcType="VARCHAR"/>
            <result property="recordTime" column="record_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,creator_id,create_time,
        update_time,updater_id,deleted,
        site_num,storeroom_no,equip_type,
        equip_code,alarm_name,alarm_content,
        record_time
    </sql>
</mapper>
