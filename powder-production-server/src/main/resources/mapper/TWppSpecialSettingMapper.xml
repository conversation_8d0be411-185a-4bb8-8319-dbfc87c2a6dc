<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.powder.productionPlan.mapper.TWppSpecialSettingMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.powder.productionPlan.entity.TWppSpecialSetting">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
            <result property="siteNum" column="site_num" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,creator_id,create_time,
        update_time,updater_id,site_num,
        deleted,year,month,
        status
    </sql>

    <resultMap id="SpecialSettingPageDTO" type="com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingPageDTO">
        <id property="id" column="id"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="siteNum" column="site_num" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="BIT"/>
        <result property="year" column="year" jdbcType="INTEGER"/>
        <result property="month" column="month" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <collection property="specialSettingDetailDTOList"
                    ofType="com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingDetailPageDTO"
                    column="id" select="getSpecialSettingDetail"
        />
    </resultMap>

    <select id="getSpecialSetting" resultMap="SpecialSettingPageDTO">
        SELECT
            ss.id,
            ss.`year`,
            ss.`month`,
            ss.`status`,
            ss.create_time,
            ss.update_time,
            ss.creator_id,
            ss.updater_id,
            ss.site_num,
            ss.deleted,
            ssd.date,
            ssd.duty_status
        FROM t_wpp_special_setting ss
        LEFT JOIN t_wpp_special_setting_detail ssd ON ssd.special_id = ss.id
        WHERE ss.status = '2'
        AND ss.deleted = FALSE
        AND ss.`year` = #{year}
        AND ss.`month` = #{month}
        LIMIT 1
    </select>

    <select id="getSpecialSettingPageList" resultMap="SpecialSettingPageDTO">
        SELECT
            ss.id,
            ss.`year`,
            ss.`month`,
            ss.`status`,
            ss.create_time,
            ss.update_time,
            ss.creator_id,
            ss.updater_id,
            ss.site_num,
            ss.deleted
        FROM t_wpp_special_setting ss
        WHERE ss.deleted = FALSE
        <if test="year != null and year >0">
            AND ss.`year` = #{year}
        </if>
        <if test="month != null and month > 0">
            AND ss.`month` = #{month}
        </if>
        <if test="status != null and status != ''">
            AND ss.status = #{status}
        </if>
        <if test="startTime != null and endTime != null">
            AND ss.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY ss.`status` ASC,ss.`year` DESC,ss.`month` DESC
    </select>

    <select id="getSpecialSettingDetail" resultType="com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingDetailPageDTO">
        SELECT * FROM t_wpp_special_setting_detail WHERE special_id = #{id}
    </select>
    <select id="selectDetailByYearAndMonth"
            resultType="com.hvisions.powder.productionPlan.entity.TWppSpecialSettingDetail">
        SELECT sd.* FROM t_wpp_special_setting s
        join t_wpp_special_setting_detail sd on s.id = sd.special_id
         WHERE s.deleted = FALSE and s.`year` = #{year}
            and s.`month` = #{month}
            and s.status = '2'
    </select>
</mapper>
