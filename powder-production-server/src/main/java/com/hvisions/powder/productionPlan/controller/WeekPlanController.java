package com.hvisions.powder.productionPlan.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.productionPlan.plan.week.*;
import com.hvisions.powder.productionPlan.service.WeekPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javassist.CannotCompileException;
import javassist.NotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/*
 * <AUTHOR>
 * @Description:制曲生产周计划
 * @date 2024/01/19 10:18
 */
@RestController
@RequestMapping(value = "/week/plan")
@Api(tags = "制曲生产周计划")
public class WeekPlanController {

    @Resource
    private WeekPlanService weekPlanService;

    @ApiOperation(value = "分页查询制曲生产周计划")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<WeekPlanPageDTO> getWeekPlanPageList(@RequestBody WeekPlanPageQueryDTO queryDTO) {
        return weekPlanService.getWeekPlanPageList(queryDTO);
    }

    @ApiOperation(value = "新增周度计划排班")
    @RequestMapping(value = "/schedule/save", method = RequestMethod.POST)
    public Integer saveWeekPlanSchedule(@RequestBody WeekPlanSaveDTO weekPlanSaveDTO) {
        return weekPlanService.saveWeekPlanSchedule(weekPlanSaveDTO);
    }

    @ApiOperation(value = "修改周度计划排班")
    @RequestMapping(value = "/schedule/update", method = RequestMethod.POST)
    public Integer updateWeekPlanSchedule(@RequestBody WeekPlanSaveDTO weekPlanSaveDTO) {
        return weekPlanService.updateWeekPlanSchedule(weekPlanSaveDTO);
    }

    @ApiOperation(value = "删除周度计划")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.GET)
    public Integer deleteWeekPlan(@PathVariable Integer id) {
        return weekPlanService.deleteWeekPlan(id);
    }

    @ApiOperation(value = "周度计划发起审批")
    @RequestMapping(value = "/audit/launch/{weekPlanId}", method = RequestMethod.GET)
    public Integer launchWeekPlanAudit(@PathVariable Integer weekPlanId) {
        return weekPlanService.launchWeekPlanAudit(weekPlanId);
    }

    @ApiOperation(value = "周计划状态变更")
    @RequestMapping(value = "/status/change/{status}/{id}", method = RequestMethod.POST)
    public Boolean updateWeekPlanStatus(@PathVariable String status, @PathVariable Integer id) {
        return weekPlanService.updateWeekPlanStatus(status, id);
    }


    @ApiOperation(value = "获取周度计划排班详情")
    @RequestMapping(value = "/schedule/detail/get/{weekPlanId}", method = RequestMethod.GET)
    public List<WeekPlanScheduleDTO> getWeekPlanScheduleDetail(@PathVariable Integer weekPlanId) {
        return weekPlanService.getWeekPlanScheduleDetail(weekPlanId);
    }

    @ApiOperation(value = "获取周度计划排班变更前后排班记录")
    @RequestMapping(value = "/schedule/change/record/get/{weekPlanId}", method = RequestMethod.GET)
    public WeekPlanScheduleChangeRecordDTO getWeekPlanScheduleDetailChangeRecord(@PathVariable Integer weekPlanId) {
        return weekPlanService.getWeekPlanScheduleDetailChangeRecord(weekPlanId);
    }

    @ApiOperation(value = "下载周计划导入模板")
    @EnableFilter
    @ApiResultIgnore
    @RequestMapping(value = "/template/{id}" , method = RequestMethod.POST)
    public ResultVO<ExcelExportDto> getImportTemplate(@PathVariable Integer id) throws NoSuchFieldException, IllegalAccessException, IOException, NotFoundException, CannotCompileException, InstantiationException {
        return weekPlanService.getImportTemplate(id);
    }

    @EnableFilter
    @ApiOperation(value = "批量导入周计划")
    @RequestMapping(value = "/import/{id}", method = RequestMethod.POST)
    public ImportResult importWeekPlan(@RequestParam("file") MultipartFile file,@PathVariable Integer id) throws IllegalAccessException, ParseException, IOException, NotFoundException, CannotCompileException, NoSuchFieldException, InstantiationException {
        return weekPlanService.importWeekPlan(file,id);
    }

    @ApiOperation(value = "根据周计划id查询人员排班")
    @RequestMapping(value = "/WeekPlanPerSchedule/get/{id}", method = RequestMethod.POST)
    public List<WeekPlanPerSchedulePageDTO> getWeekPlanPerSchedule(@PathVariable Integer id){
        return weekPlanService.getWeekPlanPerSchedule(id);
    }

    @ApiOperation(value = "查询生效的周计划 -- 根据当前查询时间")
    @RequestMapping(value = "/getAvailableWeekPlan/get/{date}", method = RequestMethod.POST)
    public List<WeekPlanPageDTO> getAvailableWeekPlan(){
        return weekPlanService.getAvailableWeekPlan();
    }
}
