package com.hvisions.powder.productionPlan.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.productionPlan.*;
import com.hvisions.powder.dto.productionPlan.plan.year.YearPlanDTO;
import com.hvisions.powder.dto.qudou.demand.DemandForYear;
import com.hvisions.powder.productionPlan.entity.TWppYearRequirement;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_wpp_year_requirement(年度大曲需求计划)】的数据库操作Service
* @createDate 2024-01-18 09:29:48
*/
public interface TWppYearRequirementService extends IService<TWppYearRequirement> {

    /**
     * @描述: 新增年计划需求
     * @作者: 刘文勇
     * @日期: 2024/1/31 10:53
     * @参数: yearRequirementDTO
     * @返回值: java.lang.Boolean
     */
    Boolean addYearRequirement(YearRequirementDTO yearRequirementDTO);

    /**
      * @描述: 新增年计划需求
      * @作者: 刘文勇
      * @日期: 2024/1/31 10:53
      * @参数: yearRequirementDTO
      * @返回值: java.lang.Boolean
    */
    Boolean addYearRequirement(List<YearRequirementDTO> yearRequirementDTO);

    /** 下载导入模板
      * @描述:
      * @作者: 刘文勇
      * @日期: 2024/1/31 10:54
      * @返回值: com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
    */
    ResultVO<ExcelExportDto> getImportTemplate() throws NoSuchFieldException, IllegalAccessException, IOException;

    /**
      * @描述: 批量导入年计划需求
      * @作者: 刘文勇
      * @日期: 2024/1/31 10:54
      * @参数: file
      * @返回值: com.hvisions.common.interfaces.ImportResult
    */
    ImportResult importYearRequirement(MultipartFile file) throws IOException, IllegalAccessException;

    /**
      * @描述: 年需求查询接口
      * @作者: 刘文勇
      * @日期: 2024/2/20 11:24
      * @参数: yearRequirementQueryDTOList
      * @返回值: java.util.List<com.hvisions.powder.dto.productionPlan.YearRequirementPageDTO>
    */
    Page<YearRequirementPageDTO> getYearRequirement(YearRequirementQueryDTO yearRequirementQueryDTOList);

    List<YearRequirementDetailPageDTO> getYearRequirementDetail(Integer id);
}
