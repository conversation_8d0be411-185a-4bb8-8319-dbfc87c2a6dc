package com.hvisions.powder.productionPlan.controller;

import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingDTO;
import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingPageDTO;
import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingQueryDTO;
import com.hvisions.powder.productionPlan.service.TWppSpecialSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * @描述: 特殊月份配置控制器
 * @作者: 刘文勇
 * @日期: 2024/1/15 15:22
 * @版本 1.0
 */
@RestController
@RequestMapping("/special/setting")
@Api(tags = "特殊月份配置控制器")
@Slf4j
public class SpecialSettingController {

    @Autowired
    private TWppSpecialSettingService tWppSpecialSettingService;

    @ApiOperation(value = "分页查询查询特殊月份配置")
    @RequestMapping(value = "/page/list/get" , method = RequestMethod.POST)
    public Page<SpecialSettingPageDTO> getSpecialSettingPageList(@RequestBody SpecialSettingQueryDTO specialSettingQueryDTO){
        return tWppSpecialSettingService.getSpecialSettingPageList(specialSettingQueryDTO);
    }

    @ApiOperation(value = "根据年月查询特殊月份配置")
    @RequestMapping(value = "/get" , method = RequestMethod.POST)
    public SpecialSettingPageDTO getSpecialSetting(@RequestBody SpecialSettingQueryDTO specialSettingQueryDTO){
        return tWppSpecialSettingService.getSpecialSetting(specialSettingQueryDTO);
    }

    @ApiOperation(value = "新增特殊月份配置")
    @RequestMapping(value = "/add" , method = RequestMethod.POST)
    public Boolean addSpecialSetting(@RequestBody SpecialSettingDTO specialSettingDTO){
        return tWppSpecialSettingService.addSpecialSetting(specialSettingDTO);
    }

    @ApiOperation(value = "修改特殊月份配置")
    @RequestMapping(value = "/update" , method = RequestMethod.POST)
    public Boolean updateSpecialSetting(@RequestBody SpecialSettingDTO specialSettingDTO){
        return tWppSpecialSettingService.updateSpecialSetting(specialSettingDTO);
    }

    @ApiOperation(value = "删除特殊月份配置")
    @RequestMapping(value = "/delete/{id}" , method = RequestMethod.GET)
    public Boolean deleteSpecialSetting(@PathVariable Integer id){
        return tWppSpecialSettingService.deleteSpecialSetting(id);
    }
}
