package com.hvisions.powder.productionPlan.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.productionPlan.workSetting.WorkSettingDTO;
import com.hvisions.powder.dto.productionPlan.workSetting.WorkSettingPageDTO;
import com.hvisions.powder.dto.productionPlan.workSetting.WorkSettingQueryDTO;
import com.hvisions.powder.productionPlan.entity.TWppWorkSetting;
import com.hvisions.powder.productionPlan.mapper.TWppWorkSettingMapper;
import com.hvisions.powder.productionPlan.service.TWppWorkSettingService;
import com.hvisions.powder.utils.CopyUtil;
import com.hvisions.powder.utils.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_wpp_work_setting(加班/开班配置)】的数据库操作Service实现
 * @createDate 2024-01-17 13:53:38
 */
@Service
public class TWppWorkSettingServiceImpl extends ServiceImpl<TWppWorkSettingMapper, TWppWorkSetting> implements TWppWorkSettingService {

    @Resource
    private UserClient userClient;

    @Override
    public Boolean addWorkSetting(WorkSettingDTO workSettingDTO) {
        TWppWorkSetting tWppWorkSetting = CopyUtil.simpleCopy(workSettingDTO, TWppWorkSetting.class);
        tWppWorkSetting.setStatus("1");
        return baseMapper.insert(tWppWorkSetting) > 0;
    }

    @Override
    public Boolean updateWorkSetting(WorkSettingDTO workSettingDTO) {
        TWppWorkSetting newTWppWorkSetting = CopyUtil.simpleCopy(workSettingDTO, TWppWorkSetting.class);
        if ("2".equals(newTWppWorkSetting.getStatus())) {
            TWppWorkSetting tWppWorkSetting = baseMapper.selectById(workSettingDTO.getId());
            List<TWppWorkSetting> tWppWorkSettings = baseMapper.selectList(new LambdaUpdateWrapper<TWppWorkSetting>()
                    .eq(TWppWorkSetting::getStatus, "2")
                    .eq(TWppWorkSetting::getDeleted, false)
                    .eq(TWppWorkSetting::getMargin, tWppWorkSetting.getMargin())
                    .eq(TWppWorkSetting::getPriority, tWppWorkSetting.getPriority()));
            if (CollectionUtils.isNotEmpty(tWppWorkSettings)) {
                tWppWorkSettings.forEach(oldWorkSetting -> {
                    oldWorkSetting.setStatus("3");
                    baseMapper.updateById(oldWorkSetting);
                });
            }
        }
        return baseMapper.updateById(newTWppWorkSetting) > 0;
    }

    @Override
    public Boolean deleteWorkSetting(Integer id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public Page<WorkSettingPageDTO> getWorkSettingPageList(WorkSettingQueryDTO workSettingQueryDTO) {
        Page<WorkSettingPageDTO> page = PageHelperUtil.getPage(baseMapper::getWorkSettingPageList, workSettingQueryDTO, WorkSettingPageDTO.class);
        for (WorkSettingPageDTO workSettingPageDTO : page.getContent()) {
            ResultVO<UserDTO> resultVO = userClient.getUser(workSettingPageDTO.getCreatorId());
            if (resultVO.getCode() == 200 && StringUtil.isNotEmpty(resultVO.getData())) {
                workSettingPageDTO.setCreateName(resultVO.getData().getUserName());
            }
        }
        return page;
    }

    @Override
    public WorkSettingPageDTO getEffectWorkSetting() {
        TWppWorkSetting workSetting = baseMapper.selectOne(Wrappers.<TWppWorkSetting>lambdaQuery()
                .eq(TWppWorkSetting::getStatus, "2")
                .orderByDesc(TWppWorkSetting::getCreateTime)
                .last("limit 1"));
        return DtoMapper.convert(workSetting, WorkSettingPageDTO.class);
    }
}




