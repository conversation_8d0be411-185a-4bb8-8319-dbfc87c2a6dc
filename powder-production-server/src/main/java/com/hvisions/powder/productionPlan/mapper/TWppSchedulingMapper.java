package com.hvisions.powder.productionPlan.mapper;

import com.hvisions.powder.dto.productionPlan.scheduling.SchedulingDTO;
import com.hvisions.powder.dto.productionPlan.scheduling.SchedulingPageDTO;
import com.hvisions.powder.dto.productionPlan.scheduling.SchedulingQueryDTO;
import com.hvisions.powder.productionPlan.entity.TWppScheduling;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_wpp_scheduling(工艺员排班配置)】的数据库操作Mapper
* @createDate 2024-01-15 16:22:49
* @Entity com.hvisions.powder.productionPlan.entity.TWppScheduling
*/
@Mapper
public interface TWppSchedulingMapper extends BaseMapper<TWppScheduling> {

    List<SchedulingPageDTO> getSchedulingList(SchedulingQueryDTO schedulingQueryDTO);
}




