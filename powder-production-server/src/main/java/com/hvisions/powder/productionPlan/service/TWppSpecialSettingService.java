package com.hvisions.powder.productionPlan.service;

import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingDTO;
import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingPageDTO;
import com.hvisions.powder.dto.productionPlan.specialSetting.SpecialSettingQueryDTO;
import com.hvisions.powder.productionPlan.entity.TWppSpecialSetting;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Page;

/**
* <AUTHOR>
* @description 针对表【t_wpp_special_setting(特殊月份生产时间配置)】的数据库操作Service
* @createDate 2024-01-17 14:47:18
*/
public interface TWppSpecialSettingService extends IService<TWppSpecialSetting> {

    Boolean addSpecialSetting(SpecialSettingDTO specialSettingDTO);

    Boolean updateSpecialSetting(SpecialSettingDTO specialSettingDTO);

    Boolean deleteSpecialSetting(Integer id);

    SpecialSettingPageDTO getSpecialSetting(SpecialSettingQueryDTO specialSettingQueryDTO);

    Page<SpecialSettingPageDTO> getSpecialSettingPageList(SpecialSettingQueryDTO specialSettingQueryDTO);
}
