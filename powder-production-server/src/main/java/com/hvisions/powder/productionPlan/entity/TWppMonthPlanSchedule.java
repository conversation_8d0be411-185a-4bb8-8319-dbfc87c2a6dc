package com.hvisions.powder.productionPlan.entity;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 月度大曲生产计划排班
 * @date 2024/1/18 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_wpp_month_plan_schedule")
public class TWppMonthPlanSchedule extends SysBase {

    /** 月计划ID */
    private Integer monthPlanId;

    /** 日期 */
    private Date date;

    /** 工作类型：1:单班压曲-双班翻曲、2:双班压曲-单班翻曲、3：休息（整天休息）、4:单班压曲-翻曲休息、
     * 5:压曲休息-单班翻曲、6:单班压曲-单班翻曲、7:双班压曲-双班翻曲、8:压曲休息-双班翻曲、9:双班压曲-翻曲休息
     */
    private String scheduleType;

}
