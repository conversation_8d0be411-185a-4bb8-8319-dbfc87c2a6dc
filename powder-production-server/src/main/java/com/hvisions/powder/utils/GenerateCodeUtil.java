package com.hvisions.powder.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-04-07 13:27
 */
@Component
public class GenerateCodeUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * @Description 根据传入的前缀获取redis内的序列号
     *
     * <AUTHOR>
     * @Date 2024-4-8 11:16
     * @param prefix 前缀
     * @return java.lang.String  前缀+yyyyMMdd+001
     **/
    public String generatePlanCode(String prefix) {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = LocalDate.now().format(formatters);

        String cacheKey = prefix + dateString;
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (Objects.nonNull(absent) && absent) {
            return cacheKey + "001";
        }
        //创建失败  自增
        Long afterIncrease = redisTemplate.opsForValue().increment(cacheKey, 1);
        return cacheKey + String.format("%03d", afterIncrease);
    }

    /**
     * @Des 交酒任务709 713 710 ZL2和DZ2 的流水号
     * <AUTHOR>
     * @Date 2022/8/2 19:47:46
     * @Param
     * @Return
     */
    public String generatePlanCodeToHandinFir() {
        Boolean absent = redisTemplate.opsForValue().setIfAbsent("HandinFir", "450001");
        if (Objects.nonNull(absent) && absent) {
            return "450001";
        }
        //创建失败  自增
        Long afterIncrease = redisTemplate.opsForValue().increment("HandinFir", 1);
        return String.format("%03d", afterIncrease);
    }

    /**
     * @Des 交酒任务其他情况的流水号
     * <AUTHOR>
     * @Date 2022/8/2 19:55:10
     * @Param
     * @Return
     */
    public String generatePlanCodeToHandinSec() {
        Boolean absent = redisTemplate.opsForValue().setIfAbsent("HandinSec", "250001");
        if (Objects.nonNull(absent) && absent) {
            return "250001";
        }
        //创建失败  自增
        Long afterIncrease = redisTemplate.opsForValue().increment("HandinSec", 1);
        return String.format("%03d", afterIncrease);
    }

    /***
     * @Description 检验模块任务号生成
     *
     * <AUTHOR>
     * @Date 2023-9-26 8:34
     * @param prefix 前缀
     * @param len 流水号长度
     * @return java.lang.String
     **/
    public String generateInspectionCode(String prefix, int len) {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyMMdd");
        String dateString = LocalDate.now().format(formatters);

        String cacheKey = prefix + dateString;
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (Objects.nonNull(absent) && absent) {
            if (len == 6) {
                return cacheKey + "000001";
            } else if (len == 4) {
                return cacheKey + "0001";

            } else if (len == 3) {
                return cacheKey + "001";
            } else {
                return cacheKey + "01";
            }
        }
        //创建失败  自增
        Long afterIncrease = redisTemplate.opsForValue().increment(cacheKey, 1);
        String lenFormat = "%0" + len + "d";
        return cacheKey + String.format(lenFormat, afterIncrease);
    }

    /***
     * @Description 来料样品编码流水号
     *
     * <AUTHOR>
     * @Date 2023-9-26 8:50
     * @param materialType 物料类型
     * @param planSamplingTime 计划取样时间
     * @return java.lang.String
     **/
    public String generateLLSerialNumber(String materialType, Date planSamplingTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateString = sdf.format(planSamplingTime);

        String cacheKey = materialType + dateString;
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (Objects.nonNull(absent) && absent) {
            return "0001";

        }
        //创建失败  自增
        Long afterIncrease = redisTemplate.opsForValue().increment(cacheKey, 1);
        return String.format("%04d", afterIncrease);
    }

    /**
     * 重置流水码
     *
     * @param handinSecStr 25开头 250000
     * @param handinfirStr 45开头 450000
     */
    public void resetHandinSerialNumber(String handinSecStr, String handinfirStr) {
        Boolean handinSec = redisTemplate.opsForValue().setIfAbsent("HandinSec", handinSecStr);
        if (Boolean.FALSE.equals(handinSec)) {
            redisTemplate.opsForValue().set("HandinSec", handinSecStr);
        }

        Boolean absent = redisTemplate.opsForValue().setIfAbsent("HandinFir", handinfirStr);
        if (Boolean.FALSE.equals(absent)) {
            redisTemplate.opsForValue().set("HandinFir", handinfirStr);
        }
    }

    /**
     * 设置是否正在sap批量同步锁(传1和0)
     */
    public void setIsBatchSap(String value) {
        Boolean bool = redisTemplate.opsForValue().setIfAbsent("IsBatchSap", value);
        if (Boolean.FALSE.equals(bool)) {
            redisTemplate.opsForValue().set("IsBatchSap", value);
        }
    }

    public String getIsBatchSap() {
        String isBatchSap = redisTemplate.opsForValue().get("IsBatchSap");
        if (isBatchSap == null) {
            setIsBatchSap("0");
            return "0";
        }
        return isBatchSap;
    }

}
