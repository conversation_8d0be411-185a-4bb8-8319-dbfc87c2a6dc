package com.hvisions.powder.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ThreadPoolExecutorFactory implements ApplicationContextAware {

    @Autowired
    private static ThreadPoolExecutorFactory executorFactory;

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext arg0) throws BeansException {
        applicationContext = arg0;
    }

    @PostConstruct
    public void init(){
        executorFactory = this;
    }

    public static ThreadPoolExecutor threadPoolExecutor;

    private ThreadPoolExecutorFactory(){

    }

    /**
     * 获取线程池实例,使用双重检查锁来创建单例实例
     * @return 线程池实例
     */
    public static ThreadPoolExecutor getInstance(){
        if(threadPoolExecutor == null){
            synchronized (ThreadPoolExecutorFactory.class){
                if (threadPoolExecutor == null) {
                    TaskConfig taskConfig = applicationContext.getBean(TaskConfig.class);
                    threadPoolExecutor = new ThreadPoolExecutor(
                            // 核心线程数
                            8,
                            // 最大核心线程 = 当前运行的服务器的最大可用进程数
                            Runtime.getRuntime().availableProcessors(),
                            // 线程最大等待释放时间60s
                            60L,
                            // 单位
                            TimeUnit.SECONDS,
                            // 阻塞队列
                            new ArrayBlockingQueue<Runnable>(20),
                            // 使用默认的线程工厂
                            Executors.defaultThreadFactory(),
                            // 拒绝策略DiscardOldestPolicy,表示当线程池资源全部被占满时,新的任务会尝试竞争线程
                            // 若竞争失败,则丢弃任务,并不会抛出异常
                            new ThreadPoolExecutor.DiscardOldestPolicy()
                    );
                }
            }
        }
        return threadPoolExecutor;

    }

}
