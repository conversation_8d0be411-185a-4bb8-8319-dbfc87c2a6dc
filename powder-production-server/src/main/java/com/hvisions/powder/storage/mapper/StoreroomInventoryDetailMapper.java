package com.hvisions.powder.storage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.powder.dto.storage.storeroom.inventory.detail.StoreroomInventoryDetailPageDTO;
import com.hvisions.powder.dto.storage.storeroom.inventory.detail.StoreroomInventoryDetailPageQueryDTO;
import com.hvisions.powder.storage.entity.TMkpStoreroomInventoryDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <AUTHOR>
 * @description 库存盘点详情
 * @date 2024/1/18 10:21
 */
@Mapper
public interface StoreroomInventoryDetailMapper extends BaseMapper<TMkpStoreroomInventoryDetail> {

    List<StoreroomInventoryDetailPageDTO> getStoreroomInventoryDetailPageList(StoreroomInventoryDetailPageQueryDTO queryDTO);

}
