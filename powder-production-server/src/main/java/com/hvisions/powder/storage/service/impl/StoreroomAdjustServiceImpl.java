package com.hvisions.powder.storage.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.dto.storage.storeroom.adjust.StoreroomAdjustPageDTO;
import com.hvisions.powder.dto.storage.storeroom.adjust.StoreroomAdjustPageQueryDTO;
import com.hvisions.powder.dto.storage.storeroom.adjust.StoreroomAdjustUpdateDTO;
import com.hvisions.powder.product.entity.process.TMkpShelvesAllocateOrder;
import com.hvisions.powder.sap.dto.wms.WmsUpdateStoreroomDto;
import com.hvisions.powder.sap.service.WmsService;
import com.hvisions.powder.storage.entity.TMkpStoreroom;
import com.hvisions.powder.storage.entity.TMkpStoreroomAdjustRecord;
import com.hvisions.powder.storage.entity.TMkpStoreroomLocation;
import com.hvisions.powder.storage.mapper.SerialNumberMapper;
import com.hvisions.powder.storage.mapper.StoreroomAdjustRecordMapper;
import com.hvisions.powder.storage.mapper.StoreroomLocationMapper;
import com.hvisions.powder.storage.mapper.StoreroomMapper;
import com.hvisions.powder.storage.service.StoreroomAdjustService;
import com.hvisions.powder.utils.CopyUtil;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 库房温湿度记录
 * @date 2024/01/19 10:18
 */
@Service
@Slf4j
public class StoreroomAdjustServiceImpl implements StoreroomAdjustService {
    @Resource
    private StoreroomAdjustRecordMapper storeroomAdjustRecordMapper;

    @Resource
    private SerialNumberMapper serialNumberMapper;

    @Resource
    private StoreroomLocationMapper storeroomLocationMapper;

    @Resource
    private StoreroomMapper storeroomMapper;

    @Resource
    private WmsService wmsService;


    @Override
    public Page<StoreroomAdjustPageDTO> getStoreroomAdjustPageList(StoreroomAdjustPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(storeroomAdjustRecordMapper::getStoreroomCleanPageList, queryDTO, StoreroomAdjustPageDTO.class);
    }

    @Override
    public Integer addOrUpdateStoreroomAdjust(StoreroomAdjustUpdateDTO storeroomUpdateDTO) {

        TMkpStoreroomAdjustRecord tMkpStoreroomAdjustRecord = new TMkpStoreroomAdjustRecord();
        tMkpStoreroomAdjustRecord.setStoreroomId(storeroomUpdateDTO.getStoreroomId());
        tMkpStoreroomAdjustRecord.setStoreroomCode(storeroomUpdateDTO.getStoreroomCode());
        String number = serialNumberMapper.getSerialNumber("t_mkp_storeroom_adjust_record", 2, true);
        tMkpStoreroomAdjustRecord.setTaskNo("KWTZ" + DateUtil.dateFormat(new Date(), "yyMMdd") + number);
        //获取操作前的库位情况
        List<TMkpStoreroomLocation> locationList = storeroomLocationMapper.selectList(new LambdaUpdateWrapper<TMkpStoreroomLocation>().eq(TMkpStoreroomLocation::getStoreroomId, storeroomUpdateDTO.getStoreroomId()).eq(TMkpStoreroomLocation::getDeleted, false));

        //对操作的库位数据进行汇总
        List<Integer> inList = new ArrayList<>();
        List<Integer> left = storeroomUpdateDTO.getLeftLocations();
        List<Integer> right = storeroomUpdateDTO.getRightLocations();
        //将选择的号转换成实际的库位号
        if(CollectionUtils.isNotEmpty(left)){
            List<Integer> leftIds = locationList.stream().filter(location -> 1 == location.getStoreColumn() && left.contains(location.getStoreRow())).map(TMkpStoreroomLocation::getId).collect(Collectors.toList());
            inList.addAll(leftIds);
            tMkpStoreroomAdjustRecord.setLeftLocation(left.get(0) + "~" + left.get(left.size() -1));
        }
        if(CollectionUtils.isNotEmpty(right)){
            List<Integer> rightIds = locationList.stream().filter(location -> 2 == location.getStoreColumn() && right.contains(location.getStoreRow())).map(TMkpStoreroomLocation::getId).collect(Collectors.toList());
            inList.addAll(rightIds);
            tMkpStoreroomAdjustRecord.setRightLocation(right.get(0) + "~" + right.get(right.size() -1));
        }
        WmsUpdateStoreroomDto wmsDto = new WmsUpdateStoreroomDto();
        List<String> wmsUpdateNos = locationList.stream().filter(location -> inList.contains(location.getId())).map(TMkpStoreroomLocation::getLocationCode).collect(Collectors.toList());
        wmsDto.setStatus("1");
        wmsDto.setPltNum("1");
        wmsDto.setWareHouse(storeroomUpdateDTO.getStoreroomCode());
        TMkpStoreroom tMkpStoreroom = storeroomMapper.selectById(storeroomUpdateDTO.getStoreroomId());
        if("库房".equals(tMkpStoreroom.getStoreroomType())){
            wmsDto.setItemNum("实曲框");
        }else{
            wmsDto.setItemNum("实曲架");
        }
        wmsDto.setLocNums(wmsUpdateNos);
        //库位更改信息发送给wms
        wmsService.updateStoreroomAndLocation(wmsDto);
        //更改库位库存情况
        List<Integer> outList = locationList.stream().filter(location -> !inList.contains(location.getId())).map(TMkpStoreroomLocation::getId).collect(Collectors.toList());
        TMkpStoreroomLocation update = new TMkpStoreroomLocation();
        if(CollectionUtils.isNotEmpty(inList)){
            update.setIsVacant("0");
            storeroomLocationMapper.update(update, new LambdaUpdateWrapper<TMkpStoreroomLocation>().in(TMkpStoreroomLocation::getId, inList));
        }
        if(CollectionUtils.isNotEmpty(outList)) {
            update.setNum(0);
            update.setIsVacant("1");
            storeroomLocationMapper.update(update, new LambdaUpdateWrapper<TMkpStoreroomLocation>().in(TMkpStoreroomLocation::getId, outList));

        }
        TMkpStoreroom storeRoomUpdate = new TMkpStoreroom();
        storeRoomUpdate.setId(storeroomUpdateDTO.getStoreroomId());
        storeRoomUpdate.setVolumeUse(inList.size());
        storeroomMapper.updateById(storeRoomUpdate);
        return storeroomAdjustRecordMapper.insert(tMkpStoreroomAdjustRecord);
    }

    @Override
    public Integer deleteStoreroomAdjust(Integer id) {
        return storeroomAdjustRecordMapper.deleteById(id);
    }

}
