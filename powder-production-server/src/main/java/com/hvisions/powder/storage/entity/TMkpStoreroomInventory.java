package com.hvisions.powder.storage.entity;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 库房盘点计划
 * @date 2024/1/18 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mkp_storeroom_inventory")
public class TMkpStoreroomInventory extends SysBase {

    /** 库房类型 */
    private String storeroomType;

    /** 盘点单号 */
    private String inventoryNo;

    /** 库房开始编号 */
    private String storeroomStart;

    /** 库房截止编号 */
    private String storeroomEnd;

    /** 执行时间 */
    private Date executeTime;

    /** 是否同步WMS */
    private String synWms;

    /** 盘点时间 */
    private Date inventoryTime;

    /** 盘点人 */
    private String inventoryUser;

    /** 状态:0-未盘点、1-盘点中、2-盘点完成 */
    private String state;
}
