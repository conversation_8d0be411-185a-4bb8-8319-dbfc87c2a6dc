package com.hvisions.powder.storage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.powder.dto.storage.storeroom.temperature.StoreroomTemperatureComparisonDTO;
import com.hvisions.powder.dto.storage.storeroom.temperature.StoreroomTemperatureLogDTO;
import com.hvisions.powder.dto.storage.storeroom.temperature.StoreroomTemperaturePageDTO;
import com.hvisions.powder.dto.storage.storeroom.temperature.StoreroomTemperaturePageQueryDTO;
import com.hvisions.powder.storage.entity.TMkpStoreroomTemperatureRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <AUTHOR>
 * @description 库房温湿度记录
 * @date 2024/1/18 10:21
 */
@Mapper
public interface StoreroomTemperatureRecordMapper extends BaseMapper<TMkpStoreroomTemperatureRecord> {

    List<StoreroomTemperatureLogDTO> getStoreroomTemperatureLoList(String taskNo);

    List<StoreroomTemperaturePageDTO> selectPageList(StoreroomTemperaturePageQueryDTO storeroomTemperaturePageQueryDTO);

    /**
     * 查询温湿度对比记录
     * @return
     */
    List<StoreroomTemperatureComparisonDTO> selectComparisonList(StoreroomTemperaturePageQueryDTO queryDTO);
}
