package com.hvisions.powder.storage.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 库房库位
 * @date 2024/1/18 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mkp_storeroom_location")
public class TMkpStoreroomLocation extends SysBase {

    /** 库房id */
    private Integer storeroomId;

    /** 库位号 */
    private String locationCode;

    /** 位置描述 */
    private String description;

    /** 排 */
    private Integer storeRow;

    /** 列 */
    private Integer storeColumn;

    /** 物料id */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer materialId;

    /** 物料编号 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String materialCode;

    /** 物料名称 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String materialName;

    /** 库存 */
    private Integer num;

    /** 批次 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String batch;

    /** 备注 */
    private String remark;

    /** 是否空仓：1-空仓、0-非空仓 */
    private String isVacant;

    /** 库存重量 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal weight;
}
