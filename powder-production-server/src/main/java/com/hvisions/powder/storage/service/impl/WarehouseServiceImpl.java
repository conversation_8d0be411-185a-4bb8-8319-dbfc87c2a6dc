package com.hvisions.powder.storage.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.advice.UserAuditorAware;
import com.hvisions.powder.dto.storage.warehouse.*;
import com.hvisions.powder.dto.storage.warehouse.data.WarehouseBarnDataInsertDTO;
import com.hvisions.powder.sap.dto.wincos.WincosBaseDataDto;
import com.hvisions.powder.sap.dto.wincos.WincosBaseResponseDto;
import com.hvisions.powder.sap.dto.wincos.WincosWarehouseDto;
import com.hvisions.powder.sap.dto.zongheng.ZhUpdateWarehouseHighDosageDto;
import com.hvisions.powder.sap.service.WincosService;
import com.hvisions.powder.storage.constant.StorageVacantStateConstant;
import com.hvisions.powder.storage.constant.WarehouseTypeConstant;
import com.hvisions.powder.storage.entity.TMkpWarehouse;
import com.hvisions.powder.storage.entity.TMkpWarehouseData;
import com.hvisions.powder.storage.mapper.WarehouseDataMapper;
import com.hvisions.powder.storage.mapper.WarehouseMapper;
import com.hvisions.powder.storage.service.WarehouseService;
import com.hvisions.powder.utils.ChineseCharacterUtil;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.hvisions.powder.consts.CommonConsts.LOGIN_HINT;

/**
 * <AUTHOR>
 * @Description: 仓库管理
 * @date 2024/01/19 10:18
 */
@Service
@Slf4j
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, TMkpWarehouse> implements WarehouseService {
    @Resource
    private WarehouseMapper warehouseMapper;

    @Resource
    private WarehouseDataMapper warehouseDataMapper;

    @Resource
    private WincosService wincosService;

    @Autowired
    UserAuditorAware userAuditorAware;

    /**
     * @Description 分页查询仓库主数据
     *
     * <AUTHOR>
     * @Date 2024-5-15 11:47
     * @param queryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.powder.dto.storage.warehouse.WarehousePageDTO>
     **/
    @Override
    public Page<WarehousePageDTO> getWarehousePageList(WarehousePageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(warehouseMapper::getWarehousePageList, queryDTO, WarehousePageDTO.class);
    }

    @Override
    public List<WarehousePageDTO> getWarehouseList(WarehousePageQueryDTO queryDTO) {
        List<TMkpWarehouse> warehouseList = warehouseMapper.selectList(Wrappers.<TMkpWarehouse>lambdaQuery()
                .eq(StringUtil.isNotEmpty(queryDTO.getWarehouseType()), TMkpWarehouse::getWarehouseType, queryDTO.getWarehouseType())
                .eq(StringUtil.isNotEmpty(queryDTO.getWarehouseCode()), TMkpWarehouse::getWarehouseCode, queryDTO.getWarehouseCode())
                .eq(StringUtil.isNotEmpty(queryDTO.getFloorId()), TMkpWarehouse::getFloorId, queryDTO.getFloorId())
        );
        return DtoMapper.convertList(warehouseList, WarehousePageDTO.class);
    }

    /**
     * @Description 新增修改仓库数据
     *
     * <AUTHOR>
     * @Date 2024-5-15 11:48
     * @param warehouseUpdateDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer addOrUpdateWarehouse(WarehouseUpdateDTO warehouseUpdateDTO) {
        TMkpWarehouse warehouse = DtoMapper.convert(warehouseUpdateDTO, TMkpWarehouse.class);
        if (StringUtil.isEmpty(warehouse.getId())) {
            return warehouseMapper.insert(warehouse);
        } else {
            return warehouseMapper.updateById(warehouse);
        }
    }

    /**
     * @Description 删除仓库
     *
     * <AUTHOR>
     * @Date 2024-5-15 11:48
     * @param id
     * @return java.lang.Integer
     **/
    @Override
    public Integer deleteWarehouse(Integer id) {
        return warehouseMapper.deleteById(id);
    }


    /**
     * @Description 根据仓库编码获取仓库信息
     *
     * <AUTHOR>
     * @Date 2024-5-15 11:48
     * @param warehouseCode
     * @return com.hvisions.powder.dto.storage.warehouse.WarehousePageDTO
     **/
    @Override
    public WarehousePageDTO getWarehouseDetailByCode(String warehouseCode) {
        TMkpWarehouse warehouse = warehouseMapper.selectOne(Wrappers.<TMkpWarehouse>lambdaQuery().
                eq(TMkpWarehouse::getWarehouseCode, warehouseCode)
                .last("limit 1")
        );
        return DtoMapper.convert(warehouse, WarehousePageDTO.class);
    }

    /**
     * @Description 根据仓库id获取仓库信息
     *
     * <AUTHOR>
     * @Date 2024-5-15 11:49
     * @param id
     * @return com.hvisions.powder.dto.storage.warehouse.WarehousePageDTO
     **/
    @Override
    public WarehousePageDTO getWarehouseDetail(Integer id) {
        WarehousePageDTO warehouseDetail = warehouseMapper.getWarehouseDetail(id);
        TMkpWarehouse tMkpWarehouse = warehouseMapper.selectById(id);
        //获取布勒的实时仓库料位信息
        if (StringUtils.isNotEmpty(tMkpWarehouse.getWincosCode())) {
            WincosBaseDataDto wincosBaseDataDto = new WincosBaseDataDto();
            wincosBaseDataDto.setStorageIdent(tMkpWarehouse.getWincosCode());
            WincosBaseResponseDto wincosBaseResponseDto = wincosService.getWarehouse(wincosBaseDataDto);
            if (!wincosBaseResponseDto.getIsSucceed()) {
                throw new RuntimeException(wincosBaseResponseDto.getMsg());
            }
            JSONArray jsonArray = (JSONArray) wincosBaseResponseDto.getData();
            List<WincosWarehouseDto> dataList = jsonArray.toJavaList(WincosWarehouseDto.class);
            if (CollectionUtils.isNotEmpty(dataList)) {
                conversionData(warehouseDetail, dataList.get(0));
            }
        }
        //虚拟粉仓获取库存 粉仓code  FC001 FC002 FC003 FC004
        if (warehouseDetail.getWarehouseCode().startsWith("FC00")) {
            List<TMkpWarehouseData> tMkpWarehouseData = warehouseDataMapper.selectList(new LambdaUpdateWrapper<TMkpWarehouseData>().eq(TMkpWarehouseData::getWarehouseId, warehouseDetail.getId()).eq(TMkpWarehouseData::getDeleted, false));
            if (CollectionUtils.isNotEmpty(tMkpWarehouseData)) {
                warehouseDetail.setWarehouseDosage(tMkpWarehouseData.stream().map(TMkpWarehouseData::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
        return warehouseDetail;
    }

    /**
     * 映射布勒的实体数据
     * @param warehouseDetail mes仓库数据
     * @param wincosWarehouseDto 布勒仓库数据
     */
    private void conversionData(WarehousePageDTO warehouseDetail, WincosWarehouseDto wincosWarehouseDto) {
        //高低料位
        if ("1".equals(wincosWarehouseDto.getHasLowLevelProbe())) {
            warehouseDetail.setWarehouseLowDosage(wincosWarehouseDto.getLevelProbeLowCovered());
        }
        if ("1".equals(wincosWarehouseDto.getHasHighLevelProbe())) {
            warehouseDetail.setWarehouseHighDosage(wincosWarehouseDto.getLevelProbeHighCovered());
        }
        // 布勒筒仓实时库存
        warehouseDetail.setWincosQuantity(new BigDecimal(wincosWarehouseDto.getLevelCur()));
        //物料信息
//        warehouseDetail.setMaterialCode(wincosWarehouseDto.getProductIdent());
//        warehouseDetail.setMaterialName(wincosWarehouseDto.getProductName());
    }

    /**
     * @Description 空仓确定
     *
     * <AUTHOR>
     * @Date 2024-3-19 15:00
     * @param id
     * @return java.lang.Integer
     **/
    @Override
    public Integer clearWarehouse(Integer id) {
        int res = 0;
        TMkpWarehouse warehouse = warehouseMapper.selectById(id);
        warehouse.setIsVacant(StorageVacantStateConstant.IS_VACANT);
        warehouse.setBatch("");
        warehouse.setUpdateTime(new Date());
        res += warehouseMapper.updateById(warehouse);
        LambdaQueryWrapper<TMkpWarehouseData> wrapper = new LambdaQueryWrapper<TMkpWarehouseData>()
                .eq(TMkpWarehouseData::getWarehouseId, id);
        TMkpWarehouseData update = new TMkpWarehouseData();
        UserBaseDTO userBaseDTO = null;
        try {
            userBaseDTO = userAuditorAware.getCurrentUserAudit()
                    .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
            update.setClearUserId(userBaseDTO.getId());
            update.setClearUserName(userBaseDTO.getUserName());
        } catch (BaseKnownException e) {
            log.error("获取用户信息失败:" + e.getMessage());
        }
        update.setClearDate(new Date());
        res += warehouseDataMapper.update(update, wrapper);
        warehouseDataMapper.delete(wrapper);
        return res;
    }

    /**
     * @Description 新增小麦筒仓库内数据，返回批次（小麦采购入库）
     *
     * <AUTHOR>
     * @Date 2024-3-19 15:25
     * @param warehouseBarnDataInsertDTO
     * @return java.lang.String 批次
     **/
    @Override
    public String addWareHouseData(WarehouseBarnDataInsertDTO warehouseBarnDataInsertDTO) {
        /**
         * 采供入库：
         *   1、判断库位是否存在
         *   2、判断库位剩余容量是否满足
         *   3、根据日期和物料判断是否是新的批次，如果是 生成批次，不是获取原来的批次
         *   4、判断当前库位下的库存是否存在数据，并且库存下的数据是否是同一批次
         *      如果是同一批次，数量累加，，不是则创建一条新的库存数据
         */
        TMkpWarehouse warehouse = warehouseMapper.selectById(warehouseBarnDataInsertDTO.getWarehouseId());
        if (warehouse == null) {
            throw new BaseKnownException(10000, "筒仓不存在！");
        }

        // 判断今日是否有的该物料库存
        List<TMkpWarehouseData> bathDataList = warehouseDataMapper.selectList(Wrappers.<TMkpWarehouseData>lambdaQuery()
                .eq(TMkpWarehouseData::getMaterialCode, warehouseBarnDataInsertDTO.getMaterialCode())
                .eq(TMkpWarehouseData::getMaterialId, warehouseBarnDataInsertDTO.getMaterialId())
                .between(TMkpWarehouseData::getCreateTime, new Date(DateUtil.getTodayStartTime()), new Date(DateUtil.getTodayEndTime()))
                .eq(TMkpWarehouseData::getDeleted, "0")
        );

        String batch = "";

        if (bathDataList.size() > 0) {
            // 存在该物料批次
            batch = bathDataList.get(0).getBatch();

        } else {
            String newNum = warehouseMapper.getBatchSerialNumber();
            String spells = ChineseCharacterUtil.getSpells(warehouseBarnDataInsertDTO.getMaterialName());
            batch = spells + DateUtil.dateFormat(new Date(), "yyyyMMdd") + newNum;
        }

        List<TMkpWarehouseData> warehouseDataList = warehouseDataMapper.selectList(Wrappers.<TMkpWarehouseData>lambdaQuery()
                .eq(TMkpWarehouseData::getWarehouseId, warehouseBarnDataInsertDTO.getWarehouseId())
                .eq(TMkpWarehouseData::getDeleted, "0")
        );
        if (warehouseDataList.size() > 0) {
            for (TMkpWarehouseData warehouseData : warehouseDataList) {
                if (warehouseData.getBatch().equals(batch)) { // 存在相同批次数据
                    warehouseData.setQuantity(warehouseData.getQuantity().add(warehouseBarnDataInsertDTO.getInQuantity()));
                    warehouseDataMapper.updateById(warehouseData);
                    return batch;
                }
            }
        }
        // 库位中不存在数据，新增库位数据
        TMkpWarehouseData warehouseData = DtoMapper.convert(warehouseBarnDataInsertDTO, TMkpWarehouseData.class);
        warehouseData.setBatch(batch);
        warehouseData.setQuantity(warehouseBarnDataInsertDTO.getInQuantity());
        warehouseData.setCreateTime(new Date());
        warehouseDataMapper.insert(warehouseData);
        return batch;
    }

    /**
     * @Description 筒仓出入库
     *
     * <AUTHOR>
     * @Date 2024-3-19 15:45
     * @param warehouseOutAndInDTO 出入库dto
     * @return java.lang.String
     **/
    @Override
    @Transactional
    public String warehouseOutAndIn(WarehouseOutAndInDTO warehouseOutAndInDTO) {
        /*
         * 出入库操作:
         *      1、获取出库库位下的库位数据，将最早的物料先出
         *      2、获取入库库位，判断是否存在库位数据，相同批次数据合并，不同批次数据新增
         */
        if (StringUtil.isEmpty(warehouseOutAndInDTO.getOutId()) && StringUtil.isNotEmpty(warehouseOutAndInDTO.getInId())) {
            // 没有出库，只有入库，直接入筒仓
            TMkpWarehouseData warehouseData = new TMkpWarehouseData();
            warehouseData.setWarehouseId(warehouseOutAndInDTO.getInId());
            warehouseData.setMaterialId(warehouseOutAndInDTO.getMaterialId());
            warehouseData.setMaterialName(warehouseOutAndInDTO.getMaterialName());
            warehouseData.setMaterialCode(warehouseOutAndInDTO.getMaterialCode());
            warehouseData.setBatch(warehouseOutAndInDTO.getBatch());
            warehouseData.setWheatNumber(warehouseOutAndInDTO.getWheatNumber());
            warehouseData.setQuantity(warehouseOutAndInDTO.getQuantity());
            this.stockIn(warehouseData);
            return warehouseOutAndInDTO.getBatch();
        } else if (StringUtil.isNotEmpty(warehouseOutAndInDTO.getOutId()) && StringUtil.isEmpty(warehouseOutAndInDTO.getInId())) {
            // 没有入库，只有出库，直接出筒仓，注意库存不够出
            TMkpWarehouse outStorage = warehouseMapper.selectById(warehouseOutAndInDTO.getOutId());
            List<TMkpWarehouseData> outStorageDataList = warehouseDataMapper.selectList(Wrappers.<TMkpWarehouseData>lambdaQuery()
                    .eq(TMkpWarehouseData::getWarehouseId, warehouseOutAndInDTO.getOutId())
                    .orderByAsc(TMkpWarehouseData::getCreateTime)
            );
            Set<String> batches = new HashSet<>();

            BigDecimal remainQuantity = warehouseOutAndInDTO.getQuantity(); // 记录剩余出库数量
            // 出库筒仓没有数据
//            if (outStorageDataList.size() == 0) {
//                throw new BaseKnownException(10000, "出仓筒仓没有数据，无法出仓！");
//            }
            for (TMkpWarehouseData outStorageData : outStorageDataList) {
                if (remainQuantity.compareTo(new BigDecimal(0)) == 0) {
                    return StringUtils.join(batches, ",");
                }
                if (outStorageData.getQuantity().compareTo(remainQuantity) > 0) {
                    // 剩余出库数量小于库存数量，库存减少
                    outStorageData.setQuantity(outStorageData.getQuantity().subtract(remainQuantity));
                    batches.add(outStorageData.getBatch());
                    warehouseDataMapper.updateById(outStorageData);
                    return StringUtils.join(batches, ",");
                } else {
                    // 剩余出库数量大于库存数量，全部出库（删除出库），剩余出库数量减少
                    remainQuantity = remainQuantity.subtract(outStorageData.getQuantity());
                    batches.add(outStorageData.getBatch());
                    warehouseDataMapper.deleteById(outStorageData.getId());
                }
            }
//            if (remainQuantity.compareTo(new BigDecimal(0)) > 0) {
//                throw new BaseKnownException(10000, outStorage.getWarehouseCode() + "库位库存不足以出库");
//            }

        } else if (StringUtil.isNotEmpty(warehouseOutAndInDTO.getOutId()) && StringUtil.isNotEmpty(warehouseOutAndInDTO.getInId())) {
            // 出入库筒仓都存在
            List<TMkpWarehouseData> outStorageDataList = warehouseDataMapper.selectList(Wrappers.<TMkpWarehouseData>lambdaQuery()
                    .eq(TMkpWarehouseData::getWarehouseId, warehouseOutAndInDTO.getOutId())
                    .eq(TMkpWarehouseData::getDeleted, 0)
                    .orderByAsc(TMkpWarehouseData::getCreateTime)
            );
            // 出库筒仓没有数据
//            if (outStorageDataList.size() == 0) {
//                throw new BaseKnownException(10000, "出仓筒仓没有数据，无法出仓！");
//            }
            Set<String> batches = new HashSet<>();
            BigDecimal remainQuantity = warehouseOutAndInDTO.getQuantity(); // 记录剩余入库数量

            for (TMkpWarehouseData outStorageData : outStorageDataList) {
                if (remainQuantity.compareTo(new BigDecimal(0)) == 0) {
                    return StringUtils.join(batches, ",");
                }

                // 剩余入库数量小于出库库存数量，出库库存减少，入库库存增加
                if (outStorageData.getQuantity().compareTo(remainQuantity) > 0) {
                    TMkpWarehouseData inStorageData = DtoMapper.convert(outStorageData, TMkpWarehouseData.class);
                    inStorageData.setWarehouseId(warehouseOutAndInDTO.getInId());
                    inStorageData.setQuantity(remainQuantity);

                    outStorageData.setQuantity(outStorageData.getQuantity().subtract(remainQuantity));
                    batches.add(outStorageData.getBatch());
                    warehouseDataMapper.updateById(outStorageData);
                    this.stockIn(inStorageData);
                    return StringUtils.join(batches, ",");
                } else {
                    // 剩余入库数量大于出库库存数量，全部出库（删除出库），入库增加，剩余入库数量减少
                    TMkpWarehouseData inStorageData = DtoMapper.convert(outStorageData, TMkpWarehouseData.class);
                    inStorageData.setWarehouseId(warehouseOutAndInDTO.getInId());
                    remainQuantity = remainQuantity.subtract(outStorageData.getQuantity());
                    batches.add(outStorageData.getBatch());
                    warehouseDataMapper.deleteById(outStorageData.getId());
                    this.stockIn(inStorageData);
                }
            }

            // 剩余入库数量大于0
//            if (remainQuantity.compareTo(new BigDecimal(0)) > 0) {
//                throw new BaseKnownException(10000, "筒仓库存不足以出库");
//            }
            return StringUtils.join(batches, ",");
        }
        return null;
    }

    /**
     * @Description 筒仓入库
     *
     * <AUTHOR>
     * @Date 2024-3-26 17:01
     * @param warehouseData
     * @return void
     **/
    public void stockIn(TMkpWarehouseData warehouseData) {
        List<TMkpWarehouseData> warehouseDataList = warehouseDataMapper.selectList(Wrappers.<TMkpWarehouseData>lambdaQuery()
                .eq(TMkpWarehouseData::getWarehouseId, warehouseData.getWarehouseId())
                .eq(TMkpWarehouseData::getDeleted, "0")
        );

        for (TMkpWarehouseData oldWarehouseData : warehouseDataList) {
            if (warehouseData.getBatch() != null && warehouseData.getBatch().equals(oldWarehouseData.getBatch())) {
                oldWarehouseData.setQuantity(oldWarehouseData.getQuantity().add(warehouseData.getQuantity()));
                oldWarehouseData.setUpdateTime(new Date());
                warehouseDataMapper.updateById(oldWarehouseData);
                return;
            }
        }
        warehouseData.setCreateTime(new Date());
        warehouseDataMapper.insert(warehouseData);
    }

    /**
     * @Description 粉仓出库(曲粉发放调用)
     *
     * <AUTHOR>
     * @Date 2024-6-12 17:59
     * @param powderOutDTO
     * @return java.lang.String
     **/
    @Override
    public String warehousePowderOut(WarehousePowderOutDTO powderOutDTO) {
        TMkpWarehouse warehouse = this.getPowderWarehouseByLineCode(powderOutDTO.getLineCode());
        if (StringUtil.isNotEmpty(warehouse)) {
            WarehouseOutAndInDTO warehouseOutAndInDTO = new WarehouseOutAndInDTO();
            warehouseOutAndInDTO.setOutId(warehouse.getId());
            warehouseOutAndInDTO.setQuantity(powderOutDTO.getQuantity());
            return this.warehouseOutAndIn(warehouseOutAndInDTO);
        }
        return "";
    }

    /**
     * @Description 根据布勒筒仓号获取mes筒仓号
     *
     * <AUTHOR>
     * @Date 2024-4-11 16:42
     * @param wincosCode
     * @return java.lang.String
     **/
    @Override
    public TMkpWarehouse getWarehouseByWincosCode(String wincosCode) {
        TMkpWarehouse warehouse = warehouseMapper.selectOne(Wrappers.<TMkpWarehouse>lambdaQuery()
                .eq(TMkpWarehouse::getWincosCode, wincosCode)
                .last("limit 1"));
        return warehouse;
    }

    /**
     * @Description 根据粉碎线获取粉仓
     *
     * <AUTHOR>
     * @Date 2024-5-14 17:16
     * @param lineCode
     * @return com.hvisions.powder.storage.entity.TMkpWarehouse
     **/
    @Override
    public TMkpWarehouse getPowderWarehouseByLineCode(String lineCode) {
        String lastChar = lineCode.substring(lineCode.length() - 1);

        TMkpWarehouse warehouse = warehouseMapper.selectOne(Wrappers.<TMkpWarehouse>lambdaQuery()
                .eq(TMkpWarehouse::getWarehouseType, WarehouseTypeConstant.POWDER)
                .like(TMkpWarehouse::getWarehouseCode, "FC00" + lastChar)
                .last("limit 1"));
        return warehouse;
    }

    @Override
    public List<TMkpWarehouse> getByIds(List<String> ids) {
        return warehouseMapper.selectList(new LambdaUpdateWrapper<TMkpWarehouse>().in(TMkpWarehouse::getId, ids));
    }

    @Override
    public String getWarehouseCodesById(String ids) {
        List<TMkpWarehouse> warehouses = getByIds(Arrays.asList(ids.split(",")));
        List<String> codeList = warehouses.stream().map(TMkpWarehouse::getWarehouseCode).collect(Collectors.toList());
        StringBuffer codes = new StringBuffer();
        int idx = 1;
        for (String code : codeList) {
            codes.append(code);
            if (idx < (codeList.size())) {
                codes.append(",");
            }
            idx++;
        }
        return codes.toString();
    }

    /**
     * 更新粉仓的高低料位
     * @param dto 更新dto
     * @return
     */
    @Override
    public int updateWarehouseHighDosage(ZhUpdateWarehouseHighDosageDto dto) {
        TMkpWarehouse tMkpWarehouse = baseMapper.selectOne(new LambdaUpdateWrapper<TMkpWarehouse>()
                .eq(TMkpWarehouse::getWarehouseCode, dto.getWarehouseCode()).eq(TMkpWarehouse::getDeleted, false).last("limit 1"));
        if (StringUtils.isNotEmpty(dto.getWarehouseLowDosage())) {
            log.info("更新粉仓低料位:" + dto.getWarehouseCode() + ", 更新：" + dto.getWarehouseLowDosage());
            tMkpWarehouse.setWarehouseLowDosage(dto.getWarehouseLowDosage());
        }
        if (StringUtils.isNotEmpty(dto.getWarehouseHighDosage())) {
            log.info("更新粉仓高料位:" + dto.getWarehouseCode() + ", 更新：" + dto.getWarehouseHighDosage());
            tMkpWarehouse.setWarehouseHighDosage(dto.getWarehouseHighDosage());
        }
        return baseMapper.updateById(tMkpWarehouse);
    }

    /**
     * @Description 根据仓库编码获取仓库库存详情（根据物料进行汇总）
     *
     * <AUTHOR>
     * @Date 2024-5-15 14:15
     * @param warehouseCode
     * @return java.util.List<com.hvisions.powder.dto.storage.warehouse.WarehouseStorageDTO>
     **/
    @Override
    public List<WarehouseStorageDTO> getWarehouseStorageByCode(String warehouseCode) {
        return baseMapper.getWarehouseStorageByCode(warehouseCode);
    }

    /**
     * @Description 根据仓库类型获取仓库库存详情（根据物料进行汇总）
     *
     * <AUTHOR>
     * @Date 2024-7-17 9:37
     * @param warehouseType
     * @return java.util.List<com.hvisions.powder.dto.storage.warehouse.WarehouseStorageDTO>
     **/
    @Override
    public List<WarehouseStorageDTO> getWarehouseStorageByWarehouseType(String warehouseType) {
        return baseMapper.getWarehouseStorageByWarehouseType(warehouseType);
    }

    /**
     * @Description 根据仓库类型获取仓库库存详情（根据筒仓汇总）
     *
     * <AUTHOR>
     * @Date 2024-7-17 9:37
     * @param warehouseType
     * @return java.util.List<com.hvisions.powder.dto.storage.warehouse.WarehouseDetailDTO>
     **/
    @Override
    public List<WarehouseDetailDTO> getWarehouseStorageDetailByWarehouseType(String warehouseType) {
        return baseMapper.getWarehouseStorageDetailByWarehouseType(warehouseType);
    }

    /**
     * 获取布勒实时筒仓库存
     * @return
     */
    @Override
    public List<WarehouseDetailDTO> getWincosWarehouse() {
        List<WarehouseDetailDTO> dataList = new ArrayList<>();
        //调用布勒获取实时数据接口
        List<TMkpWarehouse> warehouseList = warehouseMapper.selectList(new LambdaUpdateWrapper<TMkpWarehouse>().in(TMkpWarehouse::getWincosCode, "W001", "W002", "W003", "W004").eq(TMkpWarehouse::getDeleted, false));

        for (TMkpWarehouse tMkpWarehouse : warehouseList) {
            WarehouseDetailDTO data = new WarehouseDetailDTO();
            data.setWarehouseCode(tMkpWarehouse.getWarehouseCode());
            data.setId(tMkpWarehouse.getId());
            data.setVolume(tMkpWarehouse.getVolume());
            WincosBaseDataDto wincosBaseDataDto = new WincosBaseDataDto();
            wincosBaseDataDto.setStorageIdent(tMkpWarehouse.getWincosCode());
            WincosBaseResponseDto wincosBaseResponseDto = wincosService.getWarehouse(wincosBaseDataDto);
            if (!wincosBaseResponseDto.getIsSucceed()) {
                throw new RuntimeException(wincosBaseResponseDto.getMsg());
            }
            JSONArray jsonArray = (JSONArray) wincosBaseResponseDto.getData();
            List<WincosWarehouseDto> arrayJavaList = jsonArray.toJavaList(WincosWarehouseDto.class);
            if (CollectionUtils.isNotEmpty(arrayJavaList)) {
                data.setQuantity(new BigDecimal(arrayJavaList.get(0).getLevelCur()));
            }
            dataList.add(data);
        }
        return dataList;
    }
}
