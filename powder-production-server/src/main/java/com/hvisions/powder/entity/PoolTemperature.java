package com.hvisions.powder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 车辆运输管理配置信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_pool_temperature")
@ApiModel("PoolTemperature对象")
public class PoolTemperature extends SysBase {

    @ApiModelProperty(value = "日期", example = "2023-03-28")
    private LocalDate date;
    @ApiModelProperty(value = "酿酒中心", example = "709、713、718")
    private String center;
    @ApiModelProperty(value = "车间", example = "1、2、3、4、5")
    private String location;
    @ApiModelProperty(value = "窖号", example = "19749")
    private String pitCode;
    @ApiModelProperty(value = "上层平均温度")
    private BigDecimal top;
    @ApiModelProperty(value = "中层平均温度")
    private BigDecimal middle;
    @ApiModelProperty(value = "下层平均温度")
    private BigDecimal bottom;
}
