package com.hvisions.powder.kanban.controller;

import com.hvisions.powder.dto.kanban.product.*;
import com.hvisions.powder.dto.kanban.product.completeRate.DayQpCompleteRateDTO;
import com.hvisions.powder.dto.kanban.product.completeRate.DayQpCompleteRateQueryDTO;
import com.hvisions.powder.dto.kanban.product.completeRate.FloorCompleteDetailDTO;
import com.hvisions.powder.dto.kanban.product.completeRate.SendReceiveStoreSummaryQueryDTO;
import com.hvisions.powder.dto.kanban.product.inAndOut.OrderMaterialInAndOutQuantityQueryDTO;
import com.hvisions.powder.dto.kanban.product.inAndOut.ProductSuccessRateDTO;
import com.hvisions.powder.dto.kanban.product.smashBatchCompart.SmashBatchCompareDTO;
import com.hvisions.powder.dto.kanban.product.smashBatchCompart.SmashBatchCompareQueryDTO;
import com.hvisions.powder.dto.kanban.product.timeProduct.TimeEmbryoProductDTO;
import com.hvisions.powder.dto.kanban.product.timeProduct.TimeEmbryoProductQueryDTO;
import com.hvisions.powder.kanban.service.ProductReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 生产报表
 * @date 2024/6/18 10:21
 */
@RestController
@RequestMapping("/product/report")
@Api(tags = "生产报表")
@Slf4j
public class ProductReportController {

    @Resource
    private ProductReportService productReportService;

    @ApiOperation(value = "获取各月度曲坯产量")
    @RequestMapping(value = "/getMonthEmbryoProductList", method = RequestMethod.POST)
    public List<MonthEmbryoProductDTO> getMonthEmbryoProductList(@RequestBody ProductReportQueryDTO queryDTO) {
        return productReportService.getMonthEmbryoProductList(queryDTO);
    }

    @ApiOperation(value = "获取指定时间内的qp1和qp2产量")
    @RequestMapping(value = "/getEmbryoProductByTime", method = RequestMethod.POST)
    public MonthEmbryoProductDTO getEmbryoProductByTime(@RequestBody ProductReportQueryDTO queryDTO) {
        return productReportService.getEmbryoProductByTime(queryDTO);
    }

    @ApiOperation(value = "获取粉碎批次参数对比列表")
    @RequestMapping(value = "/getSmashBatchCompareList", method = RequestMethod.POST)
    public Page<SmashBatchCompareDTO> getSmashBatchCompareList(@RequestBody SmashBatchCompareQueryDTO queryDTO) {
        return productReportService.getSmashBatchCompareList(queryDTO);
    }

    @ApiOperation(value = "根据不同时间维度统计各楼层曲坯产量")
    @RequestMapping(value = "/getTimeEmbryoProductList", method = RequestMethod.POST)
    public List<TimeEmbryoProductDTO> getTimeEmbryoProductList(@RequestBody TimeEmbryoProductQueryDTO queryDTO) {
        return productReportService.getTimeEmbryoProductList(queryDTO);
    }

    @ApiOperation(value = "获取大曲月度库存报表（扎帐报表）")
    @RequestMapping(value = "/getPowderMonthStockList", method = RequestMethod.POST)
    public List<PowderMonthStockDTO> getPowderMonthStockList(@RequestBody List<LocalDate> dates) {
        return productReportService.getPowderMonthStockList(dates);
    }

    @ApiOperation(value = "获取月计划达成率（计划达成报表）")
    @RequestMapping(value = "/getMonthPlanCompleteRate", method = RequestMethod.POST)
    public Map<Integer, BigDecimal> getMonthPlanCompleteRate(@RequestBody PlanCompleteReportQueryDTO queryDTO) {
        return productReportService.getMonthPlanCompleteRate(queryDTO);
    }

    @ApiOperation(value = "获取指定月曲坯成曲率（计划达成报表）")
    @RequestMapping(value = "/getMonthProductSuccessRate", method = RequestMethod.POST)
    public Map<Integer, BigDecimal> getMonthProductSuccessRate(@RequestBody PlanCompleteReportQueryDTO queryDTO) {
        return productReportService.getMonthProductSuccessRate(queryDTO);
    }

    @ApiOperation(value = "获取指定月大曲成曲率（成曲率报表）")
    @RequestMapping(value = "/getPowderProductSuccessRate", method = RequestMethod.POST)
    public List<ProductSuccessRateDTO> getPowderProductSuccessRate(@RequestBody OrderMaterialInAndOutQuantityQueryDTO queryDTO) {
        return productReportService.getPowderProductSuccessRate(queryDTO);
    }

    @ApiOperation(value = "获取楼层当日生产进度（计划看板）")
    @RequestMapping(value = "/getFloorProductDetailByMaterialAndTime", method = RequestMethod.POST)
    public Map<String, List<BigDecimal>> getFloorProductDetailByMaterialAndTime(@RequestBody FloorOrderProductionQueryDTO queryDTO) {
        return productReportService.getFloorProductDetailByMaterialAndTime(queryDTO);
    }

    @ApiOperation(value = "获取近7天的曲坯日产量（计划看板）")
    @RequestMapping(value = "/get7DayQpProduction", method = RequestMethod.GET)
    public List<MonthEmbryoProductDTO> get7DayQpProduction() throws ParseException {
        return productReportService.get7DayQpProduction();
    }

    @ApiOperation(value = "获取指定区间内的曲坯计划和实际产量（制曲中心月计划完成报表）")
    @RequestMapping(value = "/getMonthEmbryoPlanAndProduct", method = RequestMethod.POST)
    public List<MonthEmbryoPlanAndProductDTO> getMonthEmbryoPlanAndProduct(@RequestBody ProductReportQueryDTO queryDTO) {
        return productReportService.getMonthEmbryoPlanAndProduct(queryDTO);
    }

    @ApiOperation(value = "获取指定年份下各月qf1、qf2 计划、实际、需求、发放量（制曲中心月计划完成报表）")
    @RequestMapping(value = "/getMonthEmbryoPlanAndProductAndIssueByYear/{year}", method = RequestMethod.GET)
    public List<MonthEmbryoPlanAndProductDTO> getMonthEmbryoPlanAndProductAndIssueByYear(@PathVariable Integer year) {
        return productReportService.getMonthEmbryoPlanAndProductAndIssueByYear(year);
    }

    @ApiOperation(value = "获取各楼层每日曲坯完成率（大曲生产计划完成情况报表）")
    @RequestMapping(value = "/getDayQpCompleteRate", method = RequestMethod.POST)
    public List<DayQpCompleteRateDTO> getDayQpCompleteRate(@RequestBody DayQpCompleteRateQueryDTO queryDTO) {
        return productReportService.getDayQpCompleteRate(queryDTO);
    }

    @ApiOperation(value = "获取大曲生产计划完成情况列表（大曲生产计划完成情况报表）")
    @RequestMapping(value = "/getFloorCompleteDetailList", method = RequestMethod.POST)
    public List<FloorCompleteDetailDTO> getFloorCompleteDetailList(@RequestBody DayQpCompleteRateQueryDTO queryDTO) {
        return productReportService.getFloorCompleteDetailList(queryDTO);
    }

    @ApiOperation(value = "获取qp1和qp2 各月产量、完成率、成曲率（大曲生产情况报表）")
    @RequestMapping(value = "/getQpCompleteAndPassRate/{year}", method = RequestMethod.GET)
    public List<QpCompleteAndPassRateDTO> getQpCompleteAndPassRate(@PathVariable Integer year) {
        return productReportService.getQpCompleteAndPassRate(year);
    }


    @ApiOperation(value = "制曲中心月度报表累计")
    @RequestMapping(value = "/getMonthProductSummary", method = RequestMethod.POST)
    public List<MonthProductSummaryDTO> getMonthProductSummary(@RequestBody DayQpCompleteRateQueryDTO queryDTO) {
        return productReportService.getMonthProductSummary(queryDTO);
    }

    @ApiOperation(value = "制曲中心月度大曲库存报表")
    @RequestMapping(value = "/getInventorySummary", method = RequestMethod.POST)
    public List<InventorySummaryDTO> getInventorySummary(@RequestBody DayQpCompleteRateQueryDTO queryDTO) {
        return productReportService.getInventorySummary(queryDTO);
    }

    @ApiOperation(value = "原辅料收发存情况报表")
    @RequestMapping(value = "/getSendReceiveStoreSummary", method = RequestMethod.POST)
    public List<SendReceiveStoreSummaryDTO> getSendReceiveStoreSummary(@RequestBody SendReceiveStoreSummaryQueryDTO queryDTO) {
        return productReportService.getSendReceiveStoreSummary(queryDTO);
    }

    @ApiOperation(value = "获取年计划完成率")
    @RequestMapping(value = "/getYearPlanCompletionRate", method = RequestMethod.POST)
    public YearPlanCompletionRateDto getYearPlanCompletionRate() {
        return productReportService.getYearPlanCompletionRate();
    }
}
