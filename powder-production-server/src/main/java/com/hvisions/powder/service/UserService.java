package com.hvisions.powder.service;

import com.hvisions.powder.dto.QueryDTO;
import com.hvisions.powder.dto.UserDTO;
import com.hvisions.common.dto.ExcelExportDto;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: UserService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface UserService {


	/**
	 * 根据名称和描述模糊查询
	 *
	 * @param queryDTO 名称
	 * @return 分页数据
	 */
	Page<UserDTO> getPage(QueryDTO queryDTO);

	/**
	 * 添加实体
	 *
	 * @param userDTO 实体对象
	 * @return 实体ID
	 */
	int save(UserDTO userDTO);

	/**
	 * 删除实体
	 *
	 * @param id 实体id
	 */
	void deleteById(int id);

	/**
	 * 逻辑删除
	 *
	 * @param id id
	 * <AUTHOR>
	 * @date 2022/2/25 8:43 下午
	 */
	void logicDelete(int id);

	/**
	 * 获取所有
	 *
	 * @return 所有实体列表
	 */
	List<UserDTO> getList();

	/**
	 * 分页查询另外一种实现
	 *
	 * @param queryDTO 查询对象
	 * @return 分页信息
	 */
	Page<UserDTO> getByNameOtherWay(QueryDTO queryDTO);

	/**
	 * 查询列表
	 *
	 * @param queryDTO 查询条件
	 * @return list
	 */
	List<UserDTO> getUserList(QueryDTO queryDTO);

	/**
	 * 分页查询
	 *
	 * @param queryDTO 查询条件
	 * @return 分页信息
	 */
	Page<UserDTO> getUserPage(QueryDTO queryDTO);

	/**
	 * 根据名称和描述模糊查询,使用Jpa另外一种实现方式，Specifications
	 * 灵活度最高。但是代码量最多。基本可以实现单表所有的复杂查询
	 *
	 * @param dto 查询对象
	 * @return 分页数据
	 */
	Page<UserDTO> getByNameBySpec(QueryDTO dto);

	/**
	 * 导入示例
	 *
	 * @param file 文件
	 */
	void importExample(MultipartFile file);

	/**
	 * 下载模板(给前端使用）
	 *
	 * @return 模板数据
	 */
	ExcelExportDto getImportTemplateForFront();

	/**
	 * 下载模板
	 *
	 * @return 模板
	 */
	ResponseEntity<byte[]> getImportTemplate();
}
