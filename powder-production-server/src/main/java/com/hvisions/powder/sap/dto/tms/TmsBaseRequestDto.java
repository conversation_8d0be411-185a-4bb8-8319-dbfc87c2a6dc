package com.hvisions.powder.sap.dto.tms;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class TmsBaseRequestDto {
    public TmsBaseRequestDto(Request request) {
        this.request = request;
    }

    @JsonProperty("REQUEST")
    private Request request;

    @Data
    public static class Request {
        public Request(Head head, List list) {
            this.head = head;
            this.list = list;
        }

        @JsonProperty("HEAD")
        private Head head;

        @JsonProperty("LIST")
        private List list;


        @Data
        public static class Head {
            public Head() {
                this.account = "ZQMES";
                this.password = "";
                this.consumer = "ZQMES";
                this.count = "1";
                this.sevLevel = "TMS";
                final String now = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
                this.requestDate = now;
                final String transaction = "LZLJ_ZQMES_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                this.biTransactionId = transaction;
                this.setBzbh("");
                this.setBglx("");
                this.setKds("");
                this.setJhrq("");

            }

            @JsonProperty("BIZTRANSACTIONID")
            private String biTransactionId;

            @JsonProperty("ACCOUNT")
            private String account;

            @JsonProperty("PASSWORD")
            private String password;

            @JsonProperty("CONSUMER")
            private String consumer;

            @JsonProperty("COUNT")
            private String count;


            @JsonProperty("SRVLEVEL")
            private String sevLevel;

            @JsonProperty("REQUESTDATE")
            private String requestDate;

            // 变更类型： I新增,U修改
            @JsonProperty("BGLX")
            private String bglx;

            // 跨
            @JsonProperty("BZBH")
            private String bzbh;

            @JsonProperty("KDS")
            private String kds;

            @JsonProperty("JHRQ")
            private String jhrq;

        }


        @Data
        public static class List {

//            public List(String qf1, String qf2, String qf3, String kds) {
//                this.qf1 = qf1;
//                this.qf2 = qf2;
//                this.qf3 = qf3;
//                this.kds = kds;
//            }

            @JsonProperty("QF1")
            private String qf1;

            @JsonProperty("QF2")
            private String qf2;

            @JsonProperty("QF3")
            private String qf3;

            @JsonProperty("QF1GG")
            private String qf1gg;

            @JsonProperty("QF2GG")
            private String qf2gg;

            @JsonProperty("QF3GG")
            private String qf3gg;

            // 空斗数
            @JsonProperty("kds")
            private String kds;

        }
    }
}
