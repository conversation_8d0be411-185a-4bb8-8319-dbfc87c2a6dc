package com.hvisions.powder.sap.dto.wincos;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 布勒基础参数数据dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Getter
@Setter
@NoArgsConstructor
public class WincosBaseDataDto {

    // 入仓任务下发参数
    @ApiModelProperty(value = "Mes单据号")
    @JsonProperty("billNo")
    private String billNo;

    @ApiModelProperty(value = "生产线编号(例：INTW1)")
    @JsonProperty("lineIdent")
    private String lineIdent;

    @ApiModelProperty(value = "物料编号")
    @JsonProperty("productIdent")
    private String productIdent;

    @ApiModelProperty(value = "物料名称")
    @JsonProperty("productName")
    private String productName;

    @ApiModelProperty(value = "生产线目标流量")
    @JsonProperty("lineFlowrateTarget")
    private Integer lineFlowrateTarget;

    @ApiModelProperty(value = "接收仓")
    @JsonProperty("receivers")
    private String receivers;

    @ApiModelProperty(value = "备注")
    @JsonProperty("remark")
    private String remark;

    // 润麦任务下发参数
    @ApiModelProperty(value = "目标重量")
    @JsonProperty("targetWeight")
    private Integer targetWeight;

    @ApiModelProperty(value = "发送仓")
    @JsonProperty("senders")
    private String senders;

    @ApiModelProperty(value = "配麦比")
    @JsonProperty("sendersRate")
    private String sendersRate;

    @ApiModelProperty(value = "着水机1目标水分")
    @JsonProperty("targetMoisture1")
    private Integer targetMoisture1;

    @ApiModelProperty(value = "着水机1水分补偿")
    @JsonProperty("moistureOffset1")
    private BigDecimal moistureOffset1;

    @ApiModelProperty(value = "着水机2目标水分")
    @JsonProperty("targetMoisture2")
    private Integer targetMoisture2;

    @ApiModelProperty(value = "着水机2水分补偿")
    @JsonProperty("moistureOffset2")
    private BigDecimal moistureOffset2;

    // 磨粉任务下发参数
    @ApiModelProperty(value = "着水机3目标水分")
    @JsonProperty("targetMoisture3")
    private Integer targetMoisture3;

    @ApiModelProperty(value = "着水机4目标水分")
    @JsonProperty("targetMoisture4")
    private Integer targetMoisture4;

    @ApiModelProperty(value = "1楼1线2线用麦流量")
    @JsonProperty("flowrate1")
    private Integer flowrate1;

    @ApiModelProperty(value = "1楼3线4线用麦流量")
    @JsonProperty("flowrate2")
    private Integer flowrate2;

    //仓容信息查询
    @ApiModelProperty(value = "仓库号 用,隔开")
    @JsonProperty("storageIdent")
    private String storageIdent;
}
