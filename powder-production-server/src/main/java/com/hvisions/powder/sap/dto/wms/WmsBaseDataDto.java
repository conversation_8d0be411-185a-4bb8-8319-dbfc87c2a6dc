package com.hvisions.powder.sap.dto.wms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class WmsBaseDataDto {

    //下发出入库单据
    /**
     * 单据号，我们用wmsOrderNo
     */
    private String storageOrderNo;

    /**
     * 出入库单据类型 In，Out，InOut
     */
    private String type;

    /**
     * 源位置 多个可以用英文,隔开
     */
    private String sendCode;

    /**
     *  目标位置 多个可以用英文,隔开
     */
    private String acceptCode;

    /**
     * 物料编号 实曲框,空曲框,实曲架,空曲架
     */
    private String itemNum;

    /**
     * 当前生产状态 新增0,确认1,完成2,取消3,暂停4
     */
    private String ProductStatus;

    /**
     * 发酵时间
     */
    private String fermentTime;

    /**
     * 批次信息
     */
    private String batch;

    //库存调整
    /**
     *  库房
     */
    private String wareHouse;

    /**
     * 状态 发酵中，发酵完成
     * 状态 启动， 禁用
     */
    private String status;

    /**
     * 调整 0空闲,1占用
     */
    private String pltNum;

    /**
     * 库位列表
     */
    private List<String> locNums;

}
