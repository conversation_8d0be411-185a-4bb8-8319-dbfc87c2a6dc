package com.hvisions.powder.sap.controller;


import com.hvisions.powder.sap.dto.wms.WmsExceptionDto;
import com.hvisions.powder.sap.dto.zongheng.*;
import com.hvisions.powder.sap.service.ZonghengService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description: 制曲生产纵横对接
 * @author: lyz
 * @time: 2024/4/24 10:11
 */
@RestController
@RequestMapping(value = "/zongheng")
@Api(tags = "制曲生产纵横对接")
public class ZongHengController {

    @Resource
    private ZonghengService zonghengService;


    @ApiOperation(value = "压曲/剔除数量")
    @RequestMapping(value = "/extrusionNum", method = RequestMethod.PUT)
    public Integer addExtrusionNum(@RequestBody ZhExtrusionNumDto dto) {
        return zonghengService.addExtrusionNum(dto);
    }

    @ApiOperation(value = "粉碎投入曲框数量")
    @RequestMapping(value = "/smashInput", method = RequestMethod.POST)
    public Integer addSmashInput(@RequestBody ZhSmashInputDto dto) {
        return zonghengService.addSmashInput(dto);
    }

    @ApiOperation(value = "粉仓料位信息接口")
    @RequestMapping(value = "/warehouseInformation", method = RequestMethod.PUT)
    public Integer updateWarehouseInformation(@RequestBody ZhWarehouseInformationDto dto) {
        return zonghengService.updateWarehouseInformation(dto);
    }

    @ApiOperation(value = "粉仓高低料位信息")
    @RequestMapping(value = "/warehouseHighDosage", method = RequestMethod.PUT)
    public Integer updateWarehouseHighDosage(@RequestBody ZhUpdateWarehouseHighDosageDto dto) {
        return zonghengService.updateWarehouseHighDosage(dto);
    }


    @ApiOperation(value = "设备信息")
    @RequestMapping(value = "/exception", method = RequestMethod.POST)
    public Integer exception(@RequestBody ZhExceptionDto exceptionDto) {
        return zonghengService.addException(exceptionDto);
    }

    @ApiOperation(value = "打包数据返回")
    @RequestMapping(value = "/packageData", method = RequestMethod.POST)
    public Integer packageData(@RequestBody ZhPackageDto packageDto) {
        return zonghengService.packageData(packageDto);
    }
    
}
