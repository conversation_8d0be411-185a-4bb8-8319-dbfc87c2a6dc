package com.hvisions.powder.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: sap生产订单返回数据dto
 * @author: yyy
 * @time: 2024/4/8 10:19
 */
@Getter
@Setter
@NoArgsConstructor
public class SapGoodsIssueDto {

    // 抬头主键
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    // 消息类型
    @JsonProperty("MSGTY")
    private String msgty;

    // 消息文本
    @JsonProperty("MSGTX")
    private String msgtx;

    // 序列号
    @JsonProperty("ID")
    private String id;

    // 产出物料编码
    @JsonProperty("MATNR")
    private String matnr;

    // sap订单号
    @JsonProperty("ORDERID")
    private String orderId;

    // 投入数量
    @JsonProperty("MENGE")
    private String menge;

    // 投入单位
    @JsonProperty("MEINS")
    private String meins;

    // 凭证年度
    @JsonProperty("MJAHR")
    private String mjahr;

    // 物料凭证
    @JsonProperty("MBLNR")
    private String mblnr;

    // 物料凭证行号
    @JsonProperty("ZEILE")
    private String zeile;

    // 移动类型
    @JsonProperty("BWART")
    private String bwart;

    // 过账日期
    @JsonProperty("BUDAT")
    private String budat;


    @JsonProperty("EXT01")
    private String extOne = "";

    @JsonProperty("EXT02")
    private String extTwo = "";

    @JsonProperty("EXT03")
    private String extThree = "";

    @JsonProperty("EXT04")
    private String extFour = "";

    @JsonProperty("EXT05")
    private String extFive = "";


}
