package com.hvisions.powder.product.controller.exception;

import com.hvisions.powder.dto.product.exception.*;
import com.hvisions.powder.product.service.exception.ExceptionLevelService;
import com.hvisions.powder.product.service.exception.ExceptionManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @描述: 异常管理控制器
 * @作者: 刘文勇
 * @日期: 2024/4/3 9:31
 * @版本 1.0
 */
@RestController
@RequestMapping("/exception")
@Api(tags = "异常管理控制器")
@Slf4j
public class ExceptionController {

    /**
     * 异常级别业务
     */
    @Resource
    private ExceptionLevelService exceptionLevelService;

    /**
     * 异常管理业务
     */
    @Resource
    private ExceptionManageService exceptionManageService;

    @ApiOperation(value = "分页查询异常级别")
    @RequestMapping(value = "/level/list/page/get", method = RequestMethod.POST)
    public Page<ExceptionLevelPageDTO> getExceptionLevelPageList(@RequestBody ExceptionLevelPageQueryDTO exceptionLevelPageQueryDTO) {
        return exceptionLevelService.getExceptionLevelPageList(exceptionLevelPageQueryDTO);
    }

    @ApiOperation(value = "查询所有异常级别")
    @RequestMapping(value = "/level/list/get", method = RequestMethod.POST)
    public List<ExceptionLevelPageDTO> getExceptionLevelList(@RequestBody ExceptionLevelQueryDTO exceptionLevelQueryDTO) {
        return exceptionLevelService.getExceptionLevelList(exceptionLevelQueryDTO);
    }

    @ApiOperation(value = "新增异常级别")
    @RequestMapping(value = "/level/add", method = RequestMethod.POST)
    public Boolean insertExceptionLevel(@RequestBody ExceptionLevelDTO exceptionLevelDTO) {
        return exceptionLevelService.insertExceptionLevel(exceptionLevelDTO);
    }

    @ApiOperation(value = "修改异常级别")
    @RequestMapping(value = "/level/update", method = RequestMethod.POST)
    public Boolean updateExceptionLevel(@RequestBody ExceptionLevelDTO exceptionLevelDTO) {
        return exceptionLevelService.updateExceptionLevel(exceptionLevelDTO);
    }

    @ApiOperation(value = "删除异常级别")
    @RequestMapping(value = "/level/delete/{id}", method = RequestMethod.GET)
    public Boolean deleteExceptionLevel(@PathVariable Integer id) {
        return exceptionLevelService.deleteExceptionLevel(id);
    }

    @ApiOperation(value = "分页查询异常管理")
    @RequestMapping(value = "/manage/list/page/get", method = RequestMethod.POST)
    public Page<ExceptionManagePageDTO> getExceptionManagePageList(@RequestBody ExceptionManageQueryPageDTO exceptionManageQueryPageDTO) {
        return exceptionManageService.getExceptionManagePageList(exceptionManageQueryPageDTO);
    }

    @ApiOperation(value = "新增异常管理")
    @RequestMapping(value = "/manage/add", method = RequestMethod.POST)
    public Boolean insertExceptionManage(@RequestBody ExceptionManageDTO exceptionManageDTO) {
        return exceptionManageService.insertExceptionManage(exceptionManageDTO);
    }

    @ApiOperation(value = "修改异常管理")
    @RequestMapping(value = "/manage/update", method = RequestMethod.POST)
    public Boolean updateExceptionManage(@RequestBody ExceptionManageDTO exceptionManageDTO) {
        return exceptionManageService.updateExceptionManage(exceptionManageDTO);
    }

    @ApiOperation(value = "删除异常管理")
    @RequestMapping(value = "/manage/delete/{id}", method = RequestMethod.GET)
    public Boolean deleteExceptionManage(@PathVariable Integer id) {
        return exceptionManageService.deleteExceptionManage(id);
    }

    @ApiOperation(value = "关闭异常管理")
    @RequestMapping(value = "/close/{id}", method = RequestMethod.GET)
    public Boolean closeExceptionManage(@PathVariable Integer id) {
        return exceptionManageService.closeExceptionManage(id);
    }

    @ApiOperation(value = "异常处理")
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public Boolean handleException(@RequestBody ExceptionHandleDTO exceptionHandleDTO) {
        return exceptionManageService.handleException(exceptionHandleDTO);
    }

    @ApiOperation(value = "获取未处理的异常top10")
    @RequestMapping(value = "/getNotHandleTop10", method = RequestMethod.GET)
    public List<ExceptionManagePageDTO> getNotHandleTop10() {
        return exceptionManageService.getNotHandleTop10();
    }

}
