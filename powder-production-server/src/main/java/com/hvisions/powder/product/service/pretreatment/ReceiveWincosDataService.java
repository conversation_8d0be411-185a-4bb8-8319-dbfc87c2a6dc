package com.hvisions.powder.product.service.pretreatment;


import com.hvisions.powder.dto.product.pretreatment.receiveWincos.EquipmentDataReceiveDTO;
import com.hvisions.powder.dto.product.pretreatment.receiveWincos.MillingTaskDataReceiveDTO;
import com.hvisions.powder.dto.product.pretreatment.receiveWincos.RunmaiTaskDataReceiveDTO;
import com.hvisions.powder.dto.product.pretreatment.receiveWincos.WarehouseTaskDetailReceiveDTO;

/**
 * <AUTHOR>
 * @Description: 接收布勒数据
 * @date 2024/01/19 10:18
 */
public interface ReceiveWincosDataService {

    /**
     * @Description 接收布勒入仓任务明细入库
     *
     * <AUTHOR>
     * @Date 2024-3-13 14:40
     * @param warehouseTaskDetailReceiveDTO
     * @return java.lang.Integer
     **/
    Integer receiveWarehouseTaskDetail(WarehouseTaskDetailReceiveDTO warehouseTaskDetailReceiveDTO);


    /**
     * @Description 接收布勒润麦任务数据
     *
     * <AUTHOR>
     * @Date 2024-4-11 16:29
     * @param receiveDTO
     * @return java.lang.Integer
     **/
    Integer receiveRunmaiTaskData(RunmaiTaskDataReceiveDTO receiveDTO);

    /**
     * @Description 接收布勒磨粉任务数据
     *
     * <AUTHOR>
     * @Date 2024-4-11 18:09
     * @param receiveDTO
     * @return java.lang.Integer
     **/
    Integer receiveMillingTaskData(MillingTaskDataReceiveDTO receiveDTO);

    /**
     * @Description 接收布勒设备启动信息
     *
     * <AUTHOR>
     * @Date 2024-4-12 14:28
     * @param receiveDTO
     * @return java.lang.Integer
     **/
    Integer receiveEquipmentData(EquipmentDataReceiveDTO receiveDTO);
}
