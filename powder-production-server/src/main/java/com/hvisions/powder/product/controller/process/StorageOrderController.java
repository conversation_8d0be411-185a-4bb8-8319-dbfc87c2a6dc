package com.hvisions.powder.product.controller.process;

import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderListDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderListQueryDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageQueryDTO;
import com.hvisions.powder.product.service.process.StorageOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @描述: 出入库单管理控制器
 * @作者: 刘文勇
 * @日期: 2024/2/29 16:28
 * @版本 1.0
 */
@RestController
@RequestMapping("/storage/order")
@Api(tags = "出入库单管理控制器")
@Slf4j
public class StorageOrderController {

    /**
     * 出入库单业务
     */
    @Resource
    private StorageOrderService storageOrderService;

    @ApiOperation(value = "分页查询出入库单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<StorageOrderPageDTO> getRepositoryManagePageList(@RequestBody StorageOrderPageQueryDTO storageOrderPageQueryDTO) {
        return storageOrderService.getRepositoryManagePageList(storageOrderPageQueryDTO);
    }

    @ApiOperation(value = "获取指定工单的出入库单")
    @RequestMapping(value = "/getStorageOrderList/{orderId}", method = RequestMethod.POST)
    public List<StorageOrderPageDTO> getStorageOrderList(@PathVariable Integer orderId, @RequestBody List<Integer> typeList) {
        return storageOrderService.getStorageOrderList(orderId, typeList);
    }

    @ApiOperation(value = "出入库单据多对多查询")
    @RequestMapping(value = "/orderList", method = RequestMethod.POST)
    public Page<StorageOrderListDTO> orderList(@RequestBody StorageOrderListQueryDTO queryDTO) {
        return storageOrderService.orderList(queryDTO);
    }
}
