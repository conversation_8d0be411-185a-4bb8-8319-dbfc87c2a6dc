package com.hvisions.powder.product.service.config;


import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.product.config.evaluation.EvaluationRecordExportDTO;
import com.hvisions.powder.dto.product.config.evaluation.EvaluationRecordPageDTO;
import com.hvisions.powder.dto.product.config.evaluation.EvaluationRecordPageQueryDTO;
import com.hvisions.powder.dto.product.config.evaluation.EvaluationRecordUpdateDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <AUTHOR>
 * @Description: 品鉴记录
 * @date 2024/01/19 10:18
 */
public interface EvaluationRecordService extends EntitySaver<EvaluationRecordExportDTO> {

    Page<EvaluationRecordPageDTO> getEvaluationRecordPageList(EvaluationRecordPageQueryDTO queryDTO);

    Integer addOrUpdateEvaluationRecord(EvaluationRecordUpdateDTO evaluationRecordUpdateDTO);

    Integer deleteEvaluationRecord(Integer id);

    ExcelExportDto exportData(EvaluationRecordPageQueryDTO queryDTO);

    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    ImportResult importData(MultipartFile file) throws IOException, IllegalAccessException, ParseException;


}
