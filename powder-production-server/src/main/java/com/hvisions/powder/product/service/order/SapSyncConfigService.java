package com.hvisions.powder.product.service.order;

import com.hvisions.powder.dto.product.order.sync.SapSyncConfigSaveDTO;
import com.hvisions.powder.product.entity.order.TMkpSapSyncConfig;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * sap 扎帐配置
 */
public interface SapSyncConfigService {

    List<TMkpSapSyncConfig> getList();

    List<TMkpSapSyncConfig> getListByParent(Integer parentId);

    Boolean insertData(SapSyncConfigSaveDTO sapSyncConfigSaveDTO);

    Boolean updateData(SapSyncConfigSaveDTO sapSyncConfigSaveDTO);
}
