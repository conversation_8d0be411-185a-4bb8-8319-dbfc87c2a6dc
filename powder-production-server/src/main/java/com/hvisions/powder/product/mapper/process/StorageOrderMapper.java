package com.hvisions.powder.product.mapper.process;

import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderListDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderListQueryDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageDTO;
import com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageQueryDTO;
import com.hvisions.powder.product.entity.process.TMkpStorageOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_mkp_storage_order(出入库单管理)】的数据库操作Mapper
* @createDate 2024-03-19 15:18:42
* @Entity com.hvisions.powder.product.entity.process.TMkpStorageOrder
*/
@Mapper
public interface StorageOrderMapper extends BaseMapper<TMkpStorageOrder> {

    int batchInsert(@Param("list") List<TMkpStorageOrder> storageOrderList);

    /**
     * @Description 获取指定工单的出入库单据
     *
     * <AUTHOR>
     * @Date 2024-4-29 14:58
     * @param orderId
     * @param typeList
     * @return java.util.List<com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageDTO>
     **/
    List<StorageOrderPageDTO> getStorageOrderList(@Param("orderId") Integer orderId, @Param("typeList") List<Integer> typeList);

    /**
     * @Description 获取出入库单列表和下级搬运记录
     *
     * <AUTHOR>
     * @Date 2024-4-29 14:55
     * @param storageOrderPageQueryDTO
     * @return java.util.List<com.hvisions.powder.dto.product.process.storageOrder.StorageOrderPageDTO>
     **/
    List<StorageOrderPageDTO> getStorageOrderPageList(StorageOrderPageQueryDTO storageOrderPageQueryDTO);

    /**
     * 搬运记录增加出入库单据的数量
     *
     * @param blockNum 曲块/曲胚数量
     */
    void addOrderNum(@Param("id") Integer id, @Param("blockNum") Integer blockNum);

    /**
     * 搬运记录减少出入库单据的数量
     *
     * @param id       id
     * @param decNum   减少数量
     */
    void decOrderNum(@Param("id") Integer id, @Param("decNum") Integer decNum);

    /**
     * 查询出入库单据多对多数据
     * @param queryDTO
     * @return
     */
    List<StorageOrderListDTO> orderList(StorageOrderListQueryDTO queryDTO);
}




