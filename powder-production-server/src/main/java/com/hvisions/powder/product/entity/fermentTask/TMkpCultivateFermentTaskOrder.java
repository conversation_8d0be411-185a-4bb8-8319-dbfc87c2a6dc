package com.hvisions.powder.product.entity.fermentTask;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 发酵任务工单关联
 * @TableName t_mkp_cultivate_ferment_task_order
 */
@Table(name ="t_mkp_cultivate_ferment_task_order")
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
public class TMkpCultivateFermentTaskOrder extends SysBase implements Serializable {
    /**
     * 发酵类型;0-醅菌发酵、1-转化发酵
     */
    private String type;

    /**
     * 压曲工单id
     */
    private Integer orderId;

    /**
     * 醅菌发酵任务id
     */
    private Integer taskId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}