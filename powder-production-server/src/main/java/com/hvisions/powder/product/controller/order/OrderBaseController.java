package com.hvisions.powder.product.controller.order;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.powder.dto.product.order.flip.FlipOrderPageDTO;
import com.hvisions.powder.dto.product.order.ProductPageQueryDTO;
import com.hvisions.powder.dto.product.order.smash.SmashOrderDTO;
import com.hvisions.powder.dto.product.order.smash.SmashOrderPageDTO;
import com.hvisions.powder.operlog.annotation.Log;
import com.hvisions.powder.operlog.enums.BusinessType;
import com.hvisions.powder.product.config.ProductOrderContext;
import com.hvisions.powder.product.service.order.ProductOrderService;
import com.hvisions.powder.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @描述: 公共工单控制器
 * @作者: 刘文勇
 * @日期: 2024/3/4 15:00
 * @版本 1.0
 */
@RestController
@RequestMapping("/order/base")
@Api(tags = "公共工单控制器")
@Slf4j
public class OrderBaseController {

    @ApiOperation(value = "分页查询生产工单")
    @RequestMapping(value = "/list/page/get",method = RequestMethod.POST)
    public Page getProductOrderPageList(@RequestBody ProductPageQueryDTO productPageQueryDTO, @RequestHeader(value = "type") String type){
        log.info("参数：{},类型：{}",productPageQueryDTO,type);
        return getOrderService(type).getOrderPageList(productPageQueryDTO);
    }

    @ApiOperation(value = "删除工单")
    @RequestMapping(value = "/delete/{id}",method = RequestMethod.GET)
    public Boolean deleteOrder(@PathVariable Integer id,@RequestHeader(value = "type") String type){
        log.info("参数：{},类型：{}",id,type);
        return getOrderService(type).deleteOrder(id);
    }

    @ApiOperation(value = "下发工单")
    @RequestMapping(value = "/distribute/{id}",method = RequestMethod.GET)
    @Log(title = "下发工单", businessType = BusinessType.EXECUTE)
    public Boolean distribute(@PathVariable Integer id,@RequestHeader(value = "type") String type){
        log.info("参数：{},类型：{}",id,type);
        return getOrderService(type).distribute(id);
    }

    @ApiOperation(value = "暂停工单")
    @RequestMapping(value = "/pause",method = RequestMethod.POST)
    @Log(title = "暂停工单", businessType = BusinessType.EXECUTE)
    public Boolean pauseSmashOrder(@PathVariable Integer id,@RequestHeader(value = "type") String type){
        return getOrderService(type).pauseOrder(id);
    }

    @ApiOperation(value = "结束工单")
    @RequestMapping(value = "/finish/{id}",method = RequestMethod.GET)
    @Log(title = "结束工单", businessType = BusinessType.EXECUTE)
    public Boolean finishOrder(@PathVariable Integer id,@RequestHeader(value = "type") String type){
        log.info("参数：{},类型：{}",id,type);
        return getOrderService(type).finishOrder(id);
    }

    @ApiOperation(value = "显示翻曲DTO")
    @RequestMapping(value = "/show/flip",method = RequestMethod.POST)
    public void showFlipOrder(@RequestBody FlipOrderPageDTO flipOrderPageDTO){

    }

    @ApiOperation(value = "显示粉碎DTO")
    @RequestMapping(value = "/show/smash",method = RequestMethod.POST)
    public void showFlipOrder(@RequestBody SmashOrderPageDTO smashOrderPageDTO){

    }

    /**
      * @描述: 根据type获取工单业务
      * @作者: 刘文勇
      * @日期: 2024/3/4 15:17
      * @参数: type
      * @返回值: com.hvisions.powder.product.service.order.ProductOrderService
    */
    public ProductOrderService getOrderService(String type){
        ProductOrderService productOrderService = ProductOrderContext.serviceMap.get(type);
        if(StringUtil.isEmpty(productOrderService)){
            throw new BaseKnownException(10000,"type异常");
        }
        return productOrderService;
    }

}
