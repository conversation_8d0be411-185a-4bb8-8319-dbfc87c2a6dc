package com.hvisions.powder.product.controller.order;

import com.hvisions.powder.dto.product.order.RebindPlanDTO;
import com.hvisions.powder.dto.product.order.extrusion.*;
import com.hvisions.powder.dto.product.order.muqu.input.*;
import com.hvisions.powder.product.service.order.ExtrusionOrderInputService;
import com.hvisions.powder.product.service.order.ExtrusionOrderItemService;
import com.hvisions.powder.product.service.order.ExtrusionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @描述: 压曲工单控制器
 * @作者: 刘文勇
 * @日期: 2024/2/20 16:43
 * @版本 1.0
 */
@RestController
@RequestMapping("/extrusion/order")
@Api(tags = "压曲工单控制器")
@Slf4j
public class ExtrusionOrderController {

    /**
     *压曲工单业务
     */
    @Resource
    private ExtrusionOrderService extrusionOrderService;

    /**
     * 压曲工单母曲投入业务
     */
    @Resource
    private ExtrusionOrderInputService extrusionOrderInputService;

    /**
     * 压曲工单人工补录业务
     */
    @Resource
    private ExtrusionOrderItemService extrusionOrderItemService;


    @ApiOperation(value = "新增压曲工单")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Boolean insertExtrusionOrder(@Valid @RequestBody ExtrusionOrderDTO extrusionOrderDTO) {
        return extrusionOrderService.insertExtrusionOrder(extrusionOrderDTO);
    }

    @ApiOperation(value = "编辑压曲工单")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Boolean updateExtrusionOrder(@Valid @RequestBody ExtrusionOrderDTO extrusionOrderDTO) {
        return extrusionOrderService.updateExtrusionOrder(extrusionOrderDTO);
    }

    @ApiOperation(value = "分页查询压曲工单母曲投入")
    @RequestMapping(value = "/input/list/page/get", method = RequestMethod.POST)
    public Page<ExtrusionOrderInputPageDTO> getExtrusionOrderInputPageList(@RequestBody ExtrusionOrderInputQueryDTO extrusionOrderInputQueryDTO) {
        return extrusionOrderInputService.getExtrusionOrderInputPageList(extrusionOrderInputQueryDTO);
    }

    @ApiOperation(value = "根据人员和操作时间统计母曲投入记录")
    @RequestMapping(value = "/getExtrusionOrderInputPersonSummary", method = RequestMethod.POST)
    public List<ExtrusionOrderInputPersonSummaryDTO> getExtrusionOrderInputPersonSummary(@RequestBody ExtrusionOrderInputQueryDTO queryDTO){
        return extrusionOrderInputService.getExtrusionOrderInputPersonSummary(queryDTO);
    }

    @ApiOperation(value = "统计当月母曲投入汇总量")
    @RequestMapping(value = "/getExtrusionOrderInputByMonth", method = RequestMethod.POST)
    public List<ExtrusionOrderInputByMonthDTO> getExtrusionOrderInputByMonth(){
        return extrusionOrderInputService.getExtrusionOrderInputByMonth();
    }

    @ApiOperation(value = "新增压曲工单母曲投入")
    @RequestMapping(value = "/input/add", method = RequestMethod.POST)
    public Boolean insertExtrusionOrderInput(@Valid @RequestBody MuQuInputDTO extrusionOrderInputDTO) {
        return extrusionOrderInputService.insertExtrusionOrderInput(extrusionOrderInputDTO);
    }

    @ApiOperation(value = "母曲投入回退")
    @RequestMapping(value = "/returnMuQuInput", method = RequestMethod.POST)
    public Integer returnMuQuInput(@RequestBody List<Integer> muQuInputIds) {
        return extrusionOrderInputService.returnMuQuInput(muQuInputIds);
    }

    @ApiOperation(value = "新增压曲工单母曲投入")
    @RequestMapping(value = "/shareWheatInput", method = RequestMethod.POST)
    public Integer shareWheatInput(@RequestBody ShareWheatInputDTO shareWheatInputDTO) {
        return extrusionOrderService.shareWheatInput(shareWheatInputDTO);
    }


    @ApiOperation(value = "修改压曲工单母曲投入")
    @RequestMapping(value = "/input/update", method = RequestMethod.POST)
    public Boolean updateExtrusionOrderInput(@Valid @RequestBody ExtrusionOrderInputDTO extrusionOrderInputDTO) {
        return extrusionOrderInputService.updateExtrusionOrderInput(extrusionOrderInputDTO);
    }

    @ApiOperation(value = "删除压曲工单母曲投入")
    @RequestMapping(value = "/input/delete/{id}", method = RequestMethod.GET)
    public Boolean deleteExtrusionOrderInput(@PathVariable Integer id) {
        return extrusionOrderInputService.deleteExtrusionOrderInput(id);
    }

    @ApiOperation(value = "新增压曲工单人工补录")
    @RequestMapping(value = "/supplement/add", method = RequestMethod.POST)
    public Boolean insertExtrusionOrderSupplement(@Valid @RequestBody ExtrusionOrderSupplementDTO extrusionOrderSupplementDTO) {
        return extrusionOrderItemService.insertExtrusionOrderSupplement(extrusionOrderSupplementDTO);
    }

    @ApiOperation(value = "重新绑定计划")
    @RequestMapping(value = "/rebindPlan", method = RequestMethod.POST)
    public Integer rebindPlan(@Valid @RequestBody RebindPlanDTO rebindPlanDTO) {
        return extrusionOrderService.rebindPlan(rebindPlanDTO);
    }

    @ApiOperation(value = "根据计划查询工单信息-扎帐使用")
    @RequestMapping(value = "/getOrderByPlan", method = RequestMethod.GET)
    public ExtrusionOrderByPlanDTO getOrderByPlan(@RequestParam Integer planid) {
        return extrusionOrderService.getOrderByPlan(planid);
    }

    @ApiOperation(value = "修改压曲工单小麦投入")
    @RequestMapping(value = "/updateWheatInput", method = RequestMethod.POST)
    public Integer updateWheatInput(@RequestBody UpdateWheatInputDTO updateWheatInputDTO) {
        return extrusionOrderService.updateWheatInput(updateWheatInputDTO);
    }
}
