package com.hvisions.powder.product.service.order;

import com.hvisions.powder.dto.product.craft.craftInspectionPlanProject.CraftInspectionPlanProjectDTO;
import com.hvisions.powder.product.entity.craft.TMkpCraftInspectionPlanProject;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_mkp_craft_inspection_plan_project(制曲工艺巡检计划项目关联)】的数据库操作Service
* @createDate 2024-02-26 14:45:28
*/
public interface CraftInspectionPlanProjectService extends IService<TMkpCraftInspectionPlanProject> {

    /**
      * @描述: 批量插入巡检计划详情
      * @作者: 刘文勇
      * @日期: 2024/2/27 9:30
      * @参数: craftInspectionPlanProjectDTOList
      * @参数: pid
      * @返回值: void
    */
    int batchInsert(List<CraftInspectionPlanProjectDTO> craftInspectionPlanProjectDTOList, Integer pid);
}
