package com.hvisions.powder.product.entity.craft;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 制曲工艺违章级别
 * @TableName t_mkp_craft_violate_level
 */
@Table(name ="t_mkp_craft_violate_level")
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
public class TMkpCraftViolateLevel extends SysBase implements Serializable {
    /**
     * 违章级别编号
     */
    private String violateLevelCode;

    /**
     * 违章级别名称
     */
    private String violateLevelName;

    /**
     * 违章说明
     */
    private String notes;

    /**
     * 扣除分数
     */
    private Integer reduceScore;

    /**
     * 是否开具违章通知单
     */
    private String isTicket;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}