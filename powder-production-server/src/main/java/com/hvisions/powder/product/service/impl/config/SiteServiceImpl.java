package com.hvisions.powder.product.service.impl.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.dto.product.config.site.SitePageDTO;
import com.hvisions.powder.dto.product.config.site.SitePageQueryDTO;
import com.hvisions.powder.dto.product.config.site.SiteUpdateDTO;
import com.hvisions.powder.product.entity.config.TMkpSite;
import com.hvisions.powder.product.mapper.config.SiteMapper;
import com.hvisions.powder.product.service.config.SiteService;
import com.hvisions.powder.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description:站点管理
 * @date 2024/01/19 10:18
 */
@Service
@Slf4j
public class SiteServiceImpl implements SiteService {

    @Resource
    private SiteMapper siteMapper;

    @Override
    public Page<SitePageDTO> getSitePageList(SitePageQueryDTO queryDTO) {
        LambdaQueryWrapper<TMkpSite> wrapper = Wrappers.<TMkpSite>lambdaQuery()
                .like(StringUtil.isNotEmpty(queryDTO.getCode()), TMkpSite::getCode, queryDTO.getCode())
                .between(StringUtil.isNotEmpty(queryDTO.getStartTime()) && StringUtil.isNotEmpty(queryDTO.getEndTime()), TMkpSite::getCreateTime, queryDTO.getStartTime(), queryDTO.getEndTime())
                .eq(TMkpSite::getDeleted, 0)
                .orderByDesc(TMkpSite::getUpdateTime);
        return PageHelperUtil.getPage(siteMapper::selectPage, queryDTO, wrapper, SitePageDTO.class);
    }

    @Override
    public Integer addOrUpdateSite(SiteUpdateDTO siteUpdateDTO) {
        TMkpSite site = DtoMapper.convert(siteUpdateDTO, TMkpSite.class);
        if (StringUtil.isEmpty(site.getId())) {
            return siteMapper.insert(site);
        } else {
            return siteMapper.updateById(site);
        }
    }

    @Override
    public Integer deleteSite(Integer id) {
        return siteMapper.deleteById(id);
    }


}
