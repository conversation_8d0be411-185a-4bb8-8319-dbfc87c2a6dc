package com.hvisions.powder.product.service.process;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.powder.dto.product.process.equipmentException.EquipmentExceptionDTO;
import com.hvisions.powder.dto.product.process.equipmentException.EquipmentExceptionPageQueryDTO;
import com.hvisions.powder.product.entity.process.TMkpEquipmentException;
import org.springframework.data.domain.Page;

public interface EquipmentExceptionService extends IService<TMkpEquipmentException> {

    Page getEquipmentExceptionPageList(EquipmentExceptionPageQueryDTO equipmentExceptionPageQueryDTO);

    Boolean updateEquipmentException(EquipmentExceptionDTO equipmentExceptionDTO);

    Boolean deleteEquipmentException(Integer id);

    Boolean insertEquipmentException(EquipmentExceptionDTO equipmentExceptionDTO);

    Integer saveEquipmentException(String code, String exceptionContent, String source);
}
