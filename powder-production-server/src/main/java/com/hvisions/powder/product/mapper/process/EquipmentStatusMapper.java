package com.hvisions.powder.product.mapper.process;

import com.hvisions.powder.dto.product.QueryDTO;
import com.hvisions.powder.dto.product.process.equipmentStatus.EquipmentStatusPageDTO;
import com.hvisions.powder.dto.product.process.equipmentStatus.EquipmentStatusPageQueryDTO;
import com.hvisions.powder.product.entity.process.TMkpEquipmentStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_mkp_equipment_status(设备状态管理)】的数据库操作Mapper
* @createDate 2024-03-01 16:32:55
* @Entity com.hvisions.powder.product.entity.process.TMkpEquipmentStatus
*/
@Mapper
public interface EquipmentStatusMapper extends BaseMapper<TMkpEquipmentStatus> {

    List<EquipmentStatusPageDTO> getEquipmentStatusList(QueryDTO queryDTO);
}




