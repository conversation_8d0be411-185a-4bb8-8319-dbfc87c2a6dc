package com.hvisions.powder.product.service.process;

import com.hvisions.powder.dto.product.process.equipmentStatus.EquipmentStatusDTO;
import com.hvisions.powder.dto.product.process.equipmentStatus.EquipmentStatusPageQueryDTO;
import com.hvisions.powder.product.entity.process.TMkpEquipmentStatus;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Page;

/**
* <AUTHOR>
* @description 针对表【t_mkp_equipment_status(设备状态管理)】的数据库操作Service
* @createDate 2024-03-01 16:32:55
*/
public interface EquipmentStatusService extends IService<TMkpEquipmentStatus> {

    Page getEquipmentStatusPageList(EquipmentStatusPageQueryDTO equipmentStatusPageQueryDTO);

    Boolean updateEquipmentStatus(EquipmentStatusDTO equipmentStatusDTO);

    Boolean deleteEquipmentStatus(Integer id);

    Boolean insertEquipmentStatus(EquipmentStatusDTO equipmentStatusDTO);

    Integer saveEquipmentStatus(String code, String type, String exceptionContent);
}
