package com.hvisions.powder.product.controller.exception;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.powder.dto.product.exception.*;
import com.hvisions.powder.product.entity.exception.TMkpSystemJoinLog;
import com.hvisions.powder.product.service.exception.ExceptionLevelService;
import com.hvisions.powder.product.service.exception.ExceptionManageService;
import com.hvisions.powder.product.service.exception.SystemJoinLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/systemJoinLog")
@Api(tags = "系统连接日志")
@Slf4j
public class SystemJoinLogController {

    /**
     * 异常级别业务
     */
    @Resource
    private SystemJoinLogService systemJoinLogService;


    @ApiOperation(value = "查询系统连接日志")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<TMkpSystemJoinLog> getPageList(@RequestBody SystemJoinLogQueryDTO pageInfo) {
        return systemJoinLogService.getPageList(pageInfo);
    }

    @ApiOperation(value = "系统连接校验")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public List<TMkpSystemJoinLog> check() {
        return systemJoinLogService.check();
    }

    @ApiOperation(value = "单个校验")
    @RequestMapping(value = "/checkByType", method = RequestMethod.POST)
    public TMkpSystemJoinLog checkByType(@RequestParam("type") String type) {
        return systemJoinLogService.checkByType(type);
    }

}
