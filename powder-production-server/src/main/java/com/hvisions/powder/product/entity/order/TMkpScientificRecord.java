package com.hvisions.powder.product.entity.order;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科研制曲记录
 * @TableName t_mkp_scientific_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mkp_scientific_record")
public class TMkpScientificRecord extends SysBase implements Serializable {

    // 曲房id
    private Integer storeroomId;

    // 楼层id
    private Integer floorId;

    // 产线id
    private String lineIds;

    // 物料id
    private Integer materialId;

    // 曲房号
    private String storeroomNo;

    // 楼层
    private String floor;

    // 制曲时间
    private Date recordTime;

    // 小麦重量
    private BigDecimal wheatQuantity;

    // 小麦原料水分
    private BigDecimal wheatWaterContent;

    // 母曲占比
    private BigDecimal primerProportion;

    // 小麦原料检测
    private String wheatInspect;

    // 母曲名称
    private String primerName;

    // 母曲添加量
    private BigDecimal primerQuantity;

    // 母曲检测
    private String primerInspect;

    // 母曲粉碎度
    private String primerSmash;

    // 润麦时间
    private String runmaiTime;

    // 小麦粉碎度
    private String wheatSmash;

    // 加水量
    private BigDecimal waterQuantity;

    // 压曲机产线
    private String extrusionLine;

    // 鲜坯重量
    private BigDecimal extrusionQuantity;

    // 曲块数
    private Integer blockNum;

    // 补水量
    private String addWaterQuantity;

    // 晾干时间
    private String dryingTime;

    // 备注
    private String remark;
}