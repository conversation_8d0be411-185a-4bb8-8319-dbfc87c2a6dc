package com.hvisions.powder.product.mapper.order;

import com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputByMonthDTO;
import com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputPageDTO;
import com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputPersonSummaryDTO;
import com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputQueryDTO;
import com.hvisions.powder.product.entity.order.TMkpExtrusionOrderInput;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_mkp_extrusion_order_input(压曲工单母曲投入记录)】的数据库操作Mapper
* @createDate 2024-03-19 11:56:28
* @Entity com.hvisions.powder.product.entity.order.TMkpExtrusionOrderInput
*/
@Mapper
public interface ExtrusionOrderInputMapper extends BaseMapper<TMkpExtrusionOrderInput> {

    /**
     * @Description 获取母曲投入列表
     *
     * <AUTHOR>
     * @Date 2024-5-15 14:57
     * @param extrusionOrderInputQueryDTO
     * @return java.util.List<com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputPageDTO>
     **/
    List<ExtrusionOrderInputPageDTO> getExtrusionOrderInputList(ExtrusionOrderInputQueryDTO extrusionOrderInputQueryDTO);


    /**
     * @Description 根据人员和操作时间统计母曲投入记录
     *
     * <AUTHOR>
     * @Date 2024-7-24 16:01
     * @param queryDTO
     * @return java.util.List<com.hvisions.powder.dto.product.order.muqu.input.ExtrusionOrderInputPersonSummaryDTO>
     **/
    List<ExtrusionOrderInputPersonSummaryDTO> getExtrusionOrderInputPersonSummary(ExtrusionOrderInputQueryDTO queryDTO);

    /**
     * 统计当月母曲投入汇总量
     * @param start
     * @param end
     * @return
     */
    List<ExtrusionOrderInputByMonthDTO> selectExtrusionOrderInputByMonth(@Param("start") Date start, @Param("end")Date end);
}




