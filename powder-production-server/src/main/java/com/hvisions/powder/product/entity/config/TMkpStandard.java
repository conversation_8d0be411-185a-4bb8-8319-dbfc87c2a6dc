package com.hvisions.powder.product.entity.config;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 标准量配置
 * @date 2024/1/18 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mkp_standard")
public class TMkpStandard extends SysBase {

    /** 标准编号 */
    private String standardNo;

    /** 物料id */
    private Integer materialId;

    /** 物料编号 */
    private String materialCode;

    /** 物料名称 */
    private String materialName;

    /** 标准量 */
    private BigDecimal standardMeasure;

    /** 单位 */
    private String measureUnit;

    /** 备注 */
    private String remark;

    // 状态：0-失效、1-生效
    private String state;

    // 失效日期
    private Date expiringDate;
}
