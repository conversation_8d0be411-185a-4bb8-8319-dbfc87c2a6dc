package com.hvisions.powder.product.entity.craft;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "t_mkp_crew")
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
public class TMkpCrew extends SysBase implements Serializable {

    /**
     * 班组名称
     */
    private String crewName;

    /**
     * 班组编码
     */
    private String crewCode;

    /**
     * 班组状态
     */
    private String state;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 楼层编码
     */
    private String floorCode;

    /**
     * 楼层id
     */
    private Integer floorId;

    // 组长id
    private Integer leaderId;

    // 组长名称
    private String leaderName;
}
