package com.hvisions.powder.product.mapper.order;

import com.hvisions.powder.product.entity.order.TMkpExtrusionOrderItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_mkp_extrusion_order_item(压曲工单子项)】的数据库操作Mapper
* @createDate 2024-03-19 11:56:28
* @Entity com.hvisions.powder.product.entity.order.TMkpExtrusionOrderItem
*/
@Mapper
public interface ExtrusionOrderItemMapper extends BaseMapper<TMkpExtrusionOrderItem> {

    int batchInsert(@Param("list") List extrusionOrderItemList);

    void addExtrusionNum(@Param("lineCode") String floorCode, @Param("orderId") Integer orderId, @Param("num") Integer num);

    void decExtrusionNum(@Param("lineCode") String floorCode, @Param("orderId") Integer orderId, @Param("num") Integer num);
}




