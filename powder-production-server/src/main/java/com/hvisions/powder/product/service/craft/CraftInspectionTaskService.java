package com.hvisions.powder.product.service.craft;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.dto.product.craft.craftInspectionTask.CraftInspectionTaskDTO;
import com.hvisions.powder.dto.product.craft.craftInspectionTask.CraftInspectionTaskPageDTO;
import com.hvisions.powder.dto.product.craft.craftInspectionTask.CraftInspectionTaskPageQueryDTO;
import com.hvisions.powder.product.entity.craft.TMkpCraftInspectionTask;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Page;

import java.io.IOException;

/**
* <AUTHOR>
* @description 针对表【t_mkp_craft_inspection_task(制曲工艺巡检任务)】的数据库操作Service
* @createDate 2024-02-27 14:24:47
*/
public interface CraftInspectionTaskService extends IService<TMkpCraftInspectionTask> {

    /**
      * @描述: 根据巡检计划id新增巡检任务
      * @作者: 刘文勇
      * @日期: 2024/2/28 10:25
      * @参数: planId -巡检计划id
      * @返回值: java.lang.Boolean
    */
    Boolean insertCraftInspectionTask(Integer planId);

    Page<CraftInspectionTaskPageDTO> getCraftInspectionTaskPageList(CraftInspectionTaskPageQueryDTO craftInspectionTaskPageQueryDTO);

    Boolean submitCraftInspectionTask(CraftInspectionTaskDTO craftInspectionTaskDTO);

    /**
     * 巡检任务导出
     * @param queryDTO
     * @return
     */
    ResultVO<ExcelExportDto> exportList(CraftInspectionTaskPageQueryDTO queryDTO) throws IOException, IllegalAccessException;
}
