# suppress inspection "UnusedProperty" for whole file
# éç¨å¼å¸¸ä¿¡æ¯
SUCCESS=SUCCESS
MERGE_FILE_FAIL=MERGE_FILE_FAIL
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
#èªå®ä¹å¼å¸¸ä¿¡æ¯
DELETE_FILE_FAIL=DELETE_FILE_FAIL
FILE_TYPE_USED=FILE_TYPE_USED
SAVE_FILE_FAILED=SAVE_FILE_FAILED
DOWNLOAD_FILE_FAILED=DOWNLOAD_FILE_FAILED
FILE_TYPE_NOR_SUPPORT=FILE_TYPE_NOR_SUPPORT
# å½åæ è®°ä¸å­å¨
CURRENT_TAG_DOES_NOT_EXIST=CURRENT_TAG_DOES_NOT_EXIST
# ç±»åæ¯è¢«ç¦æ­¢çå³é®å­
TYPE_IS_A_PROHIBITED_KEYWORD=TYPE_IS_A_PROHIBITED_KEYWORD
# ä¸æ¯æè¯¥ç±»åæ°æ®åºæ¥è¯¢
NOT_CURRENT_DATABASES_SUPPORTED=NOT_CURRENT_DATABASES_SUPPORTED
