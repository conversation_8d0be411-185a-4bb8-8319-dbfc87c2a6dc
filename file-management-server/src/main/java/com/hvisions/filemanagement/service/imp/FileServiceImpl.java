package com.hvisions.filemanagement.service.imp;

import com.hvisions.common.consts.CookieConst;
import com.hvisions.common.consts.RedisConst;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dao.FileInfoMapper;
import com.hvisions.filemanagement.dto.FileDTO;
import com.hvisions.filemanagement.dto.FileInfoQueryDTO;
import com.hvisions.filemanagement.dto.FileTagDTO;
import com.hvisions.filemanagement.dto.TagDTO;
import com.hvisions.filemanagement.entity.HvBmFile;
import com.hvisions.filemanagement.entity.HvBmFileTag;
import com.hvisions.filemanagement.entity.HvBmFileType;
import com.hvisions.filemanagement.enums.FileExceptionEnum;
import com.hvisions.filemanagement.repository.FileRepository;
import com.hvisions.filemanagement.repository.FileTypeRepository;
import com.hvisions.filemanagement.service.FileService;
import com.hvisions.filemanagement.service.FileTagService;
import com.hvisions.filemanagement.service.TagService;
import com.hvisions.filemanagement.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: FileServiceImp</p>
 * <p>Description: 文件服务实现类</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/11</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
@ConditionalOnExpression(value = "${h-visions.file.compress.enable}==false && ${h-visions.file.watermark.enable}==false")
public class FileServiceImpl implements FileService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired
    FileRepository fileRepository;
    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired
    FileTypeRepository fileTypeRepository;
    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Value("${h-visions.file.path:''}")
    protected String path;
    @Value("${h-visions.file.support}")
    private String support;

    /**
     * 获取文件下载路径,如果文件路径没有设置。返回运行时的路径
     * @return 文件包路径
     */
    protected String getPath() {
        if (Strings.isEmpty(path)) {
            return System.getProperty("user.dir")+"/files";
        } else {
            return path;
        }
    }

    private static final String SPLITTER = ",";

    /**
     * 文件标签关系
     */
    FileTagService fileTagService;
    /**
     * 标签
     */
    TagService tagService;
    /**
     * 文件 标签查询
     */
    @Autowired
    FileInfoMapper fileInfoMapper;

    @Autowired
    public FileServiceImpl(FileInfoMapper fileInfoMapper,
                           FileTagService fileTagService,
                           TagService tagService) {
        this.fileTagService = fileTagService;
        this.tagService = tagService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileDTO uploadFile(String name, MultipartFile file, Integer fileTypeId, HttpServletRequest request) {
        HvBmFile hvBmFile = new HvBmFile();
        //获取，验证文件类型是否存在,不存在设置为0
        Optional<HvBmFileType> optional = fileTypeRepository.findById(fileTypeId);
        HvBmFileType hvBmFileType = null;
        if (optional.isPresent()) {
            hvBmFileType = optional.get();
            hvBmFile.setFileTypeId(fileTypeId);
        } else {
            hvBmFile.setFileTypeId(0);
        }
        //获取文件的扩展名
        String fileExtend = FileUtils.getFileExtend(Objects.requireNonNull(file.getOriginalFilename()));
        validExtend(fileExtend);
        //获取文件原名称
        String fileOriginName = FileUtils.getFileNameWithNotExtend(file.getOriginalFilename());
        //根据配置的文件夹+文件类型编码+类型名+年月日为保存的目录
        String directory = String.format("%s/%s", getPath(), hvBmFileType == null ? "default" : hvBmFileType.getFileTypeCode());
        //生成保存的文件名，传入的文件名+时间戳
        String fileStorePath = String.format("%s/%s-%s",
                directory,
                fileOriginName,
                Calendar.getInstance().getTimeInMillis() / 1000);
        //添加后缀名
        if (!"".equals(fileExtend)) {
            fileStorePath = fileStorePath + "." + fileExtend;
        }
        //保存文件 并返回修改后的文件
        saveFile(file, fileStorePath);
        //设置基础信息
        hvBmFile.setCreateTime(new Date());
        hvBmFile.setCreatorId(getUserIdByRequest(request));
        hvBmFile.setFileExtend(fileExtend);
        //保存实际文件大小
        hvBmFile.setFileSize(file.getSize());
        hvBmFile.setCreatorName(name);
        hvBmFile.setFileName(fileOriginName);
        hvBmFile.setFilePath(fileStorePath);
        //保存文件信息到数据库
        return DtoMapper.convert(fileRepository.save(hvBmFile), FileDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FileDTO uploadFile(String name, MultipartFile file, String fileTypeName, HttpServletRequest request, List<String> tagNames) {

        //如果为空 或 空串赋予默认default
        if (fileTypeName == null || "".equals(fileTypeName.trim())) {
            log.info("fileTypeName is default:" + fileTypeName);
            fileTypeName = "default";
        }
        //通过文件类型名称查询文件类型
        HvBmFileType fileType = fileTypeRepository.findByFileTypeName(fileTypeName);
        FileDTO fileDTO = null;


        //当文件类型不存在的情况下
        if (fileType == null) {
            //生成一个新的文件类型
            HvBmFileType fileType1 = new HvBmFileType();
            fileType1.setFileTypeCode(fileTypeName);
            fileType1.setFileTypeName(fileTypeName);
            fileType1.setCreateTime(new Date());
            fileType1.setUpdateTime(new Date());
            HvBmFileType hvBmFileType = fileTypeRepository.save(fileType1);
            fileDTO = uploadFile(name, file, hvBmFileType.getId(), request);
        } else {
            fileDTO = uploadFile(name, file, fileType.getId(), request);
        }
        if (fileDTO != null && tagNames != null) {
            //添加标签
            fileTagService.addFileTag(new FileTagDTO(fileDTO.getId(), tagNames));
            //标签去重并
            fileDTO.setTagName(tagNames.stream().distinct().collect(Collectors.toList()));

        }
        return fileDTO;
    }


    protected int getUserIdByRequest(HttpServletRequest request) {
        String token = request.getHeader(CookieConst.AUTH_TOKEN);
        //如果token不是空，根据sessionId获取redis中的用户id,如果前端没有传递用户id，则添加
        if (token != null) {
            String userIdStr = stringRedisTemplate.opsForValue()
                    .get(String.format(RedisConst.AUTH_REDIS_PREFIX, token));
            if (userIdStr != null) {
                return Integer.parseInt(userIdStr);
            }
        }
        return 0;
    }

    /**
     * 验证扩展名是否支持 ，如果不支持，报错
     *
     * @param fileExtend 文件扩展名
     */
    protected void validExtend(String fileExtend) {
        if (Arrays.stream(support.split(SPLITTER)).noneMatch(t -> t.toUpperCase().equals(fileExtend.toUpperCase()))) {
            throw new BaseKnownException(FileExceptionEnum.FILE_TYPE_NOR_SUPPORT);
        }
    }

    /**
     * 保存文件
     *
     * @param file     文件
     * @param filePath 保存路径
     */
    private void saveFile(MultipartFile file, String filePath) {

        File desFile = new File(filePath);

        if (!desFile.getParentFile().exists()) {
            desFile.getParentFile().mkdirs();
        }
        try {
            file.transferTo(desFile);
        } catch (IOException e) {
            throw new BaseKnownException(FileExceptionEnum.SAVE_FILE_FAILED);
        }

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void downloadFile(int fileId, HttpServletResponse response) {
        HvBmFile hvBmFile = fileRepository.getOne(fileId);
        //获取应该返回的文件名
        String fileName = hvBmFile.getFileName();
        if (!StringUtils.isEmpty(hvBmFile.getFileExtend())) {
            fileName = fileName + "." + hvBmFile.getFileExtend();
        }
        response.setContentType("multipart/form-data");
        response.addHeader("Content-Length", "" + hvBmFile.getFileSize());
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    new String(fileName.getBytes("gb2312"), "ISO8859-1"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        byte[] buff = new byte[1024];
        BufferedInputStream bis = null;
        OutputStream os;
        try {
            os = response.getOutputStream();
            bis = new BufferedInputStream(new FileInputStream(hvBmFile.getFilePath()));
            int i = bis.read(buff);
            while (i != -1) {
                os.write(buff, 0, buff.length);
                os.flush();
                i = bis.read(buff);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new BaseKnownException(FileExceptionEnum.DOWNLOAD_FILE_FAILED);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 修改文件信息
     *
     * @param fileDTO 文件
     * @return id
     */
    @Override
    public Integer updateFile(FileDTO fileDTO) {
        //删除原有的全部关系
        fileTagService.deleteByFileId(fileDTO.getId());
        //创建新的
        FileTagDTO fileTagDTO = new FileTagDTO();
        fileTagDTO.setFileId(fileDTO.getId());
        fileTagDTO.setTagNames(fileDTO.getTagName());
        fileTagService.addFileTag(fileTagDTO);
        return fileDTO.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<FileDTO> getFilePageInfo(FileInfoQueryDTO fileInfoQueryDTO) {
        //文件查询 支持标签查询
        if (Strings.isNotBlank(fileInfoQueryDTO.getTagName())) {
            fileInfoQueryDTO.setTagName("%" + fileInfoQueryDTO.getTagName() + "%");
        }
        if (Strings.isNotBlank(fileInfoQueryDTO.getFileName())) {
            fileInfoQueryDTO.setFileName("%" + fileInfoQueryDTO.getFileName() + "%");
        }
        Page<FileDTO> page = PageHelperUtil.getPage(this.fileInfoMapper::getFile, fileInfoQueryDTO, FileDTO.class);
        //添加标签信息
        for (FileDTO fileDTO : page) {
            List<String> tagByFileId = fileTagService.getTagByFileId(fileDTO.getId());
            fileDTO.setTagName(tagByFileId);
            Optional<HvBmFileType> optional = fileTypeRepository.findById(fileDTO.getFileTypeId());
            if (optional.isPresent()) {
                fileDTO.setFileTypeName(optional.get().getFileTypeName());
            } else {
                if (fileDTO.getFileTypeId() == 0) {
                    fileDTO.setFileTypeName("default");
                } else {
                    fileDTO.setFileTypeName("---");
                }

            }
        }
        return page;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FileDTO getFileDetail(int fileId) {
        FileDTO fileDTO = DtoMapper.convert(fileRepository.getOne(fileId), FileDTO.class);
        if (fileDTO.getFileTypeId() != null && fileDTO.getFileTypeId() > 0) {
            HvBmFileType hvBmFileType = fileTypeRepository.getOne(fileDTO.getFileTypeId());
            fileDTO.setFileTypeName(hvBmFileType.getFileTypeName());
        }
        //添加文件标签
        fileDTO.setTagName(fileTagService.getTagByFileId(fileId));
        return fileDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(int fileId) {
        Optional<HvBmFile> hvBmFile = fileRepository.findById(fileId);
        if (hvBmFile.isPresent()) {
            fileRepository.deleteById(fileId);
            File file = new File(hvBmFile.get().getFilePath());
            //如果删除失败，报错
            boolean result = file.delete();
            if (!result) {
                throw new BaseKnownException(FileExceptionEnum.DELETE_FILE_FAIL);
            }
            fileTagService.deleteByFileId(fileId);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<FileDTO> getFileDetailBatch(List<Integer> fileIds) {
        List<HvBmFile> files = fileRepository.findAllById(fileIds);
        List<FileDTO> fileDTOS = DtoMapper.convertList(files, FileDTO.class);
        //添加文件标签
        return fileDTOS.stream().peek(t -> t.setTagName(fileTagService.getTagByFileId(t.getId()))).collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<ExcelExportDto> downloadFileWithResult(int fileId) {

        ExcelExportDto excelExportDto = new ExcelExportDto();
        HvBmFile hvBmFile = fileRepository.getOne(fileId);
        //获取应该返回的文件名
        String fileName = hvBmFile.getFileName();
        if (!StringUtils.isEmpty(hvBmFile.getFileExtend())) {
            fileName = fileName + "." + hvBmFile.getFileExtend();
        }
        byte[] buff = new byte[hvBmFile.getFileSize().intValue()];
        excelExportDto.setFileName(fileName);
        FileInputStream bis = null;

        try {
            //循环获取所有的文件内容
            bis = new FileInputStream(hvBmFile.getFilePath());
            bis.read(buff);
            excelExportDto.setBody(buff);
            return ResultVO.success(excelExportDto);
        } catch (IOException e) {
            throw new BaseKnownException(FileExceptionEnum.DOWNLOAD_FILE_FAILED);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 通过标签获取对应文件列表
     *
     * @param tagName 标签名称
     * @return 文件列表
     */
    @Override
    public List<FileDTO> getFileByTagName(String tagName) {
        List<FileDTO> fileDTOS = new ArrayList<>();
        TagDTO tagDTO = tagService.getTagByTagName(tagName);
        if (tagDTO != null) {
            List<HvBmFileTag> fileTags = fileTagService.findByTagId(tagDTO.getId());
            for (HvBmFileTag fileTag : fileTags) {
                fileDTOS.add(getFileDetail(fileTag.getFileId()));
            }
        }
        return fileDTOS;
    }

}

