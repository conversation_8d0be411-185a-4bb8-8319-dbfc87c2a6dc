package com.hvisions.filemanagement.service;

import com.hvisions.filemanagement.dto.TagDTO;

import java.util.List;

/**
 * <p>Title:TagService</p>
 * <p>Description:标签</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/12</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface TagService {

    /**
     * 增加标签
     *
     * @param tagName 标签
     * @return 标签id
     */
    List<Integer> addTag(List<String> tagName);

    /**
     * 获取全部标签
     *
     * @return 标签
     */
    List<TagDTO> getTag();

    /**
     * 删除标签
     *
     * @param tagName 标签名称
     */
    void deleteByTag(String tagName);

    /**
     * 通过标签id获取标签
     *
     * @param tagId 标签id
     * @return 标签
     */
    TagDTO getByTagId(Integer tagId);

    /**
     * 通过tag名称或去tag
     *
     * @param tagName tag名称
     * @return tag
     */
    TagDTO getTagByTagName(String tagName);

    /**
     * 修改标签
     *
     * @param tagDTO 标签
     * @return id
     */
    Integer updateTag(TagDTO tagDTO);
    /**
     * 通过标签id删除标签
     *
     * @param tagId 标签id
     */
    void deleteByTagId(Integer tagId);
}
