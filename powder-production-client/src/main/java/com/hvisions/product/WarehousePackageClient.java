package com.hvisions.product;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.product.req.SendPackageReqDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@FeignClient(name = "powder-production-server", path = "/warehousePackage", fallbackFactory = WarehousePackageClientFallBack.class)
public interface WarehousePackageClient {

    //  累加发放数量
    @RequestMapping(value = "/addSendNum", method = RequestMethod.POST)
    ResultVO<Integer> addSendNum(@RequestBody SendPackageReqDto sendPackageReqDto);

}
